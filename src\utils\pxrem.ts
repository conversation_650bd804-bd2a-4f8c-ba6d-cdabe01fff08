const baseSize = 16;

function setRem() {
  console.log("setRem");
  const scale = document.documentElement.clientWidth / 1920;
  // const fontSize =
  //   baseSize * Math.min(scale, 2) > 12 ? baseSize * Math.min(scale, 2) : 12;
  const fontSize = baseSize * Math.min(scale, 2);
  document.documentElement.style.fontSize = fontSize + "px";
  // 设置fontSize 后就可以使用rem实现适配 Demo
  // if (document.documentElement.clientWidth === 3840) {
  //   document.documentElement.style.fontSize = 'xxxpx'
  // }
}
setRem();
// 改变窗口大小时重新设置 rem
window.addEventListener("resize", () => {
  setRem();
}); // 避免封装的多处组件使用window.onresize导致不起效果，使用事件监听

// window.onresize = function () {
//   setRem();
// };
