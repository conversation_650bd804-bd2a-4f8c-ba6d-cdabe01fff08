<template>
  <div class="ent-combo-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate" v-show="enableAdd">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增套餐
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="套餐名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="图片" align="center" min-width="100">
          <template #default="scope">
            <div class="cover-name">
              <img :src="scope.row.thumb" alt="" class="cover-img" />
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="套餐容量" align="center" min-width="40">
          <template #default="scope"> {{ scope.row.storage }}G </template>
        </el-table-column> -->
        <!-- <el-table-column label="价格（￥）" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.price || "--" }}
          </template>
        </el-table-column> -->
        <el-table-column
          label="描述"
          align="center"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.description }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="账号数" align="center" min-width="40">
          <template #default="scope"> {{ scope.row.accounts }}个 </template>
        </el-table-column>
        <el-table-column label="更换账号次数" align="center" min-width="60">
          <template #default="scope"> {{ scope.row.unbind_acc }}次 </template>
        </el-table-column> -->

        <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="40"
        >
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status].type">{{
              statusMap[scope.row.status].label
            }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="100">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
                v-show="enableDelete"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!--弹窗 -->
    <!-- <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-scrollbar max-height="30rem" warp-style="overflow-x: hidden;">
          <el-form
            :model="formData"
            :rules="formDataRules"
            ref="formRef"
            label-width="7.5rem"
            :style="{
              'pointer-events':
                dialog.type == 'create' || dialog.type == 'edit'
                  ? 'auto'
                  : 'none',
            }"
          >
            <el-form-item label="套餐名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入名称"
                clearable
                size="large"
              />
            </el-form-item>

            <el-form-item label="图片" prop="thumb">
              <UploadImg v-model:imgUrl="formData.thumb" />
            </el-form-item>

            <el-form-item label="账号数量" prop="accounts">
              <el-input
                v-model="formData.accounts"
                placeholder="请输入账号数量"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="更换账号次数" prop="unbind_acc">
              <el-input
                v-model="formData.unbind_acc"
                placeholder="请输入"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="存储容量" prop="storage">
              <el-input
                v-model="formData.storage"
                placeholder="请输入"
                clearable
                size="large"
              >
                <template #append>G</template>
              </el-input>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input
                v-model="formData.description"
                placeholder="请输入"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                :suffix-icon="`CaretBottom`"
                size="large"
                v-model="formData.status"
                placeholder="请选择套餐状态"
                filterable
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog> -->
    <!-- <el-dialog v-model="dialog2.visible">
      <img w-full :src="previewImgUrl" alt="Preview Image" />
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
// import UploadImg from "@/components/Upload/UploadImg.vue";
import {
  getEntCombo,
  getEntComboDetail,
  deleteEntCombo,
  addEntCombo,
  updateEntCombo,
} from "@/api/enterprise";
import { parseTime } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "EntCombo",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref([]);

const statusMap = reactive<any>({
  10: { type: "primary", label: "正常", color: "#409eff" },
  20: { type: "success", label: "上架", color: "#2ab7b0" },
  30: { type: "info", label: "下架", color: "#909399" },
  40: { type: "danger", label: "删除", color: "#f56c6c" },
  50: { type: "warning", label: "过期", color: "#e6a23c" },
  // add more status mappings as needed
});

const statusOptions = reactive<any>([
  // {
  //   value: 10,
  //   label: "正常",
  // },
  {
    value: 20,
    label: "上架",
  },
  {
    value: 30,
    label: "下架",
  },
]);
// 弹窗对象
const rowId = ref();
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "新增套餐",
});
const dialog2 = reactive({
  visible: false,
});

// 表单数据
// const formData = reactive<any>({
//   name: "",
//   description: "",
//   thumb: "",
//   status: 10,
//   storage: "",
//   accounts: "",
//   unbind_acc: "",
// });
// const imgUrl = ref("");
// const previewImgUrl = imgUrl.value;
// const formRef = ref(ElForm); // 表单ref
// const formDataRules = reactive<any>({
//   name: [{ required: true, message: "请输入名称", trigger: "blur" }],
//   thumb: [
//     { required: true, message: "请上传封面图", trigger: ["blur", "change"] },
//   ],
//   accounts: [{ required: true, message: "请输入账号数", trigger: "blur" }],
//   storage: [{ required: true, message: "请输入存储容量", trigger: "blur" }],
//   unbind_acc: [
//     { required: true, message: "请输入可更换账号数", trigger: "blur" },
//   ],
// });
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});

const enableAdd = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 16);
});

const enableApprove = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 17);
});

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 19);
});

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getEntCombo(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.packages.map((item: any) => {
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleCreate() {
  // dialog.visible = true;
  // dialog.title = "新增套餐";
  // dialog.type = "create";
  router.push({
    path: "ent-combo-action",
    query: { type: "create" },
  });
}
function onRowClick(type: any, row: any) {
  if (type == "edit") {
    // dialog.visible = true;
    // dialog.type = "edit";
    // dialog.title = "修改套餐";
    // rowId.value = row.id;
    // Object.assign(formData, row);
    router.push({
      path: "ent-combo-action",
      query: { id: row.id, type: "edit" },
    });
  }
  if (type == "delete") {
    handelDelete(row);
  }
}

function handelDelete(row: any) {
  ElMessageBox.confirm(`此操作将删除套餐，是否继续?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteEntCombo(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: `删除成功!`,
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
// function closeDialog() {
//   dialog.visible = false;
//   Object.assign(formData, {
//     name: "",
//     description: "",
//     thumb: "",
//     status: 10,
//     storage: "",
//     accounts: "",
//     unbind_acc: "",
//   });
//   formRef.value.resetFields();
//   formRef.value.clearValidate();
// }

// function handleSubmit() {
//   const data = {};

//   formRef.value.validate((valid: any) => {
//     if (valid) {
//       // data["name"] = formData.name;
//       // data["description"] = formData.description;
//       // data["thumb"] = formData.thumb;
//       // data["status"] = formData.status;
//       // data["storage"] = formData.storage;
//       // data["accounts"] = formData.accounts;
//       // data["unbind_acc"] = formData.unbind_acc;
//       if (dialog.type == "create") {
//         addEntCombo(formData).then((res: any) => {
//           if (res.status == 200) {
//             ElMessage.success({
//               message: `新增成功!`,
//             });
//             getData();
//             closeDialog();
//           }
//         });
//       }
//       if (dialog.type == "edit") {
//         updateEntCombo(rowId.value, formData).then((res: any) => {
//           if (res.status == 200) {
//             ElMessage.success({
//               message: `修改成功!`,
//             });
//             getData();
//             closeDialog();
//           }
//         });
//       }
//     }
//   });
// }
</script>

<style scoped lang="scss">
.ent-combo-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      img {
        object-fit: cover;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.dialog {
  .single-uploader {
    position: relative;
    display: flex;
    justify-content: flex-start;

    .single-uploader__image {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .img-upload__overlay {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 100px;
    }
  }
}
</style>
