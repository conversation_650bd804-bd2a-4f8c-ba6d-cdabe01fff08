<template>
  <div class="message-container">
    <div class="pre-header">
      <el-tabs
        v-model="queryParams.mt"
        class="header-tabs"
        @tab-change="handleTab"
      >
        <el-tab-pane
          v-for="(item, index) in typeOptions"
          :key="index"
          :label="item.label"
          :name="item.value"
        />
      </el-tabs>
    </div>
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <el-select
          :suffix-icon="`CaretBottom`"
            size="large"
            v-model="query.type"
            placeholder="请选择类型"
            filterable
            clearable
            @change="handleChange"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.ut"
            placeholder="请选择用户类型"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in userTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-date-picker
            class="date-picker"
            size="large"
            v-model="queryParams.dateTimeRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="handleQuery"
            value-format="YYYY-MM-DD"
          />
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增消息
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column
          label="发送对象"
          align="center"
          prop="name"
          min-width="120"
        >
          <template #default="scope">
            {{ userTypeFilter(scope.row.user_types) }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="发送方"
          align="center"
          prop="name"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.sender }}
          </template>
        </el-table-column> -->
        <el-table-column
          label="消息类型"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ typeMap[scope.row.msg_type + ""]?.label }}
          </template>
        </el-table-column>
        <el-table-column
          label="消息标题"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.title }}
          </template>
        </el-table-column>
        <el-table-column
          label="消息内容"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.msg_content }}
          </template>
        </el-table-column>
        <el-table-column
          label="发送时间"
          align="center"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.send_at }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="120"
        >
          <template #default="scope">
            <el-tag :type="scope.row.status == 1 ? 'success' : 'info'">{{
              scope.row.status == 1 ? "启用" : "禁用"
            }}</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
                v-if="queryParams.mt != 10"
              >
                修改
              </div>
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn delete-btn"
                @click="handelDelete(scope.row)"
                v-show="enableDelete"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="message-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form
          label-width="5rem"
          :style="{ pointerEvents: dialog.type == 'detail' ? 'none' : 'auto' }"
        >
          <el-form-item label="消息类型">
            <el-select
              :suffix-icon="`CaretBottom`"
              disabled
              size="large"
              v-model="formData.mt"
              placeholder="消息分类"
              filterable
              clearable
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="消息标题">
            <el-input
              v-model="formData.title"
              placeholder="请输入消息标题"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="消息内容">
            <el-input
              v-model="formData.content"
              placeholder="请输入消息内容"
              clearable
              size="large"
              type="textarea"
              :rows="3"
              resize="none"
            />
          </el-form-item>
          <el-form-item label="接收用户">
            <el-select
              size="large"
              :suffix-icon="`CaretBottom`"
              v-model="formData.receivers"
              placeholder="接收用户"
              filterable
              multiple
              clearable
            >
              <el-option
                v-for="item in userTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发送时间">
            <el-date-picker
              class="date-picker"
              v-model="formData.send_at"
              type="datetime"
              size="large"
              placeholder="选择时间"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="btn cancel-btn"
            @click="closeDialog"
            v-if="formData.mt != 10"
          >
            取 消
          </div>
          <div
            class="btn primary-btn"
            @click="closeDialog"
            v-if="formData.mt == 10"
          >
            确 定
          </div>
          <div
            class="btn primary-btn"
            @click="handleSubmit"
            v-if="dialog.type != 'detail'"
          >
            保 存
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { genFileId } from "element-plus";
import type {
  TabPaneName,
  UploadInstance,
  UploadProps,
  UploadRawFile,
} from "element-plus";
import {
  getMessages,
  deleteMessages,
  addMessages,
  updateMessages,
} from "@/api/message";
import { parseTime } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Message",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const activeTab = ref();
const statusOptions = reactive<any>([]);
const typeOptions = reactive([
  { value: 10, label: "系统消息" },
  { value: 20, label: "公告" },
]);
const typeMap = reactive<any>({
  "10": { value: 10, label: "系统消息" },
  "20": { value: 20, label: "公告" },
});
const userTypeOptions = ref<any>([
  { value: "10", label: "游客" },
  { value: "20", label: "学员" },
  { value: "30", label: "企业员工" },
  { value: "40", label: "企业管理员 " },
]);
const userTypeMap = reactive<any>({
  "10": { value: 10, label: "游客" },
  "20": { value: 20, label: "学员" },
  "30": { value: 30, label: "企业员工" },
  "40": { value: 40, label: "企业管理员 " },
});
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  from: "",
  till: "",
  ut: "",
  mt: 20,
  search: "",
  dateTimeRange: [],
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref<any>([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "25%",
  title: "详情",
});

// 表单数据
const rowId = ref();
const formData = reactive<any>({
  title: "",
  content: "",
  receivers: "",
  send_at: "",
  dateTimeRange: "",
});
const imgUrl = ref("");

watch(
  () => queryParams.dateTimeRange,
  (newVal) => {
    if (newVal) {
      queryParams.from = parseTime(newVal[0], "{y}{m}{d}000000");
      queryParams.till = parseTime(newVal[1], "{y}{m}{d}235959");
    }
    // else{}
  }
);

onMounted(() => {
  formData.mt = 20;
  getData();
});
onBeforeUnmount(() => {});

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 20);
});

function getData() {
  loading.value = true;
  const params = {
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    from: queryParams.from || undefined,
    till: queryParams.till || undefined,
    ut: queryParams.ut || undefined,
    mt: queryParams.mt || undefined,
    search: queryParams.search || undefined,
  };
  if (!queryParams.dateTimeRange) {
    delete params.from;
    delete params.till;
  }
  getMessages(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.messages.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          item.send_at = item.send_at
            ? parseTime(item.send_at, "{y}-{m}-{d} {h}:{i}:{s}")
            : "--";
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}
function userTypeFilter(val: any) {
  let res: any = "- -";
  if (val) {
    res = [];
    const resArr: any = val.split(",");
    userTypeOptions.value.forEach((item: any) => {
      resArr.forEach((itm: any) => {
        if (itm == item.value) {
          res.push(item.label);
        }
      });
    });
  }
  return res.toString();
}

function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增消息";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  if (type === "edit") {
    dialog.title = "修改消息";
    dialog.type = "edit";
    dialog.visible = true;
    Object.assign(formData, row);
    formData.receivers = row.user_types.split(",");
    formData.content = row.msg_content;
    formData.send_at = parseTime(row.send_at, "{y}-{m}-{d} {h}:{i}:{s}");
    rowId.value = row.id;
  }
  if (type === "detail") {
    dialog.title = "消息详情";
    dialog.type = "detail";
    dialog.visible = true;
    Object.assign(formData, row);
    if (queryParams.mt == 10) {
      formData.mt = 10;
    }
    formData.receivers = row.user_types.split(",");
    formData.content = row.msg_content;
    formData.send_at = parseTime(row.send_at, "{y}-{m}-{d} {h}:{i}:{s}");
    rowId.value = row.id;
  }
  if (type === "delete") {
    handelDelete(row);
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除消息，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteMessages(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}

function handleTab() {
  handleQuery();
  // formData.mt = queryParams.mt;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
  // console.log(query.search);
}

function closeDialog() {
  dialog.visible = false;
  setTimeout(() => {
    formData.mt = 20;
  }, 100);
  Object.assign(formData, {
    title: "",
    content: "",
    receivers: "",
    sent_at: "",
    dateTimeRange: "",
  });
}
function handleSubmit() {
  const data = {
    mt: 20,
    title: formData.title,
    content: formData.content,
    receivers: formData.receivers.toString(),
    send_at: parseTime(formData.send_at, "{y}{m}{d}{h}{m}{s}"),
  };
  if (dialog.type === "create") {
    addMessages(data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "新增成功!",
        });
        closeDialog();
        getData();
      }
    });
  }
  if (dialog.type === "edit") {
    updateMessages(rowId.value, data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "修改成功!",
        });
        closeDialog();
        getData();
      } else {
        ElMessage.error(res.message);
      }
    });
  }
}
</script>

<style scoped lang="scss">
.message-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 170px);
    height: calc(100% - 240px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
