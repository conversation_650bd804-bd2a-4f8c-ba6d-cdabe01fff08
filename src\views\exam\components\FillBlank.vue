<template>
  <!-- 填空题组件 -->
  <div
    class="fill-blank-container"
    ref="fillBlankContainer"
    :style="{
      'pointer-events': isOperable ? 'auto' : 'none',
    }"
  >
    <div class="question-panel">
      <div class="question-title">
        <span class="text"
          >{{ questionIndex + 1 }}.{{ questionData.content.title }}
        </span>
        <!-- <span class="blank">（　　）</span> -->
        <span class="score-tip">（填空题·{{ questionData.score }}分）</span>
      </div>
      <div class="answer-row">
        <el-input
          :model-value="
            showSelectedAnswer ? questionData.content.answers : answersData
          "
          :readonly="showAnswer"
          placeholder="请输入答案"
          size="large"
          clearable
          @input="
            (val) => {
              if (!showAnswer) answersData.value = val;
            }
          "
        />
      </div>
      <div class="result-row" v-if="showAnswer">
        <div
          class="result-answer"
          :class="{
            correct: isCorrect && showGetScore,
            wrong: !isCorrect && showGetScore,
          }"
        >
          <span class="highlight"
            >答案： {{ questionData.content.answers }} </span
          >；
          <span class="score" v-if="showGetScore"> 得分{{ score }}</span>
        </div>
        <div
          class="result-analysis"
          v-if="showAnalysis && questionData.content.remark"
        >
          <div class="analysis-title">
            解析:{{ questionData.content.remark }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { el } from "element-plus/es/locale";
import { useRoute, useRouter } from "vue-router";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "FillBlank",
  inheritAttrs: false,
});
const props = defineProps({
  isOperable: {
    type: Boolean,
    default: false,
  }, //是否只读或答题
  showAnswer: {
    type: Boolean,
    default: false,
  },
  showSelectedAnswer: {
    type: Boolean,
    default: false,
  },
  showGetScore: {
    type: Boolean,
    default: false,
  },
  showAnswerTime: {
    type: Boolean,
    default: false,
  },

  showAnalysis: {
    type: Boolean,
    default: false,
  },
  showAnswerType: {
    type: String,
    default: "all", // all-全部, correct-正确, incorrect-错误
  },
  questionData: {
    type: Object,
    default: () => {
      return {};
    },
  },
  questionIndex: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(["updateAnswer", "updateScore", "submit"]);
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const questionData = computed(() => props.questionData);
const questionIndex = computed(() => props.questionIndex);
const isOperable = computed(() => props.isOperable);
const showAnswer = computed(() => props.showAnswer);
const showSelectedAnswer = computed(() => props.showSelectedAnswer);
const showGetScore = computed(() => props.showGetScore);
const showAnswerTime = computed(() => props.showAnswerTime);

const showAnalysis = computed(() => props.showAnalysis);
const showAnswerType = computed(() => props.showAnswerType);

const isCorrect = computed(() => {
  return questionData.value.answers === questionData.value.content.answers;
}); //判断是否正确
const score = computed(() => {
  return isCorrect.value ? questionData.value.score : 0;
}); //判断得分

const answersData = ref<any>([]);
function changeAnswer(answer: any) {
  emit("updateAnswer", answer);
}

onBeforeMount(() => {});
onMounted(() => {});
</script>

<style scoped lang="scss">
.fill-blank-container {
  width: 100%;
  padding: 18px 18px 24px;
  border-bottom: 1px solid hsl(200deg 6% 42% / 44%);

  .question-panel {
    .question-title {
      margin-bottom: 10px;
      font-size: 18px;
      font-weight: 500;
      color: #3b4664;

      .score-tip {
        font-size: 15px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .answer-row {
      margin: 12px 0 0;
      font-size: 15px;
      color: #666;

      .label {
        color: #888;
      }

      .answer {
        font-weight: 500;
        color: #2ab7b0;
      }
    }

    .result-row {
      .result-answer {
        margin: 12px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }

      .correct {
        color: #00918c;
      }

      .wrong {
        color: #f06e15;
      }
    }

    .result-analysis {
      font-size: 20px;
      font-weight: 500;
      color: #00918c;
    }
  }
}
</style>
