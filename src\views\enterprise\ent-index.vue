<template>
  <div class="ent-index-container">
    <div class="pre-header">
      <el-tabs v-model="activeTab" class="header-tabs" @tab-change="handleTab">
        <el-tab-pane
          v-for="(item, index) in statusOptions"
          :key="index"
          :label="item.label"
          :name="item.value"
        />
      </el-tabs>
    </div>
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增企业
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="企业名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="企业规模" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.ent_size || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="企业套餐" align="center" min-width="120">
          <template #default="scope">
            {{
              scope.row.packages?.length > 0
                ? scope.row.packages.map((item: any) => item.name).toString()
                : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="120">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type || 'info'">{{
              statusMap[scope.row.status]?.label || "--"
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="企业人员" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.employees || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          label="申请时间"
          align="center"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.created_at || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                v-if="scope.row.status != 60"
                class="btn light-green-btn"
                @click="onRowClick('combo', scope.row)"
              >
                套餐
              </div>
              <div
                v-if="scope.row.status != 60"
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                v-if="scope.row.status == 20"
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                v-if="scope.row.status == 60"
                class="btn info-btn"
                @click="onRowClick('detail2', scope.row)"
              >
                详情
              </div>
              <!-- <div class="btn delete-btn" @click="handelDelete(scope.row)" v-show="enableDelete">
                删除
              </div> -->
              <div
                v-if="scope.row.status == 10 || scope.row.status == 50"
                class="btn verify-btn"
                @click="handelVerify(scope.row)"
                v-show="enableApprove"
              >
                审核
              </div>

              <div
                class="btn primary-btn"
                v-if="scope.row.status == 30"
                @click="handleEntStatus(scope.row)"
                v-show="enableFreeze"
              >
                解除冻结
              </div>
              <div
                class="btn orange-btn"
                v-if="scope.row.status == 20"
                @click="handleEntStatus(scope.row)"
                v-show="enableFreeze"
              >
                冻结
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-scrollbar max-height="32rem" warp-style="overflow-x: hidden;">
          <el-form
            label-width="6.5rem"
            v-if="dialog.type != 'combo'"
            :model="formData"
            :rules="formDataRules"
            ref="formRef"
            :style="{
              'pointer-events':
                dialog.type == 'create' ||
                dialog.type == 'edit' ||
                dialog.type == 'combo'
                  ? 'auto'
                  : 'none',
            }"
          >
            <el-form-item label="企业名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入企业名称"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item
              label="社会信用统一代码"
              prop="credit_code"
              v-if="dialog.type == 'verify' || dialog.type == 'detail2'"
            >
              <el-input
                v-model="formData.credit_code"
                placeholder="社会信用统一代码"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item
              label="企业营业执照"
              prop="license"
              v-if="dialog.type == 'verify' || dialog.type == 'detail2'"
              :style="{
                'pointer-events': 'auto',
              }"
            >
              <div
                v-if="formData.license"
                class="license-img"
                @click="showLicense"
              >
                <img :src="formData.license" />
              </div>
              <!-- -->
              <div v-else class="text">暂无执照</div>
            </el-form-item>

            <el-form-item label="企业规模" prop="ent_size">
              <el-select
                :suffix-icon="`CaretBottom`"
                size="large"
                v-model="formData.ent_size"
                placeholder="请选择企业规模"
                filterable
              >
                <el-option
                  v-for="(item, index) in sizeOptions"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="企业套餐" prop="package_ids">
              <el-select
                :suffix-icon="`CaretBottom`"
                size="large"
                v-model="formData.package_ids"
                placeholder="请选择企业套餐"
                filterable
                multiple
                :disabled="dialog.type == 'edit'"
              >
                <el-option
                  v-for="item in comboOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="联系人名称" prop="contact">
              <el-input
                v-model="formData.contact"
                placeholder="请输入联系名称"
                clearable
                size="large"
              />
            </el-form-item>
            <!-- <el-form-item label="省份">
              <el-input
                v-model="formData.province"
                placeholder="请输入省份"
                clearable
                size="large"
              />
            </el-form-item> -->
            <el-form-item label="城市">
              <!-- <el-input
                v-model="formData.city"
                placeholder="请输入城市"
                clearable
                size="large"
              /> -->
              <el-cascader
                class="area-cascader"
                size="large"
                placeholder="省/市/区"
                :options="addressList"
                v-model="formData.cityCode"
                popper-class="area-cascader"
                @change="holdTypeChange"
                :suffix-icon="`CaretBottom`"
              />
              <!-- :props="cityProps" -->
            </el-form-item>
            <el-form-item label="地址" prop="address">
              <el-input
                v-model="formData.address"
                placeholder="请输入详细地址"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="联系人电话" prop="contact_no">
              <el-input
                v-model="formData.contact_no"
                placeholder="请输入联系电话"
                clearable
                size="large"
              />
            </el-form-item>
          </el-form>
          <!-- 内训系统 -->
          <el-form
            label-width="6rem"
            v-if="dialog.type != 'combo'"
            :model="formData"
            :rules="formDataRules"
            ref="formRef"
          >
            <el-form-item label="内训系统" class="custom-el-switch">
              <el-switch v-model="trainSysState" />
            </el-form-item>
            <template v-if="trainSysState">
              <el-form-item label="过期时间">
                <el-date-picker
                  class="date-picker"
                  v-model="trainSystemForm.expired_at"
                  type="date"
                  size="large"
                  placeholder="请选择过期时间"
                  clearable
                />
              </el-form-item>
              <el-form-item label="账号数目">
                <el-input
                  v-model="trainSystemForm.train_accs"
                  placeholder="请输入账号数目"
                  clearable
                  size="large"
                />
              </el-form-item>
              <el-form-item label="存储空间">
                <el-input
                  v-model="trainSystemForm.storage"
                  placeholder="请输入存储容量"
                  clearable
                  size="large"
                >
                  <template #append>G</template>
                </el-input>
              </el-form-item>
            </template>
          </el-form>

          <!-- 企业套餐 -->
          <el-form
            label-width="6rem"
            v-if="dialog.type == 'combo'"
            :model="formData"
            :rules="formDataRules"
            ref="formRef"
          >
            <el-form-item label="当前">
              <span>{{ currentCombo }}</span>
            </el-form-item>

            <el-form-item label="变更为" prop="package_ids">
              <el-select
                :suffix-icon="`CaretBottom`"
                size="large"
                v-model="formData.package_ids"
                placeholder="请选择企业套餐"
                multiple
                filterable
                clearable
              >
                <el-option
                  v-for="item in comboOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </div>
      <template #footer>
        <div
          class="dialog-footer"
          v-if="
            dialog.type == 'create' ||
            dialog.type == 'edit' ||
            dialog.type == 'combo'
          "
        >
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
        <div class="dialog-footer" v-if="dialog.type == 'detail2'">
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
        <div class="dialog-footer" v-if="dialog.type == 'verify'">
          <div class="btn cancel-btn" @click="verifySubmit(false)">
            不 通 过
          </div>
          <div class="btn primary-btn" @click="verifySubmit(true)">通 过</div>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      class="detail-dialog"
      v-model="detailDialog.visible"
      :title="detailDialog.title"
      :width="detailDialog.width"
      append-to-body
      @close="closeDetailDialog"
    >
      <div class="dialog-body">
        <el-scrollbar height="70vh" warp-style="overflow-x: hidden;">
          <div class="basic-form">
            <el-row>
              <el-col :span="8" class="item">
                <div class="label">企业名称：</div>
                <div class="text">
                  <el-tooltip placement="top" :content="formDetail.name">
                    {{ formDetail.name }}
                  </el-tooltip>
                </div>
              </el-col>
              <el-col :span="7" class="item">
                <div class="label">企业状态：</div>
                <div class="text">
                  {{ statusMap[formDetail.status]?.label || "--" }}
                </div>
              </el-col>
              <el-col :span="9" class="item">
                <div class="label">地址：</div>
                <div class="text">
                  {{ formDetail.province }}{{ formDetail.city
                  }}{{ formDetail.address }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="item">
                <div class="label">联系人：</div>
                <div class="text">{{ formDetail.contact }}</div>
              </el-col>
              <el-col :span="7" class="item">
                <div class="label">联系电话：</div>
                <div class="text">
                  {{ formDetail.contact_no }}
                </div>
              </el-col>
              <el-col :span="9" class="item">
                <div class="label">申请时间：</div>
                <div class="text">
                  {{ formDetail.created_at }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="item" style="align-items: flex-start">
                <div class="label">企业营业执照：</div>
                <div
                  class="license-img"
                  @click="showLicense"
                  v-if="formDetail.license"
                >
                  <img :src="formDetail.license" />
                </div>
                <div v-else class="text">暂无执照</div>
              </el-col>
              <el-col :span="16" class="item" style="align-items: flex-start">
                <div class="label">社会信用统一代码：</div>
                <div class="text">
                  {{ formDetail.credit_code || "--" }}
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="usage-form">
            <el-row>
              <el-col :span="12" class="item">
                <div class="label" style="width: 8rem">企业套餐：</div>
                <div class="input">
                  <el-select
                    size="large"
                    v-model="currentComboId"
                    placeholder="请选择企业套餐"
                    filterable
                    :suffix-icon="`CaretBottom`"
                    @change="currentComboChange"
                  >
                    <el-option
                      v-for="item in packageOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <!-- <div class="text-green">{{ currentCombo }}</div> -->
              </el-col>
              <el-col :span="12" class="item">
                <div class="label">企业规模：</div>
                <div class="text-green">
                  {{ formDetail.ent_size || "--" }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10" class="item">
                <div class="label">套餐有效期至：</div>
                <div class="text-green">
                  {{ currentPackage.expired_at || "--" }}
                </div>
              </el-col>
              <!-- <el-col :span="7" class="item">
                <div class="label">账号数目：</div>
                <div class="text-green">
                  共 {{ currentPackage.accounts || "--" }} 个
                </div>
              </el-col>
              <el-col :span="7" class="item">
                <div class="label">可更换账号数：</div>
                <div class="text-green">
                  共 {{ currentPackage.unbind_acc || "--" }} 个
                </div>
              </el-col> -->
            </el-row>
          </div>
          <div class="package-course-table">
            <el-col :span="8" class="item">
              <div class="label">套餐配套课程列表：</div>
            </el-col>
            <el-table
              :data="courseTableData"
              max-height="15rem"
              border
              fit
              highlight-current-row
              class="courseList"
            >
              <el-table-column label="课程" align="center" min-width="140">
                <template #default="scope">
                  <div class="cover-name">
                    <img :src="scope.row.thumb" alt="" class="cover-img" />
                    <span>{{ scope.row.title }}</span>
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column label="所属栏目" align="center" min-width="100">
                <template #default="scope">
                  {{ scope.row.sections?.[0]?.name || "--" }}
                </template>
              </el-table-column> -->
              <el-table-column
                label="课程账号数"
                align="center"
                min-width="100"
              >
                <template #default="scope">
                  {{ scope.row.accounts || "--" }}
                </template>
              </el-table-column>
              <el-table-column
                label="已分配账号数"
                align="center"
                min-width="100"
              >
                <template #default="scope">
                  {{ scope.row.assigned || "0" }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="employees-table">
            <el-col :span="8" class="item">
              <div class="label">
                员工列表（{{ formDetail.employees?.length || 0 }}）：
              </div>
            </el-col>
            <el-table
              :data="formDetail.employees"
              max-height="15rem"
              border
              fit
              highlight-current-row
            >
              <el-table-column label="员工id" align="center" min-width="40">
                <template #default="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column>
              <el-table-column label="姓名" align="center" min-width="100">
                <template #default="scope">
                  {{ scope.row.name }}
                </template>
              </el-table-column>
              <el-table-column label="手机号码" align="center" min-width="120">
                <template #default="scope">
                  {{ scope.row.mobile }}
                </template>
              </el-table-column>

              <el-table-column
                label="是否绑定微信"
                align="center"
                min-width="60"
              >
                <template #default="scope">
                  {{ scope.row.bind_wechat ? "已绑定" : "未绑定" }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closeDetailDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      class="previewImgDialog"
      :width="'70%'"
      v-model="dialogPreview.visible"
    >
      <!-- <div class="previewImg-dialog">
        <img h-full :src="previewImgUrl" alt="Preview Image" />
      </div> -->
      <div class="preview-img">
        <el-scrollbar warp-style="overflow-x: hidden;">
          <img :src="previewImgUrl" alt="Preview Image" />
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import cityData from "@/utils/city.json";
import {
  getEnterprises,
  getEnterprisesDetail,
  deleteEnterprises,
  addEnterprises,
  updateEnterprises,
  getEntCombo,
  getEntTrainSys,
  addEntTrainSys,
  updateEntTrainSys,
} from "@/api/enterprise";
import { parseTime } from "@/utils";
import { validateMobile, validEmail } from "@/utils/validate";

import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";

import { regionData, CodeToText, TextToCode } from "element-china-area-data";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "EntIndex",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const userStore = useUserStore();

// const addressList = ref(cityData);
const addressList = ref<any>(regionData);
const cityProps = reactive({
  value: "code", //指定选项的值为addresslist对象的code属性
  label: "name", //指定选项标签为addresslist对象的name属性
  children: "children", //指定选项的子选项为addresslist对象的children属性
});
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  status: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "新增企业",
});
const detailDialog = reactive({
  visible: false,
  type: "form",
  width: "50%",
  title: "企业详情",
});

const comboOptions = ref<any>([]);
// 表单数据
const rowId = ref();
const formData = reactive<any>({
  name: "",
  country: "中国",
  province: "",
  city: "",
  ent_size: "",
  address: "",
  package_ids: "",
  contact: "",
  contact_no: "",
  addressList: [], // 省市区
  status: "",
});
const currentCombo = ref<any>("");
const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  contact_no: [
    {
      required: true,
      trigger: "blur",
      validator: validateMobile,
    },
  ],
  contact: [{ required: true, message: "请输入联系人名称", trigger: "blur" }],
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  package_ids: [{ required: false, message: "请选择套餐", trigger: "blur" }],
  ent_size: [{ required: true, message: "请选择企业规模", trigger: "blur" }],
});
const sizeOptions = reactive<any>(["1-30", "31-50", "51-100", "100人以上"]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "待审核", color: "#409eff" },
  20: { type: "success", label: "正常", color: "#2ab7b0" },
  30: { type: "info", label: "冻结", color: "#909399" },
  40: { type: "danger", label: "注销", color: "#f56c6c" },
  50: { type: "warning", label: "咨询中", color: "#e6a23c" },
  60: { type: "error", label: "审核不通过", color: "#f56c6c" },
});
const activeTab = ref<any>("");
const statusOptions = ref<any>([
  { value: "", label: "全部" },
  { value: 20, label: "正常" },
  { value: 30, label: "冻结" },
  { value: 10, label: "待审核" },
  // { value: 50, label: "咨询中" },
  { value: 60, label: "不通过" },
  // { value: 40, label: "注销" },
]);
const formDetail = reactive<any>({});

const courseTableData = ref<any>([]);
const currentPackage = reactive<any>({});
const packageOptions = ref<any>([]);
const currentComboId = ref<any>("");

const dialogPreview = reactive({
  visible: false,
  title: "企业营业执照",
  width: "60%",
});
const previewImgUrl = ref<any>("");

//内训系统表单--只有创建了企业才可一开通，不然没id
const trainSysState = ref<any>(false);
const trainSystemForm = reactive<any>({
  // ent_id: "",
  // status:''
  storage: "",
  train_accs: "",
  expired_at: "",
});
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });

//TODO:监听省市区做法？
// watch(
//   () => formData.addressList,
//   (newVal) => {
//     if (newVal && dialog.visible == true) {
//       formData.province = newVal[0];
//       formData.city = newVal[1];
//     }
//   }
// );
onMounted(() => {
  getEntCombos();
  getData();
});

const enableFreeze = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 12);
});

const enableApprove = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 13);
});

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 14);
});

function getEntCombos() {
  const params = {
    page: 1,
    per_page: 999,
  };
  getEntCombo(params).then((res: any) => {
    if (res.status == 200) {
      comboOptions.value = res.data.packages.map((item: any) => {
        item.label = item.name;
        item.value = item.id;
        return item;
      });
    }
  });
}
function filterComboName(val: any) {
  let res = "";
  if (val) {
    res = comboOptions.value.find((item: any) => item.id == val).name;
  }
  return res;
}
function handleTab() {
  queryParams.pageNum = 1;
  queryParams.status = activeTab.value;
  getData();
}
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    status: queryParams.status || undefined,
  };
  getEnterprises(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.enterprises.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增企业";
  dialog.type = "create";
}

function onRowClick(type: any, row: any) {
  if (type == "combo") {
    dialog.visible = true;
    rowId.value = row.id;
    dialog.type = "combo";
    dialog.title = row.name || "企业套餐";
    Object.assign(formData, row);
    getEnterprisesDetail(row.id).then((res: any) => {
      currentCombo.value =
        res.data.packages?.length > 0
          ? res.data.packages.map((item: any) => item.name).toString()
          : "--";
      formData.package_ids =
        res.data.packages?.length > 0
          ? res.data.packages.map((item: any) => item.id)
          : "";
    });
  }
  if (type == "detail2") {
    dialog.visible = true;
    dialog.type = "detail2";
    dialog.title = "企业详情";
    Object.assign(formData, row);
  }
  if (type == "detail") {
    router.push({
      path: "ent-detail",
      query: { id: row.id, type: "detail" },
    });
    // detailDialog.visible = true;
    // detailDialog.type = "detail";
    // detailDialog.title = "企业详情";
    // currentCombo.value =
    //   row.packages?.length > 0
    //     ? row.packages.map((item: any) => item.name).toString()
    //     : "--";
    // formData.package_ids = row.packages?.map((item: any) => item.id);
    // getEnterprisesDetail(row.id).then((res: any) => {
    //   Object.assign(formDetail, res.data, {
    //     created_at: parseTime(res.data.created_at, "{y}-{m}-{d} {h}:{i}:{s}"),
    //   });
    //   currentComboId.value = res.data.packages[0]?.id;
    //   console.log("currentComboId", currentComboId.value);
    //   currentComboChange();
    //   packageOptions.value = res.data.packages.map((item: any) => {
    //     return {
    //       label: item.name,
    //       value: item.id,
    //     };
    //   });
    // });
  }
  if (type == "edit") {
    dialog.visible = true;
    rowId.value = row.id;
    dialog.type = "edit";
    dialog.title = "修改企业";
    Object.assign(formData, row);
    getEnterprisesDetail(row.id).then((res: any) => {
      formData.cityCode = res.data.city ? cityTextToCode(res.data.city) : "";
      formData.package_ids =
        res.data.packages?.length > 0
          ? res.data.packages.map((item: any) => item.id)
          : "";
    });
    getEntTrainSys({ ent_id: row.id }).then((res: any) => {
      Object.assign(trainSystemForm, res.data[0], {
        expired_at: res.data[0]?.expired_at
          ? parseTime(res.data[0].expired_at, "{y}-{m}-{d}")
          : "",
        storage: res.data[0]?.storage
          ? (res.data[0]?.storage / 1024 / 1024 / 1024).toFixed(3)
          : "",
      });
      if (res.data[0] && res.data[0].status != 30) {
        trainSysState.value = true;
      }
    });
  }
  if (type == "delete") {
    handelDelete(row);
  }
}
function currentComboChange() {
  if (currentComboId.value) {
    Object.assign(
      currentPackage,
      formDetail.packages.filter(
        (item: any) => item.id == currentComboId.value
      )[0]
    );
    currentPackage.expired_at = currentPackage.expired_at
      ? parseTime(currentPackage.expired_at, "{y}-{m}-{d} {h}:{i}:{s}")
      : "--";
    courseTableData.value = currentPackage.courses;
  }
}

function handelVerify(row: any) {
  dialog.visible = true;
  dialog.type = "verify";
  dialog.title = "审核";
  rowId.value = row.id;
  Object.assign(formData, row);
  getEnterprisesDetail(row.id).then((res: any) => {
    formData.package_ids =
      res.data.packages?.length > 0
        ? res.data.packages.map((item: any) => item.id)
        : "";
  });
  // formData.package_ids =
  //   row.packages?.length > 0 ? row.packages.map((item: any) => item.id) : "";
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除企业，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteEnterprises(row.id).then((res: any) => {
      ElMessage.success({
        message: "删除成功!",
      });
    });
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closeDialog() {
  dialog.visible = false;
  formRef.value.clearValidate();
  formRef.value.resetFields();
  Object.assign(formData, {
    name: "",
    country: "中国",
    province: "",
    city: "",
    cityCode: "",
    address: "",
    package_ids: "",
    contact: "",
    contact_no: "",
    ent_size: "",
  });

  trainSysState.value = false;
  Object.keys(trainSystemForm).forEach((key) => {
    trainSystemForm[key] = "";
  });
}
function closeDetailDialog() {
  detailDialog.visible = false;
  Object.keys(formDetail).forEach((key) => delete formDetail[key]); //清空formData
  Object.keys(currentPackage).forEach((key) => delete currentPackage[key]);
  courseTableData.value = [];

  packageOptions.value = [];
  currentComboId.value = "";
}

function handleSubmit() {
  const data = {
    name: formData.name,
    country: "中国",
    province: formData.province,
    city: formData.city,
    address: formData.address,
    package_ids: formData.package_ids,
    contact: formData.contact,
    contact_no: formData.contact_no,
    ent_size: formData.ent_size,
  };
  formRef.value.validate((valid: any) => {
    if (valid) {
      if (dialog.type === "create") {
        addEnterprises(data).then((res: any) => {
          ElMessage.success({
            message: "创建成功!",
          });
          closeDialog();
          getData();
        });
      }

      if (
        dialog.type === "edit" &&
        trainSysState.value &&
        (!trainSystemForm.storage ||
          !trainSystemForm.train_accs ||
          !trainSystemForm.expired_at)
      ) {
        ElMessage.warning("内训系统内容不可为空");
        return;
      }

      if (dialog.type === "edit" || dialog.type === "combo") {
        updateEnterprises(rowId.value, data).then((res: any) => {
          ElMessage.success({
            message: "修改成功!",
          });
          closeDialog();
          getData();
        });
      }

      //内训系统
      if (dialog.type === "edit") {
        const data: any = {
          ent_id: rowId.value,
          storage: trainSystemForm.storage
            ? (trainSystemForm.storage * 1024 * 1024 * 1024).toFixed(0)
            : undefined, //基本单位为mb
          train_accs: trainSystemForm.train_accs * 1 || undefined,
          expired_at: trainSystemForm.expired_at
            ? parseTime(trainSystemForm.expired_at, "{y}{m}{d}000000")
            : undefined,
        };
        if (!trainSystemForm.id && trainSysState.value) {
          addEntTrainSys(data).then((res) => {});
        } else if (trainSystemForm.id) {
          delete data.id;
          data.status = trainSysState.value ? 10 : 30; // 10-正常，20-过期，30-暂停

          updateEntTrainSys(trainSystemForm.id, data).then((res) => {});
        }
      }
    }
  });
}
function handleEntStatus(row: any) {
  const data: any = {};
  if (row.status == 20) {
    data.status = 30;
  } else if (row.status == 30) {
    data.status = 20;
  }
  ElMessageBox.confirm(
    `此操作将${row.status == 20 ? "冻结" : "解冻"}该企业，是否继续?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    updateEnterprises(row.id, data).then((res: any) => {
      ElMessage.success({
        message: "操作成功!",
      });
      getData();
    });
  });
}
function verifySubmit(val: any) {
  const data: any = {
    // name: formData.name,
    // country: "中国",
    // province: formData.province,
    // city: formData.city,
    // address: formData.address,
    // package_id: formData.package_id,
    // contact: formData.contact,
    // contact_no: formData.contact_no,
  };
  if (val) {
    data.status = 20;
  } else {
    data.status = 60;
    // closeDialog();
    // return;
  }
  updateEnterprises(rowId.value, data).then((res: any) => {
    if (res.status == 200) {
      ElMessage.success({
        message: "操作成功!",
      });
      closeDialog();
      getData();
    }
  });
}

function showLicense() {
  dialogPreview.visible = true;
  previewImgUrl.value = formDetail.license || formData.license;
}

function holdTypeChange() {
  getCodeToText(formData.cityCode);
}
function cityTextToCode(cityText: any) {
  let code: any = [];
  const cityArray: any = cityText.split(",");
  switch (cityArray.length) {
    case 1:
      code = [TextToCode[cityArray[0]].code];
      break;
    case 2:
      code = [
        TextToCode[cityArray[0]].code,
        TextToCode[cityArray[0]][cityArray[1]].code,
      ];
      break;
    case 3:
      code = [
        TextToCode[cityArray[0]].code,
        TextToCode[cityArray[0]][cityArray[1]].code,

        TextToCode[cityArray[0]][cityArray[1]][cityArray[2]].code,
      ];
      break;
    default:
      break;
  }
  // TextToCode[‘北京市’].code输出110000,TextToCode[‘北京市’][‘市辖区’].code输出110100,TextToCode[‘北京市’][‘市辖区’][‘朝阳区’].code输出110105
  return code;
}
function getCodeToText(codeArray: any) {
  let area = "";
  switch (codeArray.length) {
    case 1:
      area += CodeToText[codeArray[0]];
      break;
    case 2:
      area += CodeToText[codeArray[0]] + "/" + CodeToText[codeArray[1]];
      break;
    case 3:
      area +=
        CodeToText[codeArray[0]] +
        "," +
        CodeToText[codeArray[1]] +
        "," +
        CodeToText[codeArray[2]];
      break;
    default:
      break;
  }
  formData.city = area;
  // console.log(area);
}
</script>

<style scoped lang="scss">
.ent-index-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 170px);
    height: calc(100% - 240px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

// .previewImg-dialog {
//   height: 60vh;
//   justify-content: center;
//   align-items: center;
//   display: flex;
// }
.preview-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  :deep(.el-scrollbar__wrap) {
    max-height: 75vh;
  }

  img {
    width: 100%;
  }
}

.dialog,
.detail-dialog {
  .dialog-body {
    .item {
      display: flex;
      align-items: center;
      margin: 10px 0;
      text-align: center;

      .label {
        font-size: 18px;
        font-weight: 600;
        color: #3b4664;
      }

      .text {
        overflow: hidden;
        font-size: 18px;
        color: #152b62;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .text-green {
        font-size: 20px;
        color: #00918c;
      }

      .input {
        width: 65%;
        margin-left: 5px;

        :deep(.el-select__wrapper) {
          color: #7b8d9c !important;
          background-color: #f5f5f591 !important;
          border: 1px solid #c0c7d666 !important;
        }
      }
    }

    :deep(.el-form-item) {
      display: flex;
      align-items: center;
      text-align: center;
    }

    .license-img {
      width: 120px;
      height: 120px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .basic-form {
      width: 100%;
      min-height: 7rem;
      padding: 10px;
      // background: url("@/assets/images/basic-bg.png") no-repeat;
      // background-size: 100% 100%;
      padding: 5px 20px;
      margin: 15px 0;
      background: #f7f8fb;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 0 3px 6px 1px rgb(188 198 214 / 16%);

      .label {
        white-space: nowrap;
      }
    }

    .usage-form {
      width: 100%;
      // height: 10rem;
      height: 8rem;
      padding: 10px;
      // background: url("@/assets/images/usage-bg.png") no-repeat;
      // background-size: 100% 100%;
      padding: 5px 20px;
      margin: 15px 0;
      background: #f7f8fb;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 0 3px 6px 1px rgb(188 198 214 / 16%);

      .text {
        margin-left: 15px;
      }

      .label {
        text-align: left;
      }
    }

    .package-course-table {
      margin: 20px 0;

      .courseList {
        margin-bottom: 30px;

        .cover-name {
          display: flex;
          align-items: center;
          justify-content: center;

          .cover-img {
            // width: 150px !important;
            // height: 100px !important;
            width: 162px !important;
            height: 86px !important;
            object-fit: cover;
            border-radius: 8px;
          }

          span {
            display: inline-block;
            width: 40%;
          }
        }
      }
    }
    // .employees-table {
    // }
  }
}
</style>
