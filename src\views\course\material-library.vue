<template>
  <div class="material-container">
    <div class="pre-header">
      <el-tabs v-model="activeTab" class="header-tabs" @tab-change="handleTab">
        <el-tab-pane
          v-for="(item, index) in typeOptions"
          :key="index"
          :label="item.label"
          :name="item.value"
        />
      </el-tabs>
    </div>
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>

        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.gid"
            placeholder="请选择素材分组"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in groupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增素材
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="ID" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="资源名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="资源类型" align="center" min-width="70">
          <template #default="scope">
            {{ typeMap[scope.row.res_type] }}
          </template>
        </el-table-column> -->
        <el-table-column label="资源大小" align="center" min-width="70">
          <template #default="scope"> {{ scope.row.size }}Mb </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="120">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn info-btn"
                @click="onRowClick('preview', scope.row)"
              >
                预览
              </div>
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>

              <div
                class="btn delete-btn"
                @click="handelDelete(scope.row)"
                v-show="enableDelete"
                :style="{
                  pointerEvents: enableDelete ? 'auto' : 'none',
                  opacity: enableDelete ? '1' : '0.5',
                }"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 素材弹窗 -->
    <el-dialog
      class="material-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <el-progress
        class="upload-progress"
        v-if="uploading && formData.res_type == 10"
        :percentage="uploadPercent"
        :stroke-width="15"
        striped
        striped-flow
        :duration="10"
      />
      <div class="loading-mask" v-if="fileLoading || uploading">
        文件上传处理中，请稍等...
      </div>
      <div class="dialog-body">
        <!--         v-loading="fileLoading"
        element-loading-text="文件上传处理中，请稍等..."
        element-loading-background="#ffffffb4" -->
        <el-form
          label-width="5rem"
          :model="formData"
          ref="formRef"
          :rules="formDataRules"
        >
          <el-form-item label="素材分类">
            <el-select
              :suffix-icon="`CaretBottom`"
              disabled
              size="large"
              v-model="formData.res_type"
              placeholder="素材分类"
              filterable
              clearable
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="素材文件">
            <el-upload
              ref="upload"
              class="upload-file"
              :limit="1"
              :on-exceed="handleExceed"
              :auto-upload="false"
              :before-upload="handleBeforeUpload"
              :on-change="handleFileChange"
              :on-progress="handleFileProgress"
            >
              <!-- action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" -->
              <!-- :http-request="uploadFile" -->
              <template #trigger>
                <div class="btn primary-btn">上传</div>
              </template>
            </el-upload>
            <span v-if="activeTab == 20" class="tips">
              注：上传课程封面时--建议尺寸为212x112px
            </span>
          </el-form-item>
          <el-form-item
            label="文件名称"
            v-if="activeTab == 10 && dialog.type == 'edit'"
          >
            <span class="tips">{{
              formData.res_info?.video_name || "- -"
            }}</span>
          </el-form-item>
          <el-form-item label="封面预览" v-if="activeTab == 20">
            <img v-if="imgUrl" :src="imgUrl" class="single-uploader__image" />
            <span class="tips">*文件保存成功后看预览封面</span>
          </el-form-item>

          <el-form-item label="名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入素材名称"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="素材分组">
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="formData.group_ids"
              placeholder="素材分组"
              multiple
              filterable
              clearable
            >
              <el-option
                v-for="item in groupOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div
          class="dialog-footer"
          :class="{ 'footer-mask': fileLoading || uploading }"
          :style="{
            opacity: fileLoading ? 0.8 : 1,
            pointerEvents: fileLoading ? 'none' : 'auto',
          }"
        >
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">保 存</div>
        </div>
      </template>
    </el-dialog>

    <!-- 预览素材弹窗 -->
    <el-dialog
      class="material-preview-dialog"
      v-model="previewDialog.visible"
      :title="previewDialog.title"
      :width="previewDialog.width"
      append-to-body
      @close="closePreviewDialog"
    >
      <div class="dialog-body">
        <MaterialPreview
          ref="material-preview"
          v-model:previewResInfo="previewResInfo"
          v-model:id="previewResInfo.id"
          v-model:type="queryParams.res_type"
          v-model:visible="previewDialog.visible"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { genFileId } from "element-plus";
import {
  getResources,
  getResGroups,
  getResourcesDetail,
  addResources,
  deleteResources,
  updateResources,
} from "@/api/resource";
import type {
  TabPaneName,
  UploadInstance,
  UploadProps,
  UploadRawFile,
} from "element-plus";
import MaterialPreview from "./components/MaterialPreview.vue";
import { parseTime, secondsToMinutes } from "@/utils";
import { obsUpload } from "@/utils/obs";
import { vodUpload, uploadProgressCallBack } from "@/utils/vod.js";
import { isString } from "@/utils/validate";
import { checkUserPermission } from "@/utils/auth";
// import { postVodAsset, getVodAsset } from "@/api/file";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MaterialLibrary",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const { t } = useI18n();
const activeTab = ref(10);
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  res_type: activeTab.value,
  gid: "",
});
const groupOptions = ref<any>([]);
const typeOptions = ref<any>([
  {
    value: 10,
    label: "视频",
  },
  {
    value: 20,
    label: "图片",
  },
  // {
  //   value: 30,
  //   label: "文档",
  // },
  // {
  //   value: 40,
  //   label: "音频",
  // },
  // {
  //   value: 50,
  //   label: "其他",
  // },
]);
const typeMap = reactive<any>({
  10: { value: 10, label: "视频", dict: "video" },
  20: { value: 20, label: "图片", dict: "image" },
  30: { value: 30, label: "文档", dict: "document" },
  40: { value: 40, label: "音频", dict: "audio" },
  50: { value: 50, label: "其他", dict: "other" },
  // add more status mappings as needed
});
const loading = ref(false);

const total = ref(0); // 数据总数
const tableData = ref([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "25%",
  title: "详情",
});

// 用户表单数据
const formData = reactive<any>({
  group_ids: [], //	素材分组id
  group_seq: "", //		该资源在素材分组中的排序。当group_ids 存在时有效
  name: "", //		素材名字
  thumb: "", //	封面图
  res_type: "", //		素材类型，10-video，20-image，30-doc，40-音频，50-其他
  res_info: {}, //	素材信息，素材上传到华为云后的信息
});
const formDataRules = reactive<any>({
  name: [{ required: true, message: "请输入素材名称", trigger: "blur" }],
});
const formRef = ref(ElForm);
const rowId = ref<any>();
const imgUrl = ref();

const fileData = ref<any>();
const fileLoading = ref<any>(false);
const upload = ref<UploadInstance>();
const uploadPercent = ref<any>(0);
const uploading = ref<any>(false);
const progressTimer = ref<any>(null);
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });

const previewDialog = reactive({
  visible: false,
  type: "preview",
  width: "65%",
  title: "素材预览",
});
const previewResInfo = reactive<any>({});

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 1);
});

watch(
  [uploading, () => dialog.visible],
  (val) => {
    // console.log("val", val, val[0], "dialog.visible", dialog.visible);
    uploadPercent.value = 0;
    if (val[0] && dialog.visible) {
      progressTimer.value = setInterval(() => {
        uploadPercent.value = uploadProgressCallBack() * 1;
        console.log("uploadPercent.value", uploadPercent.value);
      }, 500);
    } else if (!dialog.visible || !val[0]) {
      console.log("progressTimer--clearInterval");
      clearInterval(progressTimer.value);
    }
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  formData.res_type = activeTab.value;
  getGroups();
  getData();
});

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.res_type,
    gid: queryParams.gid || undefined,
  };
  getResources(params)
    .then((res: any) => {
      tableData.value = res.data.resources.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.size = (item.size / 1024 / 1024)?.toFixed(2);
        if (item.res_type !== 10) {
          item.size = item.res_info.size
            ? (item.res_info.size / 1024 / 1024)?.toFixed(2)
            : (item.size / 1024 / 1024)?.toFixed(2);
        }
        return item;
      });
      total.value = res.total;
      loading.value = false;
    })
    .catch((e) => {
      loading.value = false;
    });
}

function getGroups() {
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getResGroups(params).then((res: any) => {
    groupOptions.value = res.data.groups.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  });
}
function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增素材";
  dialog.type = "add";
  userStore.judgeObsAndVodExpired(); //判断OBS\VOD key 过期
}
function onRowClick(type: any, row: any) {
  formData.res_type = activeTab.value;
  if (type === "edit") {
    userStore.judgeObsAndVodExpired(); //判断OBS\VOD key 过期
    dialog.visible = true;
    // fileLoading.value = true;
    // uploading.value = true;
    dialog.title = "修改素材";
    dialog.type = "edit";
    Object.assign(formData, row);
    formData.group_ids =
      row.groups.length > 0
        ? row.groups.map((item: any) => {
            return item.id;
          })
        : undefined;
    rowId.value = row.id;
    const res_info = isString(row.res_info)
      ? JSON.parse(row.res_info)
      : row.res_info;
    if (formData.res_type === 20 && res_info) {
      // console.log("res_info", res_info);
      imgUrl.value = res_info.url;
    }
    // console.log("formData", formData);
  }
  if (type === "preview") {
    Object.assign(previewResInfo, row.res_info, {
      created_at: row.created_at,
      res_type: row.res_type,
      size: row.size,
    });
    if (row.res_type == 10) {
      previewResInfo.duration = secondsToMinutes(row.res_info.duration);
    }
    // console.log("previewResInfo", row.size, previewResInfo);
    previewDialog.title =
      "预览" + typeMap[activeTab.value].label + "-" + row.name;
    previewDialog.visible = true;
  }
}

function handleTab(name: TabPaneName) {
  queryParams.pageNum = 1;
  formData.res_type = activeTab.value;
  queryParams.res_type = activeTab.value;
  getData();
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closePreviewDialog() {
  previewDialog.visible = false;
  Object.keys(previewResInfo).forEach((key) => delete previewResInfo[key]);
}
function closeDialog() {
  dialog.visible = false;
  fileLoading.value = false;
  fileData.value = null;
  imgUrl.value = "";
  upload.value?.clearFiles(); // 清空file文件
  formRef.value.resetFields();
  formRef.value.clearValidate();
  Object.assign(formData, {
    group_ids: [], //	素材分组id
    group_seq: "", //		该资源在素材分组中的排序。当group_ids 存在时有效
    name: "", //		素材名字
    thumb: "", //	封面图
    res_type: activeTab.value, //		素材类型，10-video，20-image，30-doc，40-音频，50-其他
    res_info: {}, //	素材信息async async async ，素材上传到华为云后的信息
  });
}
async function handleSubmit() {
  // 先上传文件获取info再保存
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      let res_info: any = {};
      if (!fileData.value && dialog.type !== "edit") {
        ElMessage.warning("请选择文件上传");
        return;
      }
      if (fileData.value) {
        fileLoading.value = true;
        res_info = await uploadFile(fileData.value);
        if (res_info == "error") {
          fileLoading.value = false;
          return;
        }
      } else if (!fileData.value && formData.res_info) {
        res_info = formData.res_info;
      }

      const data: any = {
        group_ids: formData.group_ids, //	素材分组id
        group_seq: formData.group_seq || undefined, //		该资源在素材分组中的排序。当group_ids 存在时有效
        name: formData.name, //		素材名字
        thumb: formData.thumb || undefined, //	封面图
        res_type: formData.res_type, //		素材类型，10-video，20-image，30-doc，40-音频，50-其他
        res_info: res_info, //	素材信息，素材上传到华为云后的信息
      };
      if (dialog.type === "add") {
        await addResources(data)
          .then((res: any) => {
            if (res.status == 200) {
              uploading.value = false;
              ElMessage.success({
                message: "新增成功!",
              });
              closeDialog();
              getData();
            }
          })
          .catch((err: any) => {
            uploading.value = false;
          });
      }
      if (dialog.type === "edit") {
        await updateResources(rowId.value, data)
          .then((res: any) => {
            if (res.status == 200) {
              uploading.value = false;
              ElMessage.success({
                message: "修改成功!",
              });
              closeDialog();
              getData();
            }
          })
          .catch((err: any) => {
            uploading.value = false;
          });
      }
    }
  });
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除素材，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteResources(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}

function handleFileChange(file: any, res: any) {
  if (formData.res_type == 10) {
    if (file.raw.type != "video/mp4") {
      ElMessage.warning("只能上传MP4");
      upload.value?.clearFiles();
      return;
    }
  }
  if (formData.res_type == 20) {
    if (file.raw.type != "image/jpeg" && file.raw.type != "image/png") {
      ElMessage.warning("只能上传图片");
      upload.value?.clearFiles();
      return;
    }
  }
  fileData.value = file || null;
}
function handleFileProgress(file: any, res: any) {}
function handleBeforeUpload(file: any) {}
async function uploadFile(file: any) {
  fileLoading.value = true;
  uploading.value = true;
  try {
    let uploadRes: any = null;

    if (formData.res_type == 10) {
      uploadRes = await vodUpload(file.raw, formData.name);
      fileLoading.value = false;
    } else if (formData.res_type == 20) {
      // file.raw.name
      const objKey = "image/platform/" + file.raw.name;
      uploadRes = await obsUpload(file.raw, objKey);
      uploadRes.size = file.raw.size;
      fileLoading.value = false;
    } else {
      const objKey =
        typeMap[formData.res_type].dict + "/platform/" + file.raw.name;
      uploadRes = await obsUpload(file.raw, objKey);
      uploadRes.size = file.raw.size;
      fileLoading.value = false;
    }
    return uploadRes;
  } catch (e) {
    console.log(e);
    ElMessage.error("上传失败，请尝试刷新重试");
    return "error";
  }
}
const handleExceed: UploadProps["onExceed"] = (files) => {
  upload.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  upload.value!.handleStart(file);
};

function submitUpload() {
  upload.value!.submit();
}
</script>

<style scoped lang="scss">
.material-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 170px);
    height: calc(100% - 240px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.material-dialog {
  position: relative;

  .dialog-body {
    // height: 300px;
    position: relative;
  }

  .upload-file {
    width: 100%;

    .btn {
      width: 98px !important;
      height: 38px !important;
    }
  }

  .single-uploader__image {
    width: 190px;
    height: 150px;
    object-fit: cover;
  }

  .upload-progress {
    position: absolute;
    top: 40%;
    left: 50%;
    z-index: 5;
    width: 70%;
    margin: 0 auto;
    transform: translate(-50%, -50%);
  }

  .footer-mask {
    position: relative;

    &::after {
      position: absolute;
      z-index: 3;
      width: 100%;
      height: 100%;
      content: "";
      background: #ffffffb4;
    }
  }

  .loading-mask {
    position: absolute;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 95%;
    height: 100%;
    color: #00918c;
    background: #ffffffb4;
  }
}
</style>

<style lang="scss">
.material-dialog {
  .el-dialog__body {
    position: relative !important;
  }
}
</style>
