/*! For license information please see esdk-obs-browserjs.3.24.3.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.ObsClient=e():t.ObsClient=e()}(self,(function(){return function(){var t,e,r={5250:function(t,e,r){"use strict";r(1377),r(946),r(4667),r(1631),r(5490),r(2430),r(2547),r(7281),r(7063),r(2487),r(1765),r(7760),r(6349),r(5846)},6783:function(t,e,r){t.exports=r(3761)},5326:function(t,e,r){"use strict";var o=r(9082),n=r(6168),i=r(8446),a=r(3220),s=r(8829),c=r(2510),u=r(2084),l=r(8353),p=r(5754),f=r(1566);t.exports=function(t){return new Promise((function(e,r){var d,h=t.data,m=t.headers,y=t.responseType;function g(){t.cancelToken&&t.cancelToken.unsubscribe(d),t.signal&&t.signal.removeEventListener("abort",d)}o.isFormData(h)&&delete m["Content-Type"];var v=new XMLHttpRequest;if(t.auth){var b=t.auth.username||"",A=t.auth.password?unescape(encodeURIComponent(t.auth.password)):"";m.Authorization="Basic "+btoa(b+":"+A)}var x=s(t.baseURL,t.url);function w(){if(v){var o="getAllResponseHeaders"in v?c(v.getAllResponseHeaders()):null,i={data:y&&"text"!==y&&"json"!==y?v.response:v.responseText,status:v.status,statusText:v.statusText,headers:o,config:t,request:v};n((function(t){e(t),g()}),(function(t){r(t),g()}),i),v=null}}if(v.open(t.method.toUpperCase(),a(x,t.params,t.paramsSerializer),!0),v.timeout=t.timeout,"onloadend"in v?v.onloadend=w:v.onreadystatechange=function(){v&&4===v.readyState&&(0!==v.status||v.responseURL&&0===v.responseURL.indexOf("file:"))&&setTimeout(w)},v.onabort=function(){v&&(r(l("Request aborted",t,"ECONNABORTED",v)),v=null)},v.onerror=function(){r(l("Network Error",t,null,v)),v=null},v.ontimeout=function(){var e=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded",o=t.transitional||p;t.timeoutErrorMessage&&(e=t.timeoutErrorMessage),r(l(e,t,o.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",v)),v=null},o.isStandardBrowserEnv()){var S=(t.withCredentials||u(x))&&t.xsrfCookieName?i.read(t.xsrfCookieName):void 0;S&&(m[t.xsrfHeaderName]=S)}"setRequestHeader"in v&&o.forEach(m,(function(t,e){void 0===h&&"content-type"===e.toLowerCase()?delete m[e]:v.setRequestHeader(e,t)})),o.isUndefined(t.withCredentials)||(v.withCredentials=!!t.withCredentials),y&&"json"!==y&&(v.responseType=t.responseType),"function"==typeof t.onDownloadProgress&&v.addEventListener("progress",t.onDownloadProgress),"function"==typeof t.onUploadProgress&&v.upload&&v.upload.addEventListener("progress",t.onUploadProgress),(t.cancelToken||t.signal)&&(d=function(t){v&&(r(!t||t&&t.type?new f("canceled"):t),v.abort(),v=null)},t.cancelToken&&t.cancelToken.subscribe(d),t.signal&&(t.signal.aborted?d():t.signal.addEventListener("abort",d))),h||(h=null),v.send(h)}))}},3761:function(t,e,r){"use strict";var o=r(9082),n=r(4694),i=r(4769),a=r(5433),s=function t(e){var r=new i(e),s=n(i.prototype.request,r);return o.extend(s,i.prototype,r),o.extend(s,r),s.create=function(r){return t(a(e,r))},s}(r(1378));s.Axios=i,s.Cancel=r(1566),s.CancelToken=r(7557),s.isCancel=r(7250),s.VERSION=r(8195).version,s.all=function(t){return Promise.all(t)},s.spread=r(142),s.isAxiosError=r(4993),t.exports=s,t.exports.default=s},1566:function(t){"use strict";function e(t){this.message=t}e.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},e.prototype.__CANCEL__=!0,t.exports=e},7557:function(t,e,r){"use strict";var o=r(1566);function n(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");var e;this.promise=new Promise((function(t){e=t}));var r=this;this.promise.then((function(t){if(r._listeners){var e,o=r._listeners.length;for(e=0;e<o;e++)r._listeners[e](t);r._listeners=null}})),this.promise.then=function(t){var e,o=new Promise((function(t){r.subscribe(t),e=t})).then(t);return o.cancel=function(){r.unsubscribe(e)},o},t((function(t){r.reason||(r.reason=new o(t),e(r.reason))}))}n.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},n.prototype.subscribe=function(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]},n.prototype.unsubscribe=function(t){if(this._listeners){var e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}},n.source=function(){var t;return{token:new n((function(e){t=e})),cancel:t}},t.exports=n},7250:function(t){"use strict";t.exports=function(t){return!(!t||!t.__CANCEL__)}},4769:function(t,e,r){"use strict";var o=r(9082),n=r(3220),i=r(5041),a=r(1928),s=r(5433),c=r(7763),u=c.validators;function l(t){this.defaults=t,this.interceptors={request:new i,response:new i}}l.prototype.request=function(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var r=e.transitional;void 0!==r&&c.assertOptions(r,{silentJSONParsing:u.transitional(u.boolean),forcedJSONParsing:u.transitional(u.boolean),clarifyTimeoutError:u.transitional(u.boolean)},!1);var o=[],n=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(n=n&&t.synchronous,o.unshift(t.fulfilled,t.rejected))}));var i,l=[];if(this.interceptors.response.forEach((function(t){l.push(t.fulfilled,t.rejected)})),!n){var p=[a,void 0];for(Array.prototype.unshift.apply(p,o),p=p.concat(l),i=Promise.resolve(e);p.length;)i=i.then(p.shift(),p.shift());return i}for(var f=e;o.length;){var d=o.shift(),h=o.shift();try{f=d(f)}catch(t){h(t);break}}try{i=a(f)}catch(t){return Promise.reject(t)}for(;l.length;)i=i.then(l.shift(),l.shift());return i},l.prototype.getUri=function(t){return t=s(this.defaults,t),n(t.url,t.params,t.paramsSerializer).replace(/^\?/,"")},o.forEach(["delete","get","head","options"],(function(t){l.prototype[t]=function(e,r){return this.request(s(r||{},{method:t,url:e,data:(r||{}).data}))}})),o.forEach(["post","put","patch"],(function(t){l.prototype[t]=function(e,r,o){return this.request(s(o||{},{method:t,url:e,data:r}))}})),t.exports=l},5041:function(t,e,r){"use strict";var o=r(9082);function n(){this.handlers=[]}n.prototype.use=function(t,e,r){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1},n.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)},n.prototype.forEach=function(t){o.forEach(this.handlers,(function(e){null!==e&&t(e)}))},t.exports=n},8829:function(t,e,r){"use strict";var o=r(5131),n=r(4686);t.exports=function(t,e){return t&&!o(e)?n(t,e):e}},8353:function(t,e,r){"use strict";var o=r(3311);t.exports=function(t,e,r,n,i){var a=new Error(t);return o(a,e,r,n,i)}},1928:function(t,e,r){"use strict";var o=r(9082),n=r(9623),i=r(7250),a=r(1378),s=r(1566);function c(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new s("canceled")}t.exports=function(t){return c(t),t.headers=t.headers||{},t.data=n.call(t,t.data,t.headers,t.transformRequest),t.headers=o.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),o.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||a.adapter)(t).then((function(e){return c(t),e.data=n.call(t,e.data,e.headers,t.transformResponse),e}),(function(e){return i(e)||(c(t),e&&e.response&&(e.response.data=n.call(t,e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},3311:function(t){"use strict";t.exports=function(t,e,r,o,n){return t.config=e,r&&(t.code=r),t.request=o,t.response=n,t.isAxiosError=!0,t.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},t}},5433:function(t,e,r){"use strict";var o=r(9082);t.exports=function(t,e){e=e||{};var r={};function n(t,e){return o.isPlainObject(t)&&o.isPlainObject(e)?o.merge(t,e):o.isPlainObject(e)?o.merge({},e):o.isArray(e)?e.slice():e}function i(r){return o.isUndefined(e[r])?o.isUndefined(t[r])?void 0:n(void 0,t[r]):n(t[r],e[r])}function a(t){if(!o.isUndefined(e[t]))return n(void 0,e[t])}function s(r){return o.isUndefined(e[r])?o.isUndefined(t[r])?void 0:n(void 0,t[r]):n(void 0,e[r])}function c(r){return r in e?n(t[r],e[r]):r in t?n(void 0,t[r]):void 0}var u={url:a,method:a,data:a,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:c};return o.forEach(Object.keys(t).concat(Object.keys(e)),(function(t){var e=u[t]||i,n=e(t);o.isUndefined(n)&&e!==c||(r[t]=n)})),r}},6168:function(t,e,r){"use strict";var o=r(8353);t.exports=function(t,e,r){var n=r.config.validateStatus;r.status&&n&&!n(r.status)?e(o("Request failed with status code "+r.status,r.config,null,r.request,r)):t(r)}},9623:function(t,e,r){"use strict";var o=r(9082),n=r(1378);t.exports=function(t,e,r){var i=this||n;return o.forEach(r,(function(r){t=r.call(i,t,e)})),t}},1378:function(t,e,r){"use strict";var o=r(9082),n=r(9872),i=r(3311),a=r(5754),s={"Content-Type":"application/x-www-form-urlencoded"};function c(t,e){!o.isUndefined(t)&&o.isUndefined(t["Content-Type"])&&(t["Content-Type"]=e)}var u,l={transitional:a,adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(u=r(5326)),u),transformRequest:[function(t,e){return n(e,"Accept"),n(e,"Content-Type"),o.isFormData(t)||o.isArrayBuffer(t)||o.isBuffer(t)||o.isStream(t)||o.isFile(t)||o.isBlob(t)?t:o.isArrayBufferView(t)?t.buffer:o.isURLSearchParams(t)?(c(e,"application/x-www-form-urlencoded;charset=utf-8"),t.toString()):o.isObject(t)||e&&"application/json"===e["Content-Type"]?(c(e,"application/json"),function(t,e,r){if(o.isString(t))try{return(0,JSON.parse)(t),o.trim(t)}catch(t){if("SyntaxError"!==t.name)throw t}return(0,JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){var e=this.transitional||l.transitional,r=e&&e.silentJSONParsing,n=e&&e.forcedJSONParsing,a=!r&&"json"===this.responseType;if(a||n&&o.isString(t)&&t.length)try{return JSON.parse(t)}catch(t){if(a){if("SyntaxError"===t.name)throw i(t,this,"E_JSON_PARSE");throw t}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};o.forEach(["delete","get","head"],(function(t){l.headers[t]={}})),o.forEach(["post","put","patch"],(function(t){l.headers[t]=o.merge(s)})),t.exports=l},5754:function(t){"use strict";t.exports={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},8195:function(t){t.exports={version:"0.26.1"}},4694:function(t){"use strict";t.exports=function(t,e){return function(){for(var r=new Array(arguments.length),o=0;o<r.length;o++)r[o]=arguments[o];return t.apply(e,r)}}},3220:function(t,e,r){"use strict";var o=r(9082);function n(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(t,e,r){if(!e)return t;var i;if(r)i=r(e);else if(o.isURLSearchParams(e))i=e.toString();else{var a=[];o.forEach(e,(function(t,e){null!=t&&(o.isArray(t)?e+="[]":t=[t],o.forEach(t,(function(t){o.isDate(t)?t=t.toISOString():o.isObject(t)&&(t=JSON.stringify(t)),a.push(n(e)+"="+n(t))})))})),i=a.join("&")}if(i){var s=t.indexOf("#");-1!==s&&(t=t.slice(0,s)),t+=(-1===t.indexOf("?")?"?":"&")+i}return t}},4686:function(t){"use strict";t.exports=function(t,e){return e?t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,""):t}},8446:function(t,e,r){"use strict";var o=r(9082);t.exports=o.isStandardBrowserEnv()?{write:function(t,e,r,n,i,a){var s=[];s.push(t+"="+encodeURIComponent(e)),o.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),o.isString(n)&&s.push("path="+n),o.isString(i)&&s.push("domain="+i),!0===a&&s.push("secure"),document.cookie=s.join("; ")},read:function(t){var e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove:function(t){this.write(t,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},5131:function(t){"use strict";t.exports=function(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}},4993:function(t,e,r){"use strict";var o=r(9082);t.exports=function(t){return o.isObject(t)&&!0===t.isAxiosError}},2084:function(t,e,r){"use strict";var o=r(9082);t.exports=o.isStandardBrowserEnv()?function(){var t,e=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");function n(t){var o=t;return e&&(r.setAttribute("href",o),o=r.href),r.setAttribute("href",o),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:"/"===r.pathname.charAt(0)?r.pathname:"/"+r.pathname}}return t=n(window.location.href),function(e){var r=o.isString(e)?n(e):e;return r.protocol===t.protocol&&r.host===t.host}}():function(){return!0}},9872:function(t,e,r){"use strict";var o=r(9082);t.exports=function(t,e){o.forEach(t,(function(r,o){o!==e&&o.toUpperCase()===e.toUpperCase()&&(t[e]=r,delete t[o])}))}},2510:function(t,e,r){"use strict";var o=r(9082),n=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];t.exports=function(t){var e,r,i,a={};return t?(o.forEach(t.split("\n"),(function(t){if(i=t.indexOf(":"),e=o.trim(t.substr(0,i)).toLowerCase(),r=o.trim(t.substr(i+1)),e){if(a[e]&&n.indexOf(e)>=0)return;a[e]="set-cookie"===e?(a[e]?a[e]:[]).concat([r]):a[e]?a[e]+", "+r:r}})),a):a}},142:function(t){"use strict";t.exports=function(t){return function(e){return t.apply(null,e)}}},7763:function(t,e,r){"use strict";function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=r(8195).version,i={};["object","boolean","number","function","string","symbol"].forEach((function(t,e){i[t]=function(r){return o(r)===t||"a"+(e<1?"n ":" ")+t}}));var a={};i.transitional=function(t,e,r){function o(t,e){return"[Axios v"+n+"] Transitional option '"+t+"'"+e+(r?". "+r:"")}return function(r,n,i){if(!1===t)throw new Error(o(n," has been removed"+(e?" in "+e:"")));return e&&!a[n]&&(a[n]=!0,console.warn(o(n," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(r,n,i)}},t.exports={assertOptions:function(t,e,r){if("object"!==o(t))throw new TypeError("options must be an object");for(var n=Object.keys(t),i=n.length;i-- >0;){var a=n[i],s=e[a];if(s){var c=t[a],u=void 0===c||s(c,a,t);if(!0!==u)throw new TypeError("option "+a+" must be "+u)}else if(!0!==r)throw Error("Unknown option "+a)}},validators:i}},9082:function(t,e,r){"use strict";function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=r(4694),i=Object.prototype.toString;function a(t){return Array.isArray(t)}function s(t){return void 0===t}function c(t){return"[object ArrayBuffer]"===i.call(t)}function u(t){return null!==t&&"object"===o(t)}function l(t){if("[object Object]"!==i.call(t))return!1;var e=Object.getPrototypeOf(t);return null===e||e===Object.prototype}function p(t){return"[object Function]"===i.call(t)}function f(t,e){if(null!=t)if("object"!==o(t)&&(t=[t]),a(t))for(var r=0,n=t.length;r<n;r++)e.call(null,t[r],r,t);else for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.call(null,t[i],i,t)}t.exports={isArray:a,isArrayBuffer:c,isBuffer:function(t){return null!==t&&!s(t)&&null!==t.constructor&&!s(t.constructor)&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)},isFormData:function(t){return"[object FormData]"===i.call(t)},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&c(t.buffer)},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isObject:u,isPlainObject:l,isUndefined:s,isDate:function(t){return"[object Date]"===i.call(t)},isFile:function(t){return"[object File]"===i.call(t)},isBlob:function(t){return"[object Blob]"===i.call(t)},isFunction:p,isStream:function(t){return u(t)&&p(t.pipe)},isURLSearchParams:function(t){return"[object URLSearchParams]"===i.call(t)},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:f,merge:function t(){var e={};function r(r,o){l(e[o])&&l(r)?e[o]=t(e[o],r):l(r)?e[o]=t({},r):a(r)?e[o]=r.slice():e[o]=r}for(var o=0,n=arguments.length;o<n;o++)f(arguments[o],r);return e},extend:function(t,e,r){return f(e,(function(e,o){t[o]=r&&"function"==typeof e?n(e,r):e})),t},trim:function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")},stripBOM:function(t){return 65279===t.charCodeAt(0)&&(t=t.slice(1)),t}}},3264:function(t,e,r){var o;!function(n){"use strict";function i(t,e){var r=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(r>>16)<<16|65535&r}function a(t,e,r,o,n,a){return i((s=i(i(e,t),i(o,a)))<<(c=n)|s>>>32-c,r);var s,c}function s(t,e,r,o,n,i,s){return a(e&r|~e&o,t,e,n,i,s)}function c(t,e,r,o,n,i,s){return a(e&o|r&~o,t,e,n,i,s)}function u(t,e,r,o,n,i,s){return a(e^r^o,t,e,n,i,s)}function l(t,e,r,o,n,i,s){return a(r^(e|~o),t,e,n,i,s)}function p(t,e){var r,o,n,a,p;t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;var f=1732584193,d=-271733879,h=-1732584194,m=271733878;for(r=0;r<t.length;r+=16)o=f,n=d,a=h,p=m,f=s(f,d,h,m,t[r],7,-680876936),m=s(m,f,d,h,t[r+1],12,-389564586),h=s(h,m,f,d,t[r+2],17,606105819),d=s(d,h,m,f,t[r+3],22,-1044525330),f=s(f,d,h,m,t[r+4],7,-176418897),m=s(m,f,d,h,t[r+5],12,1200080426),h=s(h,m,f,d,t[r+6],17,-1473231341),d=s(d,h,m,f,t[r+7],22,-45705983),f=s(f,d,h,m,t[r+8],7,1770035416),m=s(m,f,d,h,t[r+9],12,-1958414417),h=s(h,m,f,d,t[r+10],17,-42063),d=s(d,h,m,f,t[r+11],22,-1990404162),f=s(f,d,h,m,t[r+12],7,1804603682),m=s(m,f,d,h,t[r+13],12,-40341101),h=s(h,m,f,d,t[r+14],17,-1502002290),f=c(f,d=s(d,h,m,f,t[r+15],22,1236535329),h,m,t[r+1],5,-165796510),m=c(m,f,d,h,t[r+6],9,-1069501632),h=c(h,m,f,d,t[r+11],14,643717713),d=c(d,h,m,f,t[r],20,-373897302),f=c(f,d,h,m,t[r+5],5,-701558691),m=c(m,f,d,h,t[r+10],9,38016083),h=c(h,m,f,d,t[r+15],14,-660478335),d=c(d,h,m,f,t[r+4],20,-405537848),f=c(f,d,h,m,t[r+9],5,568446438),m=c(m,f,d,h,t[r+14],9,-1019803690),h=c(h,m,f,d,t[r+3],14,-187363961),d=c(d,h,m,f,t[r+8],20,1163531501),f=c(f,d,h,m,t[r+13],5,-1444681467),m=c(m,f,d,h,t[r+2],9,-51403784),h=c(h,m,f,d,t[r+7],14,1735328473),f=u(f,d=c(d,h,m,f,t[r+12],20,-1926607734),h,m,t[r+5],4,-378558),m=u(m,f,d,h,t[r+8],11,-2022574463),h=u(h,m,f,d,t[r+11],16,1839030562),d=u(d,h,m,f,t[r+14],23,-35309556),f=u(f,d,h,m,t[r+1],4,-1530992060),m=u(m,f,d,h,t[r+4],11,1272893353),h=u(h,m,f,d,t[r+7],16,-155497632),d=u(d,h,m,f,t[r+10],23,-1094730640),f=u(f,d,h,m,t[r+13],4,681279174),m=u(m,f,d,h,t[r],11,-358537222),h=u(h,m,f,d,t[r+3],16,-722521979),d=u(d,h,m,f,t[r+6],23,76029189),f=u(f,d,h,m,t[r+9],4,-640364487),m=u(m,f,d,h,t[r+12],11,-421815835),h=u(h,m,f,d,t[r+15],16,530742520),f=l(f,d=u(d,h,m,f,t[r+2],23,-995338651),h,m,t[r],6,-198630844),m=l(m,f,d,h,t[r+7],10,1126891415),h=l(h,m,f,d,t[r+14],15,-1416354905),d=l(d,h,m,f,t[r+5],21,-57434055),f=l(f,d,h,m,t[r+12],6,1700485571),m=l(m,f,d,h,t[r+3],10,-1894986606),h=l(h,m,f,d,t[r+10],15,-1051523),d=l(d,h,m,f,t[r+1],21,-2054922799),f=l(f,d,h,m,t[r+8],6,1873313359),m=l(m,f,d,h,t[r+15],10,-30611744),h=l(h,m,f,d,t[r+6],15,-1560198380),d=l(d,h,m,f,t[r+13],21,1309151649),f=l(f,d,h,m,t[r+4],6,-145523070),m=l(m,f,d,h,t[r+11],10,-1120210379),h=l(h,m,f,d,t[r+2],15,718787259),d=l(d,h,m,f,t[r+9],21,-343485551),f=i(f,o),d=i(d,n),h=i(h,a),m=i(m,p);return[f,d,h,m]}function f(t){var e,r="",o=32*t.length;for(e=0;e<o;e+=8)r+=String.fromCharCode(t[e>>5]>>>e%32&255);return r}function d(t){var e,r=[];for(r[(t.length>>2)-1]=void 0,e=0;e<r.length;e+=1)r[e]=0;var o=8*t.length;for(e=0;e<o;e+=8)r[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return r}function h(t){var e,r,o="0123456789abcdef",n="";for(r=0;r<t.length;r+=1)e=t.charCodeAt(r),n+=o.charAt(e>>>4&15)+o.charAt(15&e);return n}function m(t){return unescape(encodeURIComponent(t))}function y(t){return function(t){return f(p(d(t),8*t.length))}(m(t))}function g(t,e){return function(t,e){var r,o,n=d(t),i=[],a=[];for(i[15]=a[15]=void 0,n.length>16&&(n=p(n,8*t.length)),r=0;r<16;r+=1)i[r]=909522486^n[r],a[r]=1549556828^n[r];return o=p(i.concat(d(e)),512+8*e.length),f(p(a.concat(o),640))}(m(t),m(e))}function v(t,e,r){return e?r?g(e,t):h(g(e,t)):r?y(t):h(y(t))}void 0===(o=function(){return v}.call(e,r,e,t))||(t.exports=o)}()},1377:function(t,e,r){r(9920),r(7041),r(5514),r(390),r(8320),r(9458),r(8025),r(5797),r(3050),r(9604),r(6966),r(500),r(8246),r(2031),r(9272),r(4921),r(1174),r(7052),r(5167),r(2051),r(3936),r(1917),r(3098),r(2727),r(1005),r(5075),r(1945),r(3275),r(5368),r(9411),r(1874),r(4676),r(5150),r(863),r(7440),r(2442),r(7537),r(2954),r(3413),r(7006),r(8839),r(2929),r(584),r(6266),r(2681),r(5379),r(3331),r(6118),r(2079),r(9552),r(483),r(2842),r(6510),r(2065),r(771),r(8733),r(8299),r(8194),r(3806),r(4852),r(8279),r(2154),r(7385),r(3569),r(7424),r(9597),r(7535),r(3347),r(4762),r(1871),r(244),r(797),r(7781),r(7043),r(4446),r(596),r(5543),r(4595),r(2413),r(4893),r(6385),r(9488),r(7025),r(8695),r(317),r(7294),r(3259),r(1227),r(61),r(9234),r(687),r(2128),r(6147),r(5796),r(8438),r(5004),r(7910),r(4571),r(47),r(9279),r(6835),r(8018),r(4806),r(4888),r(6466),r(1751),r(5531),r(1383),r(5263),r(3448),r(914),r(7679),r(5057),r(2688),r(2574),r(3957),r(9492),r(975),r(3502),r(4341),r(6904),r(7443),r(8391),r(2e3),r(2805),r(5e3),r(1066),r(6306),r(9735),r(6721),r(5888),r(2978),r(9498),r(9247),r(1646),r(2310),r(2261),r(2630),t.exports=r(1232)},4667:function(t,e,r){r(4800),t.exports=r(1232).Array.flatMap},946:function(t,e,r){r(7297),t.exports=r(1232).Array.includes},1765:function(t,e,r){r(5060),t.exports=r(1232).Object.entries},7063:function(t,e,r){r(6788),t.exports=r(1232).Object.getOwnPropertyDescriptors},2487:function(t,e,r){r(3036),t.exports=r(1232).Object.values},7760:function(t,e,r){"use strict";r(5263),r(2677),t.exports=r(1232).Promise.finally},5490:function(t,e,r){r(4055),t.exports=r(1232).String.padEnd},1631:function(t,e,r){r(1834),t.exports=r(1232).String.padStart},2547:function(t,e,r){r(8343),t.exports=r(1232).String.trimRight},2430:function(t,e,r){r(3372),t.exports=r(1232).String.trimLeft},7281:function(t,e,r){r(1554),t.exports=r(5078).f("asyncIterator")},8910:function(t,e,r){r(9314),t.exports=r(8664).global},7205:function(t){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},669:function(t,e,r){var o=r(3919);t.exports=function(t){if(!o(t))throw TypeError(t+" is not an object!");return t}},8664:function(t){var e=t.exports={version:"2.6.12"};"number"==typeof __e&&(__e=e)},7178:function(t,e,r){var o=r(7205);t.exports=function(t,e,r){if(o(t),void 0===e)return t;switch(r){case 1:return function(r){return t.call(e,r)};case 2:return function(r,o){return t.call(e,r,o)};case 3:return function(r,o,n){return t.call(e,r,o,n)}}return function(){return t.apply(e,arguments)}}},9821:function(t,e,r){t.exports=!r(5714)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},9316:function(t,e,r){var o=r(3919),n=r(8720).document,i=o(n)&&o(n.createElement);t.exports=function(t){return i?n.createElement(t):{}}},5053:function(t,e,r){var o=r(8720),n=r(8664),i=r(7178),a=r(9627),s=r(1811),c="prototype",u=function t(e,r,u){var l,p,f,d=e&t.F,h=e&t.G,m=e&t.S,y=e&t.P,g=e&t.B,v=e&t.W,b=h?n:n[r]||(n[r]={}),A=b[c],x=h?o:m?o[r]:(o[r]||{})[c];for(l in h&&(u=r),u)(p=!d&&x&&void 0!==x[l])&&s(b,l)||(f=p?x[l]:u[l],b[l]=h&&"function"!=typeof x[l]?u[l]:g&&p?i(f,o):v&&x[l]==f?function(t){var e=function(e,r,o){if(this instanceof t){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,r)}return new t(e,r,o)}return t.apply(this,arguments)};return e[c]=t[c],e}(f):y&&"function"==typeof f?i(Function.call,f):f,y&&((b.virtual||(b.virtual={}))[l]=f,e&t.R&&A&&!A[l]&&a(A,l,f)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,t.exports=u},5714:function(t){t.exports=function(t){try{return!!t()}catch(t){return!0}}},8720:function(t){var e=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)},1811:function(t){var e={}.hasOwnProperty;t.exports=function(t,r){return e.call(t,r)}},9627:function(t,e,r){var o=r(4953),n=r(1002);t.exports=r(9821)?function(t,e,r){return o.f(t,e,n(1,r))}:function(t,e,r){return t[e]=r,t}},6:function(t,e,r){t.exports=!r(9821)&&!r(5714)((function(){return 7!=Object.defineProperty(r(9316)("div"),"a",{get:function(){return 7}}).a}))},3919:function(t){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}t.exports=function(t){return"object"===e(t)?null!==t:"function"==typeof t}},4953:function(t,e,r){var o=r(669),n=r(6),i=r(130),a=Object.defineProperty;e.f=r(9821)?Object.defineProperty:function(t,e,r){if(o(t),e=i(e,!0),o(r),n)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(t[e]=r.value),t}},1002:function(t){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},130:function(t,e,r){var o=r(3919);t.exports=function(t,e){if(!o(t))return t;var r,n;if(e&&"function"==typeof(r=t.toString)&&!o(n=r.call(t)))return n;if("function"==typeof(r=t.valueOf)&&!o(n=r.call(t)))return n;if(!e&&"function"==typeof(r=t.toString)&&!o(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")}},9314:function(t,e,r){var o=r(5053);o(o.G,{global:r(8720)})},93:function(t){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},4400:function(t,e,r){var o=r(1831);t.exports=function(t,e){if("number"!=typeof t&&"Number"!=o(t))throw TypeError(e);return+t}},7562:function(t,e,r){var o=r(1572)("unscopables"),n=Array.prototype;null==n[o]&&r(3507)(n,o,{}),t.exports=function(t){n[o][t]=!0}},8258:function(t,e,r){"use strict";var o=r(2742)(!0);t.exports=function(t,e,r){return e+(r?o(t,e).length:1)}},8278:function(t){t.exports=function(t,e,r,o){if(!(t instanceof e)||void 0!==o&&o in t)throw TypeError(r+": incorrect invocation!");return t}},3282:function(t,e,r){var o=r(775);t.exports=function(t){if(!o(t))throw TypeError(t+" is not an object!");return t}},2248:function(t,e,r){"use strict";var o=r(4004),n=r(8075),i=r(9043);t.exports=[].copyWithin||function(t,e){var r=o(this),a=i(r.length),s=n(t,a),c=n(e,a),u=arguments.length>2?arguments[2]:void 0,l=Math.min((void 0===u?a:n(u,a))-c,a-s),p=1;for(c<s&&s<c+l&&(p=-1,c+=l-1,s+=l-1);l-- >0;)c in r?r[s]=r[c]:delete r[s],s+=p,c+=p;return r}},4750:function(t,e,r){"use strict";var o=r(4004),n=r(8075),i=r(9043);t.exports=function(t){for(var e=o(this),r=i(e.length),a=arguments.length,s=n(a>1?arguments[1]:void 0,r),c=a>2?arguments[2]:void 0,u=void 0===c?r:n(c,r);u>s;)e[s++]=t;return e}},5386:function(t,e,r){var o=r(7703),n=r(9043),i=r(8075);t.exports=function(t){return function(e,r,a){var s,c=o(e),u=n(c.length),l=i(a,u);if(t&&r!=r){for(;u>l;)if((s=c[l++])!=s)return!0}else for(;u>l;l++)if((t||l in c)&&c[l]===r)return t||l||0;return!t&&-1}}},1013:function(t,e,r){var o=r(3442),n=r(2275),i=r(4004),a=r(9043),s=r(9478);t.exports=function(t,e){var r=1==t,c=2==t,u=3==t,l=4==t,p=6==t,f=5==t||p,d=e||s;return function(e,s,h){for(var m,y,g=i(e),v=n(g),b=o(s,h,3),A=a(v.length),x=0,w=r?d(e,A):c?d(e,0):void 0;A>x;x++)if((f||x in v)&&(y=b(m=v[x],x,g),t))if(r)w[x]=y;else if(y)switch(t){case 3:return!0;case 5:return m;case 6:return x;case 2:w.push(m)}else if(l)return!1;return p?-1:u||l?l:w}}},5789:function(t,e,r){var o=r(93),n=r(4004),i=r(2275),a=r(9043);t.exports=function(t,e,r,s,c){o(e);var u=n(t),l=i(u),p=a(u.length),f=c?p-1:0,d=c?-1:1;if(r<2)for(;;){if(f in l){s=l[f],f+=d;break}if(f+=d,c?f<0:p<=f)throw TypeError("Reduce of empty array with no initial value")}for(;c?f>=0:p>f;f+=d)f in l&&(s=e(s,l[f],f,u));return s}},1284:function(t,e,r){var o=r(775),n=r(4887),i=r(1572)("species");t.exports=function(t){var e;return n(t)&&("function"!=typeof(e=t.constructor)||e!==Array&&!n(e.prototype)||(e=void 0),o(e)&&null===(e=e[i])&&(e=void 0)),void 0===e?Array:e}},9478:function(t,e,r){var o=r(1284);t.exports=function(t,e){return new(o(t))(e)}},7064:function(t,e,r){"use strict";var o=r(93),n=r(775),i=r(1987),a=[].slice,s={};t.exports=Function.bind||function(t){var e=o(this),r=a.call(arguments,1),c=function o(){var n=r.concat(a.call(arguments));return this instanceof o?function(t,e,r){if(!(e in s)){for(var o=[],n=0;n<e;n++)o[n]="a["+n+"]";s[e]=Function("F,a","return new F("+o.join(",")+")")}return s[e](t,r)}(e,n.length,n):i(e,n,t)};return n(e.prototype)&&(c.prototype=e.prototype),c}},54:function(t,e,r){var o=r(1831),n=r(1572)("toStringTag"),i="Arguments"==o(function(){return arguments}());t.exports=function(t){var e,r,a;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),n))?r:i?o(e):"Object"==(a=o(e))&&"function"==typeof e.callee?"Arguments":a}},1831:function(t){var e={}.toString;t.exports=function(t){return e.call(t).slice(8,-1)}},4327:function(t,e,r){"use strict";var o=r(3473).f,n=r(1889),i=r(1795),a=r(3442),s=r(8278),c=r(712),u=r(6621),l=r(7532),p=r(7348),f=r(6869),d=r(1994).fastKey,h=r(6970),m=f?"_s":"size",y=function(t,e){var r,o=d(e);if("F"!==o)return t._i[o];for(r=t._f;r;r=r.n)if(r.k==e)return r};t.exports={getConstructor:function(t,e,r,u){var l=t((function(t,o){s(t,l,e,"_i"),t._t=e,t._i=n(null),t._f=void 0,t._l=void 0,t[m]=0,null!=o&&c(o,r,t[u],t)}));return i(l.prototype,{clear:function(){for(var t=h(this,e),r=t._i,o=t._f;o;o=o.n)o.r=!0,o.p&&(o.p=o.p.n=void 0),delete r[o.i];t._f=t._l=void 0,t[m]=0},delete:function(t){var r=h(this,e),o=y(r,t);if(o){var n=o.n,i=o.p;delete r._i[o.i],o.r=!0,i&&(i.n=n),n&&(n.p=i),r._f==o&&(r._f=n),r._l==o&&(r._l=i),r[m]--}return!!o},forEach:function(t){h(this,e);for(var r,o=a(t,arguments.length>1?arguments[1]:void 0,3);r=r?r.n:this._f;)for(o(r.v,r.k,this);r&&r.r;)r=r.p},has:function(t){return!!y(h(this,e),t)}}),f&&o(l.prototype,"size",{get:function(){return h(this,e)[m]}}),l},def:function(t,e,r){var o,n,i=y(t,e);return i?i.v=r:(t._l=i={i:n=d(e,!0),k:e,v:r,p:o=t._l,n:void 0,r:!1},t._f||(t._f=i),o&&(o.n=i),t[m]++,"F"!==n&&(t._i[n]=i)),t},getEntry:y,setStrong:function(t,e,r){u(t,e,(function(t,r){this._t=h(t,e),this._k=r,this._l=void 0}),(function(){for(var t=this,e=t._k,r=t._l;r&&r.r;)r=r.p;return t._t&&(t._l=r=r?r.n:t._t._f)?l(0,"keys"==e?r.k:"values"==e?r.v:[r.k,r.v]):(t._t=void 0,l(1))}),r?"entries":"values",!r,!0),p(e)}}},1312:function(t,e,r){"use strict";var o=r(1795),n=r(1994).getWeak,i=r(3282),a=r(775),s=r(8278),c=r(712),u=r(1013),l=r(2923),p=r(6970),f=u(5),d=u(6),h=0,m=function(t){return t._l||(t._l=new y)},y=function(){this.a=[]},g=function(t,e){return f(t.a,(function(t){return t[0]===e}))};y.prototype={get:function(t){var e=g(this,t);if(e)return e[1]},has:function(t){return!!g(this,t)},set:function(t,e){var r=g(this,t);r?r[1]=e:this.a.push([t,e])},delete:function(t){var e=d(this.a,(function(e){return e[0]===t}));return~e&&this.a.splice(e,1),!!~e}},t.exports={getConstructor:function(t,e,r,i){var u=t((function(t,o){s(t,u,e,"_i"),t._t=e,t._i=h++,t._l=void 0,null!=o&&c(o,r,t[i],t)}));return o(u.prototype,{delete:function(t){if(!a(t))return!1;var r=n(t);return!0===r?m(p(this,e)).delete(t):r&&l(r,this._i)&&delete r[this._i]},has:function(t){if(!a(t))return!1;var r=n(t);return!0===r?m(p(this,e)).has(t):r&&l(r,this._i)}}),u},def:function(t,e,r){var o=n(i(e),!0);return!0===o?m(t).set(e,r):o[t._i]=r,t},ufstore:m}},6103:function(t,e,r){"use strict";var o=r(5464),n=r(661),i=r(7117),a=r(1795),s=r(1994),c=r(712),u=r(8278),l=r(775),p=r(189),f=r(8653),d=r(6290),h=r(7278);t.exports=function(t,e,r,m,y,g){var v=o[t],b=v,A=y?"set":"add",x=b&&b.prototype,w={},S=function(t){var e=x[t];i(x,t,"delete"==t||"has"==t?function(t){return!(g&&!l(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return g&&!l(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,r){return e.call(this,0===t?0:t,r),this})};if("function"==typeof b&&(g||x.forEach&&!p((function(){(new b).entries().next()})))){var P=new b,k=P[A](g?{}:-0,1)!=P,C=p((function(){P.has(1)})),O=f((function(t){new b(t)})),E=!g&&p((function(){for(var t=new b,e=5;e--;)t[A](e,e);return!t.has(-0)}));O||((b=e((function(e,r){u(e,b,t);var o=h(new v,e,b);return null!=r&&c(r,y,o[A],o),o}))).prototype=x,x.constructor=b),(C||E)&&(S("delete"),S("has"),y&&S("get")),(E||k)&&S(A),g&&x.clear&&delete x.clear}else b=m.getConstructor(e,t,y,A),a(b.prototype,r),s.NEED=!0;return d(b,t),w[t]=b,n(n.G+n.W+n.F*(b!=v),w),g||m.setStrong(b,t,y),b}},1232:function(t){var e=t.exports={version:"2.6.12"};"number"==typeof __e&&(__e=e)},905:function(t,e,r){"use strict";var o=r(3473),n=r(6194);t.exports=function(t,e,r){e in t?o.f(t,e,n(0,r)):t[e]=r}},3442:function(t,e,r){var o=r(93);t.exports=function(t,e,r){if(o(t),void 0===e)return t;switch(r){case 1:return function(r){return t.call(e,r)};case 2:return function(r,o){return t.call(e,r,o)};case 3:return function(r,o,n){return t.call(e,r,o,n)}}return function(){return t.apply(e,arguments)}}},1675:function(t,e,r){"use strict";var o=r(189),n=Date.prototype.getTime,i=Date.prototype.toISOString,a=function(t){return t>9?t:"0"+t};t.exports=o((function(){return"0385-07-25T07:06:39.999Z"!=i.call(new Date(-50000000000001))}))||!o((function(){i.call(new Date(NaN))}))?function(){if(!isFinite(n.call(this)))throw RangeError("Invalid time value");var t=this,e=t.getUTCFullYear(),r=t.getUTCMilliseconds(),o=e<0?"-":e>9999?"+":"";return o+("00000"+Math.abs(e)).slice(o?-6:-4)+"-"+a(t.getUTCMonth()+1)+"-"+a(t.getUTCDate())+"T"+a(t.getUTCHours())+":"+a(t.getUTCMinutes())+":"+a(t.getUTCSeconds())+"."+(r>99?r:"0"+a(r))+"Z"}:i},8817:function(t,e,r){"use strict";var o=r(3282),n=r(4250),i="number";t.exports=function(t){if("string"!==t&&t!==i&&"default"!==t)throw TypeError("Incorrect hint");return n(o(this),t!=i)}},1794:function(t){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},6869:function(t,e,r){t.exports=!r(189)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},6732:function(t,e,r){var o=r(775),n=r(5464).document,i=o(n)&&o(n.createElement);t.exports=function(t){return i?n.createElement(t):{}}},882:function(t){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},6319:function(t,e,r){var o=r(7177),n=r(7546),i=r(8815);t.exports=function(t){var e=o(t),r=n.f;if(r)for(var a,s=r(t),c=i.f,u=0;s.length>u;)c.call(t,a=s[u++])&&e.push(a);return e}},661:function(t,e,r){var o=r(5464),n=r(1232),i=r(3507),a=r(7117),s=r(3442),c="prototype",u=function t(e,r,u){var l,p,f,d,h=e&t.F,m=e&t.G,y=e&t.P,g=e&t.B,v=m?o:e&t.S?o[r]||(o[r]={}):(o[r]||{})[c],b=m?n:n[r]||(n[r]={}),A=b[c]||(b[c]={});for(l in m&&(u=r),u)f=((p=!h&&v&&void 0!==v[l])?v:u)[l],d=g&&p?s(f,o):y&&"function"==typeof f?s(Function.call,f):f,v&&a(v,l,f,e&t.U),b[l]!=f&&i(b,l,d),y&&A[l]!=f&&(A[l]=f)};o.core=n,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,t.exports=u},9897:function(t,e,r){var o=r(1572)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[o]=!1,!"/./"[t](e)}catch(t){}}return!0}},189:function(t){t.exports=function(t){try{return!!t()}catch(t){return!0}}},4910:function(t,e,r){"use strict";r(8018);var o=r(7117),n=r(3507),i=r(189),a=r(1794),s=r(1572),c=r(6950),u=s("species"),l=!i((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),p=function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2===r.length&&"a"===r[0]&&"b"===r[1]}();t.exports=function(t,e,r){var f=s(t),d=!i((function(){var e={};return e[f]=function(){return 7},7!=""[t](e)})),h=d?!i((function(){var e=!1,r=/a/;return r.exec=function(){return e=!0,null},"split"===t&&(r.constructor={},r.constructor[u]=function(){return r}),r[f](""),!e})):void 0;if(!d||!h||"replace"===t&&!l||"split"===t&&!p){var m=/./[f],y=r(a,f,""[t],(function(t,e,r,o,n){return e.exec===c?d&&!n?{done:!0,value:m.call(e,r,o)}:{done:!0,value:t.call(r,e,o)}:{done:!1}})),g=y[0],v=y[1];o(String.prototype,t,g),n(RegExp.prototype,f,2==e?function(t,e){return v.call(t,this,e)}:function(t){return v.call(t,this)})}}},4492:function(t,e,r){"use strict";var o=r(3282);t.exports=function(){var t=o(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},6472:function(t,e,r){"use strict";var o=r(4887),n=r(775),i=r(9043),a=r(3442),s=r(1572)("isConcatSpreadable");t.exports=function t(e,r,c,u,l,p,f,d){for(var h,m,y=l,g=0,v=!!f&&a(f,d,3);g<u;){if(g in c){if(h=v?v(c[g],g,r):c[g],m=!1,n(h)&&(m=void 0!==(m=h[s])?!!m:o(h)),m&&p>0)y=t(e,r,h,i(h.length),y,p-1)-1;else{if(y>=9007199254740991)throw TypeError();e[y]=h}y++}g++}return y}},712:function(t,e,r){var o=r(3442),n=r(5234),i=r(6246),a=r(3282),s=r(9043),c=r(8388),u={},l={},p=t.exports=function(t,e,r,p,f){var d,h,m,y,g=f?function(){return t}:c(t),v=o(r,p,e?2:1),b=0;if("function"!=typeof g)throw TypeError(t+" is not iterable!");if(i(g)){for(d=s(t.length);d>b;b++)if((y=e?v(a(h=t[b])[0],h[1]):v(t[b]))===u||y===l)return y}else for(m=g.call(t);!(h=m.next()).done;)if((y=n(m,v,h.value,e))===u||y===l)return y};p.BREAK=u,p.RETURN=l},7163:function(t,e,r){t.exports=r(7330)("native-function-to-string",Function.toString)},5464:function(t){var e=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=e)},2923:function(t){var e={}.hasOwnProperty;t.exports=function(t,r){return e.call(t,r)}},3507:function(t,e,r){var o=r(3473),n=r(6194);t.exports=r(6869)?function(t,e,r){return o.f(t,e,n(1,r))}:function(t,e,r){return t[e]=r,t}},4138:function(t,e,r){var o=r(5464).document;t.exports=o&&o.documentElement},5646:function(t,e,r){t.exports=!r(6869)&&!r(189)((function(){return 7!=Object.defineProperty(r(6732)("div"),"a",{get:function(){return 7}}).a}))},7278:function(t,e,r){var o=r(775),n=r(412).set;t.exports=function(t,e,r){var i,a=e.constructor;return a!==r&&"function"==typeof a&&(i=a.prototype)!==r.prototype&&o(i)&&n&&n(t,i),t}},1987:function(t){t.exports=function(t,e,r){var o=void 0===r;switch(e.length){case 0:return o?t():t.call(r);case 1:return o?t(e[0]):t.call(r,e[0]);case 2:return o?t(e[0],e[1]):t.call(r,e[0],e[1]);case 3:return o?t(e[0],e[1],e[2]):t.call(r,e[0],e[1],e[2]);case 4:return o?t(e[0],e[1],e[2],e[3]):t.call(r,e[0],e[1],e[2],e[3])}return t.apply(r,e)}},2275:function(t,e,r){var o=r(1831);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==o(t)?t.split(""):Object(t)}},6246:function(t,e,r){var o=r(100),n=r(1572)("iterator"),i=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||i[n]===t)}},4887:function(t,e,r){var o=r(1831);t.exports=Array.isArray||function(t){return"Array"==o(t)}},5088:function(t,e,r){var o=r(775),n=Math.floor;t.exports=function(t){return!o(t)&&isFinite(t)&&n(t)===t}},775:function(t){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}t.exports=function(t){return"object"===e(t)?null!==t:"function"==typeof t}},2209:function(t,e,r){var o=r(775),n=r(1831),i=r(1572)("match");t.exports=function(t){var e;return o(t)&&(void 0!==(e=t[i])?!!e:"RegExp"==n(t))}},5234:function(t,e,r){var o=r(3282);t.exports=function(t,e,r,n){try{return n?e(o(r)[0],r[1]):e(r)}catch(e){var i=t.return;throw void 0!==i&&o(i.call(t)),e}}},3654:function(t,e,r){"use strict";var o=r(1889),n=r(6194),i=r(6290),a={};r(3507)(a,r(1572)("iterator"),(function(){return this})),t.exports=function(t,e,r){t.prototype=o(a,{next:n(1,r)}),i(t,e+" Iterator")}},6621:function(t,e,r){"use strict";var o=r(68),n=r(661),i=r(7117),a=r(3507),s=r(100),c=r(3654),u=r(6290),l=r(1233),p=r(1572)("iterator"),f=!([].keys&&"next"in[].keys()),d="keys",h="values",m=function(){return this};t.exports=function(t,e,r,y,g,v,b){c(r,e,y);var A,x,w,S=function(t){if(!f&&t in O)return O[t];switch(t){case d:case h:return function(){return new r(this,t)}}return function(){return new r(this,t)}},P=e+" Iterator",k=g==h,C=!1,O=t.prototype,E=O[p]||O["@@iterator"]||g&&O[g],T=E||S(g),B=g?k?S("entries"):T:void 0,M="Array"==e&&O.entries||E;if(M&&(w=l(M.call(new t)))!==Object.prototype&&w.next&&(u(w,P,!0),o||"function"==typeof w[p]||a(w,p,m)),k&&E&&E.name!==h&&(C=!0,T=function(){return E.call(this)}),o&&!b||!f&&!C&&O[p]||a(O,p,T),s[e]=T,s[P]=m,g)if(A={values:k?T:S(h),keys:v?T:S(d),entries:B},b)for(x in A)x in O||i(O,x,A[x]);else n(n.P+n.F*(f||C),e,A);return A}},8653:function(t,e,r){var o=r(1572)("iterator"),n=!1;try{var i=[7][o]();i.return=function(){n=!0},Array.from(i,(function(){throw 2}))}catch(t){}t.exports=function(t,e){if(!e&&!n)return!1;var r=!1;try{var i=[7],a=i[o]();a.next=function(){return{done:r=!0}},i[o]=function(){return a},t(i)}catch(t){}return r}},7532:function(t){t.exports=function(t,e){return{value:e,done:!!t}}},100:function(t){t.exports={}},68:function(t){t.exports=!1},1333:function(t){var e=Math.expm1;t.exports=!e||e(10)>22025.465794806718||e(10)<22025.465794806718||-2e-17!=e(-2e-17)?function(t){return 0==(t=+t)?t:t>-1e-6&&t<1e-6?t+t*t/2:Math.exp(t)-1}:e},7788:function(t,e,r){var o=r(1291),n=Math.pow,i=n(2,-52),a=n(2,-23),s=n(2,127)*(2-a),c=n(2,-126);t.exports=Math.fround||function(t){var e,r,n=Math.abs(t),u=o(t);return n<c?u*(n/c/a+1/i-1/i)*c*a:(r=(e=(1+a/i)*n)-(e-n))>s||r!=r?u*(1/0):u*r}},959:function(t){t.exports=Math.log1p||function(t){return(t=+t)>-1e-8&&t<1e-8?t-t*t/2:Math.log(1+t)}},1291:function(t){t.exports=Math.sign||function(t){return 0==(t=+t)||t!=t?t:t<0?-1:1}},1994:function(t,e,r){function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=r(5497)("meta"),i=r(775),a=r(2923),s=r(3473).f,c=0,u=Object.isExtensible||function(){return!0},l=!r(189)((function(){return u(Object.preventExtensions({}))})),p=function(t){s(t,n,{value:{i:"O"+ ++c,w:{}}})},f=t.exports={KEY:n,NEED:!1,fastKey:function(t,e){if(!i(t))return"symbol"==o(t)?t:("string"==typeof t?"S":"P")+t;if(!a(t,n)){if(!u(t))return"F";if(!e)return"E";p(t)}return t[n].i},getWeak:function(t,e){if(!a(t,n)){if(!u(t))return!0;if(!e)return!1;p(t)}return t[n].w},onFreeze:function(t){return l&&f.NEED&&u(t)&&!a(t,n)&&p(t),t}}},8022:function(t,e,r){var o=r(5464),n=r(4042).set,i=o.MutationObserver||o.WebKitMutationObserver,a=o.process,s=o.Promise,c="process"==r(1831)(a);t.exports=function(){var t,e,r,u=function(){var o,n;for(c&&(o=a.domain)&&o.exit();t;){n=t.fn,t=t.next;try{n()}catch(o){throw t?r():e=void 0,o}}e=void 0,o&&o.enter()};if(c)r=function(){a.nextTick(u)};else if(!i||o.navigator&&o.navigator.standalone)if(s&&s.resolve){var l=s.resolve(void 0);r=function(){l.then(u)}}else r=function(){n.call(o,u)};else{var p=!0,f=document.createTextNode("");new i(u).observe(f,{characterData:!0}),r=function(){f.data=p=!p}}return function(o){var n={fn:o,next:void 0};e&&(e.next=n),t||(t=n,r()),e=n}}},3868:function(t,e,r){"use strict";var o=r(93);function n(t){var e,r;this.promise=new t((function(t,o){if(void 0!==e||void 0!==r)throw TypeError("Bad Promise constructor");e=t,r=o})),this.resolve=o(e),this.reject=o(r)}t.exports.f=function(t){return new n(t)}},2888:function(t,e,r){"use strict";var o=r(6869),n=r(7177),i=r(7546),a=r(8815),s=r(4004),c=r(2275),u=Object.assign;t.exports=!u||r(189)((function(){var t={},e={},r=Symbol(),o="abcdefghijklmnopqrst";return t[r]=7,o.split("").forEach((function(t){e[t]=t})),7!=u({},t)[r]||Object.keys(u({},e)).join("")!=o}))?function(t,e){for(var r=s(t),u=arguments.length,l=1,p=i.f,f=a.f;u>l;)for(var d,h=c(arguments[l++]),m=p?n(h).concat(p(h)):n(h),y=m.length,g=0;y>g;)d=m[g++],o&&!f.call(h,d)||(r[d]=h[d]);return r}:u},1889:function(t,e,r){var o=r(3282),n=r(6388),i=r(882),a=r(9424)("IE_PROTO"),s=function(){},c="prototype",u=function(){var t,e=r(6732)("iframe"),o=i.length;for(e.style.display="none",r(4138).appendChild(e),e.src="javascript:",(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),u=t.F;o--;)delete u[c][i[o]];return u()};t.exports=Object.create||function(t,e){var r;return null!==t?(s[c]=o(t),r=new s,s[c]=null,r[a]=t):r=u(),void 0===e?r:n(r,e)}},3473:function(t,e,r){var o=r(3282),n=r(5646),i=r(4250),a=Object.defineProperty;e.f=r(6869)?Object.defineProperty:function(t,e,r){if(o(t),e=i(e,!0),o(r),n)try{return a(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(t[e]=r.value),t}},6388:function(t,e,r){var o=r(3473),n=r(3282),i=r(7177);t.exports=r(6869)?Object.defineProperties:function(t,e){n(t);for(var r,a=i(e),s=a.length,c=0;s>c;)o.f(t,r=a[c++],e[r]);return t}},7171:function(t,e,r){var o=r(8815),n=r(6194),i=r(7703),a=r(4250),s=r(2923),c=r(5646),u=Object.getOwnPropertyDescriptor;e.f=r(6869)?u:function(t,e){if(t=i(t),e=a(e,!0),c)try{return u(t,e)}catch(t){}if(s(t,e))return n(!o.f.call(t,e),t[e])}},2835:function(t,e,r){function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=r(7703),i=r(685).f,a={}.toString,s="object"==("undefined"==typeof window?"undefined":o(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return s&&"[object Window]"==a.call(t)?function(t){try{return i(t)}catch(t){return s.slice()}}(t):i(n(t))}},685:function(t,e,r){var o=r(2347),n=r(882).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return o(t,n)}},7546:function(t,e){e.f=Object.getOwnPropertySymbols},1233:function(t,e,r){var o=r(2923),n=r(4004),i=r(9424)("IE_PROTO"),a=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=n(t),o(t,i)?t[i]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?a:null}},2347:function(t,e,r){var o=r(2923),n=r(7703),i=r(5386)(!1),a=r(9424)("IE_PROTO");t.exports=function(t,e){var r,s=n(t),c=0,u=[];for(r in s)r!=a&&o(s,r)&&u.push(r);for(;e.length>c;)o(s,r=e[c++])&&(~i(u,r)||u.push(r));return u}},7177:function(t,e,r){var o=r(2347),n=r(882);t.exports=Object.keys||function(t){return o(t,n)}},8815:function(t,e){e.f={}.propertyIsEnumerable},4661:function(t,e,r){var o=r(661),n=r(1232),i=r(189);t.exports=function(t,e){var r=(n.Object||{})[t]||Object[t],a={};a[t]=e(r),o(o.S+o.F*i((function(){r(1)})),"Object",a)}},2436:function(t,e,r){var o=r(6869),n=r(7177),i=r(7703),a=r(8815).f;t.exports=function(t){return function(e){for(var r,s=i(e),c=n(s),u=c.length,l=0,p=[];u>l;)r=c[l++],o&&!a.call(s,r)||p.push(t?[r,s[r]]:s[r]);return p}}},3992:function(t,e,r){var o=r(685),n=r(7546),i=r(3282),a=r(5464).Reflect;t.exports=a&&a.ownKeys||function(t){var e=o.f(i(t)),r=n.f;return r?e.concat(r(t)):e}},6455:function(t,e,r){var o=r(5464).parseFloat,n=r(9803).trim;t.exports=1/o(r(549)+"-0")!=-1/0?function(t){var e=n(String(t),3),r=o(e);return 0===r&&"-"==e.charAt(0)?-0:r}:o},2440:function(t,e,r){var o=r(5464).parseInt,n=r(9803).trim,i=r(549),a=/^[-+]?0[xX]/;t.exports=8!==o(i+"08")||22!==o(i+"0x16")?function(t,e){var r=n(String(t),3);return o(r,e>>>0||(a.test(r)?16:10))}:o},2658:function(t){t.exports=function(t){try{return{e:!1,v:t()}}catch(t){return{e:!0,v:t}}}},2123:function(t,e,r){var o=r(3282),n=r(775),i=r(3868);t.exports=function(t,e){if(o(t),n(e)&&e.constructor===t)return e;var r=i.f(t);return(0,r.resolve)(e),r.promise}},6194:function(t){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},1795:function(t,e,r){var o=r(7117);t.exports=function(t,e,r){for(var n in e)o(t,n,e[n],r);return t}},7117:function(t,e,r){var o=r(5464),n=r(3507),i=r(2923),a=r(5497)("src"),s=r(7163),c="toString",u=(""+s).split(c);r(1232).inspectSource=function(t){return s.call(t)},(t.exports=function(t,e,r,s){var c="function"==typeof r;c&&(i(r,"name")||n(r,"name",e)),t[e]!==r&&(c&&(i(r,a)||n(r,a,t[e]?""+t[e]:u.join(String(e)))),t===o?t[e]=r:s?t[e]?t[e]=r:n(t,e,r):(delete t[e],n(t,e,r)))})(Function.prototype,c,(function(){return"function"==typeof this&&this[a]||s.call(this)}))},7513:function(t,e,r){"use strict";function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=r(54),i=RegExp.prototype.exec;t.exports=function(t,e){var r=t.exec;if("function"==typeof r){var a=r.call(t,e);if("object"!==o(a))throw new TypeError("RegExp exec method returned something other than an Object or null");return a}if("RegExp"!==n(t))throw new TypeError("RegExp#exec called on incompatible receiver");return i.call(t,e)}},6950:function(t,e,r){"use strict";var o,n,i=r(4492),a=RegExp.prototype.exec,s=String.prototype.replace,c=a,u="lastIndex",l=(o=/a/,n=/b*/g,a.call(o,"a"),a.call(n,"a"),0!==o[u]||0!==n[u]),p=void 0!==/()??/.exec("")[1];(l||p)&&(c=function(t){var e,r,o,n,c=this;return p&&(r=new RegExp("^"+c.source+"$(?!\\s)",i.call(c))),l&&(e=c[u]),o=a.call(c,t),l&&o&&(c[u]=c.global?o.index+o[0].length:e),p&&o&&o.length>1&&s.call(o[0],r,(function(){for(n=1;n<arguments.length-2;n++)void 0===arguments[n]&&(o[n]=void 0)})),o}),t.exports=c},5077:function(t){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},412:function(t,e,r){var o=r(775),n=r(3282),i=function(t,e){if(n(t),!o(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,o){try{(o=r(3442)(Function.call,r(7171).f(Object.prototype,"__proto__").set,2))(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,r){return i(t,r),e?t.__proto__=r:o(t,r),t}}({},!1):void 0),check:i}},7348:function(t,e,r){"use strict";var o=r(5464),n=r(3473),i=r(6869),a=r(1572)("species");t.exports=function(t){var e=o[t];i&&e&&!e[a]&&n.f(e,a,{configurable:!0,get:function(){return this}})}},6290:function(t,e,r){var o=r(3473).f,n=r(2923),i=r(1572)("toStringTag");t.exports=function(t,e,r){t&&!n(t=r?t:t.prototype,i)&&o(t,i,{configurable:!0,value:e})}},9424:function(t,e,r){var o=r(7330)("keys"),n=r(5497);t.exports=function(t){return o[t]||(o[t]=n(t))}},7330:function(t,e,r){var o=r(1232),n=r(5464),i="__core-js_shared__",a=n[i]||(n[i]={});(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:o.version,mode:r(68)?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},9060:function(t,e,r){var o=r(3282),n=r(93),i=r(1572)("species");t.exports=function(t,e){var r,a=o(t).constructor;return void 0===a||null==(r=o(a)[i])?e:n(r)}},1318:function(t,e,r){"use strict";var o=r(189);t.exports=function(t,e){return!!t&&o((function(){e?t.call(null,(function(){}),1):t.call(null)}))}},2742:function(t,e,r){var o=r(5941),n=r(1794);t.exports=function(t){return function(e,r){var i,a,s=String(n(e)),c=o(r),u=s.length;return c<0||c>=u?t?"":void 0:(i=s.charCodeAt(c))<55296||i>56319||c+1===u||(a=s.charCodeAt(c+1))<56320||a>57343?t?s.charAt(c):i:t?s.slice(c,c+2):a-56320+(i-55296<<10)+65536}}},9856:function(t,e,r){var o=r(2209),n=r(1794);t.exports=function(t,e,r){if(o(e))throw TypeError("String#"+r+" doesn't accept regex!");return String(n(t))}},6134:function(t,e,r){var o=r(661),n=r(189),i=r(1794),a=/"/g,s=function(t,e,r,o){var n=String(i(t)),s="<"+e;return""!==r&&(s+=" "+r+'="'+String(o).replace(a,"&quot;")+'"'),s+">"+n+"</"+e+">"};t.exports=function(t,e){var r={};r[t]=e(s),o(o.P+o.F*n((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3})),"String",r)}},2494:function(t,e,r){var o=r(9043),n=r(1100),i=r(1794);t.exports=function(t,e,r,a){var s=String(i(t)),c=s.length,u=void 0===r?" ":String(r),l=o(e);if(l<=c||""==u)return s;var p=l-c,f=n.call(u,Math.ceil(p/u.length));return f.length>p&&(f=f.slice(0,p)),a?f+s:s+f}},1100:function(t,e,r){"use strict";var o=r(5941),n=r(1794);t.exports=function(t){var e=String(n(this)),r="",i=o(t);if(i<0||i==1/0)throw RangeError("Count can't be negative");for(;i>0;(i>>>=1)&&(e+=e))1&i&&(r+=e);return r}},9803:function(t,e,r){var o=r(661),n=r(1794),i=r(189),a=r(549),s="["+a+"]",c=RegExp("^"+s+s+"*"),u=RegExp(s+s+"*$"),l=function(t,e,r){var n={},s=i((function(){return!!a[t]()||"​"!="​"[t]()})),c=n[t]=s?e(p):a[t];r&&(n[r]=c),o(o.P+o.F*s,"String",n)},p=l.trim=function(t,e){return t=String(n(t)),1&e&&(t=t.replace(c,"")),2&e&&(t=t.replace(u,"")),t};t.exports=l},549:function(t){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},4042:function(t,e,r){var o,n,i,a=r(3442),s=r(1987),c=r(4138),u=r(6732),l=r(5464),p=l.process,f=l.setImmediate,d=l.clearImmediate,h=l.MessageChannel,m=l.Dispatch,y=0,g={},v="onreadystatechange",b=function(){var t=+this;if(g.hasOwnProperty(t)){var e=g[t];delete g[t],e()}},A=function(t){b.call(t.data)};f&&d||(f=function(t){for(var e=[],r=1;arguments.length>r;)e.push(arguments[r++]);return g[++y]=function(){s("function"==typeof t?t:Function(t),e)},o(y),y},d=function(t){delete g[t]},"process"==r(1831)(p)?o=function(t){p.nextTick(a(b,t,1))}:m&&m.now?o=function(t){m.now(a(b,t,1))}:h?(i=(n=new h).port2,n.port1.onmessage=A,o=a(i.postMessage,i,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(o=function(t){l.postMessage(t+"","*")},l.addEventListener("message",A,!1)):o=v in u("script")?function(t){c.appendChild(u("script"))[v]=function(){c.removeChild(this),b.call(t)}}:function(t){setTimeout(a(b,t,1),0)}),t.exports={set:f,clear:d}},8075:function(t,e,r){var o=r(5941),n=Math.max,i=Math.min;t.exports=function(t,e){return(t=o(t))<0?n(t+e,0):i(t,e)}},1915:function(t,e,r){var o=r(5941),n=r(9043);t.exports=function(t){if(void 0===t)return 0;var e=o(t),r=n(e);if(e!==r)throw RangeError("Wrong length!");return r}},5941:function(t){var e=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:e)(t)}},7703:function(t,e,r){var o=r(2275),n=r(1794);t.exports=function(t){return o(n(t))}},9043:function(t,e,r){var o=r(5941),n=Math.min;t.exports=function(t){return t>0?n(o(t),9007199254740991):0}},4004:function(t,e,r){var o=r(1794);t.exports=function(t){return Object(o(t))}},4250:function(t,e,r){var o=r(775);t.exports=function(t,e){if(!o(t))return t;var r,n;if(e&&"function"==typeof(r=t.toString)&&!o(n=r.call(t)))return n;if("function"==typeof(r=t.valueOf)&&!o(n=r.call(t)))return n;if(!e&&"function"==typeof(r=t.toString)&&!o(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")}},7699:function(t,e,r){"use strict";function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}if(r(6869)){var n=r(68),i=r(5464),a=r(189),s=r(661),c=r(2063),u=r(7022),l=r(3442),p=r(8278),f=r(6194),d=r(3507),h=r(1795),m=r(5941),y=r(9043),g=r(1915),v=r(8075),b=r(4250),A=r(2923),x=r(54),w=r(775),S=r(4004),P=r(6246),k=r(1889),C=r(1233),O=r(685).f,E=r(8388),T=r(5497),B=r(1572),M=r(1013),R=r(5386),_=r(9060),I=r(9279),j=r(100),D=r(8653),L=r(7348),q=r(4750),F=r(2248),N=r(3473),U=r(7171),G=N.f,H=U.f,z=i.RangeError,K=i.TypeError,W=i.Uint8Array,V="ArrayBuffer",Q="Shared"+V,X="BYTES_PER_ELEMENT",$="prototype",Y=Array[$],Z=u.ArrayBuffer,J=u.DataView,tt=M(0),et=M(2),rt=M(3),ot=M(4),nt=M(5),it=M(6),at=R(!0),st=R(!1),ct=I.values,ut=I.keys,lt=I.entries,pt=Y.lastIndexOf,ft=Y.reduce,dt=Y.reduceRight,ht=Y.join,mt=Y.sort,yt=Y.slice,gt=Y.toString,vt=Y.toLocaleString,bt=B("iterator"),At=B("toStringTag"),xt=T("typed_constructor"),wt=T("def_constructor"),St=c.CONSTR,Pt=c.TYPED,kt=c.VIEW,Ct="Wrong length!",Ot=M(1,(function(t,e){return Rt(_(t,t[wt]),e)})),Et=a((function(){return 1===new W(new Uint16Array([1]).buffer)[0]})),Tt=!!W&&!!W[$].set&&a((function(){new W(1).set({})})),Bt=function(t,e){var r=m(t);if(r<0||r%e)throw z("Wrong offset!");return r},Mt=function(t){if(w(t)&&Pt in t)return t;throw K(t+" is not a typed array!")},Rt=function(t,e){if(!w(t)||!(xt in t))throw K("It is not a typed array constructor!");return new t(e)},_t=function(t,e){return It(_(t,t[wt]),e)},It=function(t,e){for(var r=0,o=e.length,n=Rt(t,o);o>r;)n[r]=e[r++];return n},jt=function(t,e,r){G(t,e,{get:function(){return this._d[r]}})},Dt=function(t){var e,r,o,n,i,a,s=S(t),c=arguments.length,u=c>1?arguments[1]:void 0,p=void 0!==u,f=E(s);if(null!=f&&!P(f)){for(a=f.call(s),o=[],e=0;!(i=a.next()).done;e++)o.push(i.value);s=o}for(p&&c>2&&(u=l(u,arguments[2],2)),e=0,r=y(s.length),n=Rt(this,r);r>e;e++)n[e]=p?u(s[e],e):s[e];return n},Lt=function(){for(var t=0,e=arguments.length,r=Rt(this,e);e>t;)r[t]=arguments[t++];return r},qt=!!W&&a((function(){vt.call(new W(1))})),Ft=function(){return vt.apply(qt?yt.call(Mt(this)):Mt(this),arguments)},Nt={copyWithin:function(t,e){return F.call(Mt(this),t,e,arguments.length>2?arguments[2]:void 0)},every:function(t){return ot(Mt(this),t,arguments.length>1?arguments[1]:void 0)},fill:function(t){return q.apply(Mt(this),arguments)},filter:function(t){return _t(this,et(Mt(this),t,arguments.length>1?arguments[1]:void 0))},find:function(t){return nt(Mt(this),t,arguments.length>1?arguments[1]:void 0)},findIndex:function(t){return it(Mt(this),t,arguments.length>1?arguments[1]:void 0)},forEach:function(t){tt(Mt(this),t,arguments.length>1?arguments[1]:void 0)},indexOf:function(t){return st(Mt(this),t,arguments.length>1?arguments[1]:void 0)},includes:function(t){return at(Mt(this),t,arguments.length>1?arguments[1]:void 0)},join:function(t){return ht.apply(Mt(this),arguments)},lastIndexOf:function(t){return pt.apply(Mt(this),arguments)},map:function(t){return Ot(Mt(this),t,arguments.length>1?arguments[1]:void 0)},reduce:function(t){return ft.apply(Mt(this),arguments)},reduceRight:function(t){return dt.apply(Mt(this),arguments)},reverse:function(){for(var t,e=this,r=Mt(e).length,o=Math.floor(r/2),n=0;n<o;)t=e[n],e[n++]=e[--r],e[r]=t;return e},some:function(t){return rt(Mt(this),t,arguments.length>1?arguments[1]:void 0)},sort:function(t){return mt.call(Mt(this),t)},subarray:function(t,e){var r=Mt(this),o=r.length,n=v(t,o);return new(_(r,r[wt]))(r.buffer,r.byteOffset+n*r.BYTES_PER_ELEMENT,y((void 0===e?o:v(e,o))-n))}},Ut=function(t,e){return _t(this,yt.call(Mt(this),t,e))},Gt=function(t){Mt(this);var e=Bt(arguments[1],1),r=this.length,o=S(t),n=y(o.length),i=0;if(n+e>r)throw z(Ct);for(;i<n;)this[e+i]=o[i++]},Ht={entries:function(){return lt.call(Mt(this))},keys:function(){return ut.call(Mt(this))},values:function(){return ct.call(Mt(this))}},zt=function(t,e){return w(t)&&t[Pt]&&"symbol"!=o(e)&&e in t&&String(+e)==String(e)},Kt=function(t,e){return zt(t,e=b(e,!0))?f(2,t[e]):H(t,e)},Wt=function(t,e,r){return!(zt(t,e=b(e,!0))&&w(r)&&A(r,"value"))||A(r,"get")||A(r,"set")||r.configurable||A(r,"writable")&&!r.writable||A(r,"enumerable")&&!r.enumerable?G(t,e,r):(t[e]=r.value,t)};St||(U.f=Kt,N.f=Wt),s(s.S+s.F*!St,"Object",{getOwnPropertyDescriptor:Kt,defineProperty:Wt}),a((function(){gt.call({})}))&&(gt=vt=function(){return ht.call(this)});var Vt=h({},Nt);h(Vt,Ht),d(Vt,bt,Ht.values),h(Vt,{slice:Ut,set:Gt,constructor:function(){},toString:gt,toLocaleString:Ft}),jt(Vt,"buffer","b"),jt(Vt,"byteOffset","o"),jt(Vt,"byteLength","l"),jt(Vt,"length","e"),G(Vt,At,{get:function(){return this[Pt]}}),t.exports=function(t,e,r,o){var u=t+((o=!!o)?"Clamped":"")+"Array",l="get"+t,f="set"+t,h=i[u],m=h||{},v=h&&C(h),b=!h||!c.ABV,A={},S=h&&h[$],P=function(t,r){G(t,r,{get:function(){return function(t,r){var o=t._d;return o.v[l](r*e+o.o,Et)}(this,r)},set:function(t){return function(t,r,n){var i=t._d;o&&(n=(n=Math.round(n))<0?0:n>255?255:255&n),i.v[f](r*e+i.o,n,Et)}(this,r,t)},enumerable:!0})};b?(h=r((function(t,r,o,n){p(t,h,u,"_d");var i,a,s,c,l=0,f=0;if(w(r)){if(!(r instanceof Z||(c=x(r))==V||c==Q))return Pt in r?It(h,r):Dt.call(h,r);i=r,f=Bt(o,e);var m=r.byteLength;if(void 0===n){if(m%e)throw z(Ct);if((a=m-f)<0)throw z(Ct)}else if((a=y(n)*e)+f>m)throw z(Ct);s=a/e}else s=g(r),i=new Z(a=s*e);for(d(t,"_d",{b:i,o:f,l:a,e:s,v:new J(i)});l<s;)P(t,l++)})),S=h[$]=k(Vt),d(S,"constructor",h)):a((function(){h(1)}))&&a((function(){new h(-1)}))&&D((function(t){new h,new h(null),new h(1.5),new h(t)}),!0)||(h=r((function(t,r,o,n){var i;return p(t,h,u),w(r)?r instanceof Z||(i=x(r))==V||i==Q?void 0!==n?new m(r,Bt(o,e),n):void 0!==o?new m(r,Bt(o,e)):new m(r):Pt in r?It(h,r):Dt.call(h,r):new m(g(r))})),tt(v!==Function.prototype?O(m).concat(O(v)):O(m),(function(t){t in h||d(h,t,m[t])})),h[$]=S,n||(S.constructor=h));var E=S[bt],T=!!E&&("values"==E.name||null==E.name),B=Ht.values;d(h,xt,!0),d(S,Pt,u),d(S,kt,!0),d(S,wt,h),(o?new h(1)[At]==u:At in S)||G(S,At,{get:function(){return u}}),A[u]=h,s(s.G+s.W+s.F*(h!=m),A),s(s.S,u,{BYTES_PER_ELEMENT:e}),s(s.S+s.F*a((function(){m.of.call(h,1)})),u,{from:Dt,of:Lt}),X in S||d(S,X,e),s(s.P,u,Nt),L(u),s(s.P+s.F*Tt,u,{set:Gt}),s(s.P+s.F*!T,u,Ht),n||S.toString==gt||(S.toString=gt),s(s.P+s.F*a((function(){new h(1).slice()})),u,{slice:Ut}),s(s.P+s.F*(a((function(){return[1,2].toLocaleString()!=new h([1,2]).toLocaleString()}))||!a((function(){S.toLocaleString.call([1,2])}))),u,{toLocaleString:Ft}),j[u]=T?E:B,n||T||d(S,bt,B)}}else t.exports=function(){}},7022:function(t,e,r){"use strict";var o=r(5464),n=r(6869),i=r(68),a=r(2063),s=r(3507),c=r(1795),u=r(189),l=r(8278),p=r(5941),f=r(9043),d=r(1915),h=r(685).f,m=r(3473).f,y=r(4750),g=r(6290),v="ArrayBuffer",b="DataView",A="prototype",x="Wrong index!",w=o[v],S=o[b],P=o.Math,k=o.RangeError,C=o.Infinity,O=w,E=P.abs,T=P.pow,B=P.floor,M=P.log,R=P.LN2,_="buffer",I="byteLength",j="byteOffset",D=n?"_b":_,L=n?"_l":I,q=n?"_o":j;function F(t,e,r){var o,n,i,a=new Array(r),s=8*r-e-1,c=(1<<s)-1,u=c>>1,l=23===e?T(2,-24)-T(2,-77):0,p=0,f=t<0||0===t&&1/t<0?1:0;for((t=E(t))!=t||t===C?(n=t!=t?1:0,o=c):(o=B(M(t)/R),t*(i=T(2,-o))<1&&(o--,i*=2),(t+=o+u>=1?l/i:l*T(2,1-u))*i>=2&&(o++,i/=2),o+u>=c?(n=0,o=c):o+u>=1?(n=(t*i-1)*T(2,e),o+=u):(n=t*T(2,u-1)*T(2,e),o=0));e>=8;a[p++]=255&n,n/=256,e-=8);for(o=o<<e|n,s+=e;s>0;a[p++]=255&o,o/=256,s-=8);return a[--p]|=128*f,a}function N(t,e,r){var o,n=8*r-e-1,i=(1<<n)-1,a=i>>1,s=n-7,c=r-1,u=t[c--],l=127&u;for(u>>=7;s>0;l=256*l+t[c],c--,s-=8);for(o=l&(1<<-s)-1,l>>=-s,s+=e;s>0;o=256*o+t[c],c--,s-=8);if(0===l)l=1-a;else{if(l===i)return o?NaN:u?-C:C;o+=T(2,e),l-=a}return(u?-1:1)*o*T(2,l-e)}function U(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]}function G(t){return[255&t]}function H(t){return[255&t,t>>8&255]}function z(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]}function K(t){return F(t,52,8)}function W(t){return F(t,23,4)}function V(t,e,r){m(t[A],e,{get:function(){return this[r]}})}function Q(t,e,r,o){var n=d(+r);if(n+e>t[L])throw k(x);var i=t[D]._b,a=n+t[q],s=i.slice(a,a+e);return o?s:s.reverse()}function X(t,e,r,o,n,i){var a=d(+r);if(a+e>t[L])throw k(x);for(var s=t[D]._b,c=a+t[q],u=o(+n),l=0;l<e;l++)s[c+l]=u[i?l:e-l-1]}if(a.ABV){if(!u((function(){w(1)}))||!u((function(){new w(-1)}))||u((function(){return new w,new w(1.5),new w(NaN),w.name!=v}))){for(var $,Y=(w=function(t){return l(this,w),new O(d(t))})[A]=O[A],Z=h(O),J=0;Z.length>J;)($=Z[J++])in w||s(w,$,O[$]);i||(Y.constructor=w)}var tt=new S(new w(2)),et=S[A].setInt8;tt.setInt8(0,2147483648),tt.setInt8(1,2147483649),!tt.getInt8(0)&&tt.getInt8(1)||c(S[A],{setInt8:function(t,e){et.call(this,t,e<<24>>24)},setUint8:function(t,e){et.call(this,t,e<<24>>24)}},!0)}else w=function(t){l(this,w,v);var e=d(t);this._b=y.call(new Array(e),0),this[L]=e},S=function(t,e,r){l(this,S,b),l(t,w,b);var o=t[L],n=p(e);if(n<0||n>o)throw k("Wrong offset!");if(n+(r=void 0===r?o-n:f(r))>o)throw k("Wrong length!");this[D]=t,this[q]=n,this[L]=r},n&&(V(w,I,"_l"),V(S,_,"_b"),V(S,I,"_l"),V(S,j,"_o")),c(S[A],{getInt8:function(t){return Q(this,1,t)[0]<<24>>24},getUint8:function(t){return Q(this,1,t)[0]},getInt16:function(t){var e=Q(this,2,t,arguments[1]);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=Q(this,2,t,arguments[1]);return e[1]<<8|e[0]},getInt32:function(t){return U(Q(this,4,t,arguments[1]))},getUint32:function(t){return U(Q(this,4,t,arguments[1]))>>>0},getFloat32:function(t){return N(Q(this,4,t,arguments[1]),23,4)},getFloat64:function(t){return N(Q(this,8,t,arguments[1]),52,8)},setInt8:function(t,e){X(this,1,t,G,e)},setUint8:function(t,e){X(this,1,t,G,e)},setInt16:function(t,e){X(this,2,t,H,e,arguments[2])},setUint16:function(t,e){X(this,2,t,H,e,arguments[2])},setInt32:function(t,e){X(this,4,t,z,e,arguments[2])},setUint32:function(t,e){X(this,4,t,z,e,arguments[2])},setFloat32:function(t,e){X(this,4,t,W,e,arguments[2])},setFloat64:function(t,e){X(this,8,t,K,e,arguments[2])}});g(w,v),g(S,b),s(S[A],a.VIEW,!0),e[v]=w,e[b]=S},2063:function(t,e,r){for(var o,n=r(5464),i=r(3507),a=r(5497),s=a("typed_array"),c=a("view"),u=!(!n.ArrayBuffer||!n.DataView),l=u,p=0,f="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");p<9;)(o=n[f[p++]])?(i(o.prototype,s,!0),i(o.prototype,c,!0)):l=!1;t.exports={ABV:u,CONSTR:l,TYPED:s,VIEW:c}},5497:function(t){var e=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++e+r).toString(36))}},992:function(t,e,r){var o=r(5464).navigator;t.exports=o&&o.userAgent||""},6970:function(t,e,r){var o=r(775);t.exports=function(t,e){if(!o(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},8354:function(t,e,r){var o=r(5464),n=r(1232),i=r(68),a=r(5078),s=r(3473).f;t.exports=function(t){var e=n.Symbol||(n.Symbol=i?{}:o.Symbol||{});"_"==t.charAt(0)||t in e||s(e,t,{value:a.f(t)})}},5078:function(t,e,r){e.f=r(1572)},1572:function(t,e,r){var o=r(7330)("wks"),n=r(5497),i=r(5464).Symbol,a="function"==typeof i;(t.exports=function(t){return o[t]||(o[t]=a&&i[t]||(a?i:n)("Symbol."+t))}).store=o},8388:function(t,e,r){var o=r(54),n=r(1572)("iterator"),i=r(100);t.exports=r(1232).getIteratorMethod=function(t){if(null!=t)return t[n]||t["@@iterator"]||i[o(t)]}},8438:function(t,e,r){var o=r(661);o(o.P,"Array",{copyWithin:r(2248)}),r(7562)("copyWithin")},9234:function(t,e,r){"use strict";var o=r(661),n=r(1013)(4);o(o.P+o.F*!r(1318)([].every,!0),"Array",{every:function(t){return n(this,t,arguments[1])}})},5004:function(t,e,r){var o=r(661);o(o.P,"Array",{fill:r(4750)}),r(7562)("fill")},1227:function(t,e,r){"use strict";var o=r(661),n=r(1013)(2);o(o.P+o.F*!r(1318)([].filter,!0),"Array",{filter:function(t){return n(this,t,arguments[1])}})},4571:function(t,e,r){"use strict";var o=r(661),n=r(1013)(6),i="findIndex",a=!0;i in[]&&Array(1)[i]((function(){a=!1})),o(o.P+o.F*a,"Array",{findIndex:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),r(7562)(i)},7910:function(t,e,r){"use strict";var o=r(661),n=r(1013)(5),i="find",a=!0;i in[]&&Array(1)[i]((function(){a=!1})),o(o.P+o.F*a,"Array",{find:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),r(7562)(i)},7294:function(t,e,r){"use strict";var o=r(661),n=r(1013)(0),i=r(1318)([].forEach,!0);o(o.P+o.F*!i,"Array",{forEach:function(t){return n(this,t,arguments[1])}})},6385:function(t,e,r){"use strict";var o=r(3442),n=r(661),i=r(4004),a=r(5234),s=r(6246),c=r(9043),u=r(905),l=r(8388);n(n.S+n.F*!r(8653)((function(t){Array.from(t)})),"Array",{from:function(t){var e,r,n,p,f=i(t),d="function"==typeof this?this:Array,h=arguments.length,m=h>1?arguments[1]:void 0,y=void 0!==m,g=0,v=l(f);if(y&&(m=o(m,h>2?arguments[2]:void 0,2)),null==v||d==Array&&s(v))for(r=new d(e=c(f.length));e>g;g++)u(r,g,y?m(f[g],g):f[g]);else for(p=v.call(f),r=new d;!(n=p.next()).done;g++)u(r,g,y?a(p,m,[n.value,g],!0):n.value);return r.length=g,r}})},6147:function(t,e,r){"use strict";var o=r(661),n=r(5386)(!1),i=[].indexOf,a=!!i&&1/[1].indexOf(1,-0)<0;o(o.P+o.F*(a||!r(1318)(i)),"Array",{indexOf:function(t){return a?i.apply(this,arguments)||0:n(this,t,arguments[1])}})},4893:function(t,e,r){var o=r(661);o(o.S,"Array",{isArray:r(4887)})},9279:function(t,e,r){"use strict";var o=r(7562),n=r(7532),i=r(100),a=r(7703);t.exports=r(6621)(Array,"Array",(function(t,e){this._t=a(t),this._i=0,this._k=e}),(function(){var t=this._t,e=this._k,r=this._i++;return!t||r>=t.length?(this._t=void 0,n(1)):n(0,"keys"==e?r:"values"==e?t[r]:[r,t[r]])}),"values"),i.Arguments=i.Array,o("keys"),o("values"),o("entries")},7025:function(t,e,r){"use strict";var o=r(661),n=r(7703),i=[].join;o(o.P+o.F*(r(2275)!=Object||!r(1318)(i)),"Array",{join:function(t){return i.call(n(this),void 0===t?",":t)}})},5796:function(t,e,r){"use strict";var o=r(661),n=r(7703),i=r(5941),a=r(9043),s=[].lastIndexOf,c=!!s&&1/[1].lastIndexOf(1,-0)<0;o(o.P+o.F*(c||!r(1318)(s)),"Array",{lastIndexOf:function(t){if(c)return s.apply(this,arguments)||0;var e=n(this),r=a(e.length),o=r-1;for(arguments.length>1&&(o=Math.min(o,i(arguments[1]))),o<0&&(o=r+o);o>=0;o--)if(o in e&&e[o]===t)return o||0;return-1}})},3259:function(t,e,r){"use strict";var o=r(661),n=r(1013)(1);o(o.P+o.F*!r(1318)([].map,!0),"Array",{map:function(t){return n(this,t,arguments[1])}})},9488:function(t,e,r){"use strict";var o=r(661),n=r(905);o(o.S+o.F*r(189)((function(){function t(){}return!(Array.of.call(t)instanceof t)})),"Array",{of:function(){for(var t=0,e=arguments.length,r=new("function"==typeof this?this:Array)(e);e>t;)n(r,t,arguments[t++]);return r.length=e,r}})},2128:function(t,e,r){"use strict";var o=r(661),n=r(5789);o(o.P+o.F*!r(1318)([].reduceRight,!0),"Array",{reduceRight:function(t){return n(this,t,arguments.length,arguments[1],!0)}})},687:function(t,e,r){"use strict";var o=r(661),n=r(5789);o(o.P+o.F*!r(1318)([].reduce,!0),"Array",{reduce:function(t){return n(this,t,arguments.length,arguments[1],!1)}})},8695:function(t,e,r){"use strict";var o=r(661),n=r(4138),i=r(1831),a=r(8075),s=r(9043),c=[].slice;o(o.P+o.F*r(189)((function(){n&&c.call(n)})),"Array",{slice:function(t,e){var r=s(this.length),o=i(this);if(e=void 0===e?r:e,"Array"==o)return c.call(this,t,e);for(var n=a(t,r),u=a(e,r),l=s(u-n),p=new Array(l),f=0;f<l;f++)p[f]="String"==o?this.charAt(n+f):this[n+f];return p}})},61:function(t,e,r){"use strict";var o=r(661),n=r(1013)(3);o(o.P+o.F*!r(1318)([].some,!0),"Array",{some:function(t){return n(this,t,arguments[1])}})},317:function(t,e,r){"use strict";var o=r(661),n=r(93),i=r(4004),a=r(189),s=[].sort,c=[1,2,3];o(o.P+o.F*(a((function(){c.sort(void 0)}))||!a((function(){c.sort(null)}))||!r(1318)(s)),"Array",{sort:function(t){return void 0===t?s.call(i(this)):s.call(i(this),n(t))}})},47:function(t,e,r){r(7348)("Array")},4446:function(t,e,r){var o=r(661);o(o.S,"Date",{now:function(){return(new Date).getTime()}})},5543:function(t,e,r){var o=r(661),n=r(1675);o(o.P+o.F*(Date.prototype.toISOString!==n),"Date",{toISOString:n})},596:function(t,e,r){"use strict";var o=r(661),n=r(4004),i=r(4250);o(o.P+o.F*r(189)((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})})),"Date",{toJSON:function(t){var e=n(this),r=i(e);return"number"!=typeof r||isFinite(r)?e.toISOString():null}})},2413:function(t,e,r){var o=r(1572)("toPrimitive"),n=Date.prototype;o in n||r(3507)(n,o,r(8817))},4595:function(t,e,r){var o=Date.prototype,n="Invalid Date",i="toString",a=o[i],s=o.getTime;new Date(NaN)+""!=n&&r(7117)(o,i,(function(){var t=s.call(this);return t==t?a.call(this):n}))},5167:function(t,e,r){var o=r(661);o(o.P,"Function",{bind:r(7064)})},3936:function(t,e,r){"use strict";var o=r(775),n=r(1233),i=r(1572)("hasInstance"),a=Function.prototype;i in a||r(3473).f(a,i,{value:function(t){if("function"!=typeof this||!o(t))return!1;if(!o(this.prototype))return t instanceof this;for(;t=n(t);)if(this.prototype===t)return!0;return!1}})},2051:function(t,e,r){var o=r(3473).f,n=Function.prototype,i=/^\s*function ([^ (]*)/,a="name";a in n||r(6869)&&o(n,a,{configurable:!0,get:function(){try{return(""+this).match(i)[1]}catch(t){return""}}})},3448:function(t,e,r){"use strict";var o=r(4327),n=r(6970),i="Map";t.exports=r(6103)(i,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{get:function(t){var e=o.getEntry(n(this,i),t);return e&&e.v},set:function(t,e){return o.def(n(this,i),0===t?0:t,e)}},o,!0)},2442:function(t,e,r){var o=r(661),n=r(959),i=Math.sqrt,a=Math.acosh;o(o.S+o.F*!(a&&710==Math.floor(a(Number.MAX_VALUE))&&a(1/0)==1/0),"Math",{acosh:function(t){return(t=+t)<1?NaN:t>94906265.62425156?Math.log(t)+Math.LN2:n(t-1+i(t-1)*i(t+1))}})},7537:function(t,e,r){var o=r(661),n=Math.asinh;o(o.S+o.F*!(n&&1/n(0)>0),"Math",{asinh:function t(e){return isFinite(e=+e)&&0!=e?e<0?-t(-e):Math.log(e+Math.sqrt(e*e+1)):e}})},2954:function(t,e,r){var o=r(661),n=Math.atanh;o(o.S+o.F*!(n&&1/n(-0)<0),"Math",{atanh:function(t){return 0==(t=+t)?t:Math.log((1+t)/(1-t))/2}})},3413:function(t,e,r){var o=r(661),n=r(1291);o(o.S,"Math",{cbrt:function(t){return n(t=+t)*Math.pow(Math.abs(t),1/3)}})},7006:function(t,e,r){var o=r(661);o(o.S,"Math",{clz32:function(t){return(t>>>=0)?31-Math.floor(Math.log(t+.5)*Math.LOG2E):32}})},8839:function(t,e,r){var o=r(661),n=Math.exp;o(o.S,"Math",{cosh:function(t){return(n(t=+t)+n(-t))/2}})},2929:function(t,e,r){var o=r(661),n=r(1333);o(o.S+o.F*(n!=Math.expm1),"Math",{expm1:n})},584:function(t,e,r){var o=r(661);o(o.S,"Math",{fround:r(7788)})},6266:function(t,e,r){var o=r(661),n=Math.abs;o(o.S,"Math",{hypot:function(t,e){for(var r,o,i=0,a=0,s=arguments.length,c=0;a<s;)c<(r=n(arguments[a++]))?(i=i*(o=c/r)*o+1,c=r):i+=r>0?(o=r/c)*o:r;return c===1/0?1/0:c*Math.sqrt(i)}})},2681:function(t,e,r){var o=r(661),n=Math.imul;o(o.S+o.F*r(189)((function(){return-5!=n(4294967295,5)||2!=n.length})),"Math",{imul:function(t,e){var r=65535,o=+t,n=+e,i=r&o,a=r&n;return 0|i*a+((r&o>>>16)*a+i*(r&n>>>16)<<16>>>0)}})},5379:function(t,e,r){var o=r(661);o(o.S,"Math",{log10:function(t){return Math.log(t)*Math.LOG10E}})},3331:function(t,e,r){var o=r(661);o(o.S,"Math",{log1p:r(959)})},6118:function(t,e,r){var o=r(661);o(o.S,"Math",{log2:function(t){return Math.log(t)/Math.LN2}})},2079:function(t,e,r){var o=r(661);o(o.S,"Math",{sign:r(1291)})},9552:function(t,e,r){var o=r(661),n=r(1333),i=Math.exp;o(o.S+o.F*r(189)((function(){return-2e-17!=!Math.sinh(-2e-17)})),"Math",{sinh:function(t){return Math.abs(t=+t)<1?(n(t)-n(-t))/2:(i(t-1)-i(-t-1))*(Math.E/2)}})},483:function(t,e,r){var o=r(661),n=r(1333),i=Math.exp;o(o.S,"Math",{tanh:function(t){var e=n(t=+t),r=n(-t);return e==1/0?1:r==1/0?-1:(e-r)/(i(t)+i(-t))}})},2842:function(t,e,r){var o=r(661);o(o.S,"Math",{trunc:function(t){return(t>0?Math.floor:Math.ceil)(t)}})},2727:function(t,e,r){"use strict";var o=r(5464),n=r(2923),i=r(1831),a=r(7278),s=r(4250),c=r(189),u=r(685).f,l=r(7171).f,p=r(3473).f,f=r(9803).trim,d="Number",h=o[d],m=h,y=h.prototype,g=i(r(1889)(y))==d,v="trim"in String.prototype,b=function(t){var e=s(t,!1);if("string"==typeof e&&e.length>2){var r,o,n,i=(e=v?e.trim():f(e,3)).charCodeAt(0);if(43===i||45===i){if(88===(r=e.charCodeAt(2))||120===r)return NaN}else if(48===i){switch(e.charCodeAt(1)){case 66:case 98:o=2,n=49;break;case 79:case 111:o=8,n=55;break;default:return+e}for(var a,c=e.slice(2),u=0,l=c.length;u<l;u++)if((a=c.charCodeAt(u))<48||a>n)return NaN;return parseInt(c,o)}}return+e};if(!h(" 0o1")||!h("0b1")||h("+0x1")){h=function(t){var e=arguments.length<1?0:t,r=this;return r instanceof h&&(g?c((function(){y.valueOf.call(r)})):i(r)!=d)?a(new m(b(e)),r,h):b(e)};for(var A,x=r(6869)?u(m):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),w=0;x.length>w;w++)n(m,A=x[w])&&!n(h,A)&&p(h,A,l(m,A));h.prototype=y,y.constructor=h,r(7117)(o,d,h)}},1945:function(t,e,r){var o=r(661);o(o.S,"Number",{EPSILON:Math.pow(2,-52)})},3275:function(t,e,r){var o=r(661),n=r(5464).isFinite;o(o.S,"Number",{isFinite:function(t){return"number"==typeof t&&n(t)}})},5368:function(t,e,r){var o=r(661);o(o.S,"Number",{isInteger:r(5088)})},9411:function(t,e,r){var o=r(661);o(o.S,"Number",{isNaN:function(t){return t!=t}})},1874:function(t,e,r){var o=r(661),n=r(5088),i=Math.abs;o(o.S,"Number",{isSafeInteger:function(t){return n(t)&&i(t)<=9007199254740991}})},4676:function(t,e,r){var o=r(661);o(o.S,"Number",{MAX_SAFE_INTEGER:9007199254740991})},5150:function(t,e,r){var o=r(661);o(o.S,"Number",{MIN_SAFE_INTEGER:-9007199254740991})},863:function(t,e,r){var o=r(661),n=r(6455);o(o.S+o.F*(Number.parseFloat!=n),"Number",{parseFloat:n})},7440:function(t,e,r){var o=r(661),n=r(2440);o(o.S+o.F*(Number.parseInt!=n),"Number",{parseInt:n})},1005:function(t,e,r){"use strict";var o=r(661),n=r(5941),i=r(4400),a=r(1100),s=1..toFixed,c=Math.floor,u=[0,0,0,0,0,0],l="Number.toFixed: incorrect invocation!",p="0",f=function(t,e){for(var r=-1,o=e;++r<6;)o+=t*u[r],u[r]=o%1e7,o=c(o/1e7)},d=function(t){for(var e=6,r=0;--e>=0;)r+=u[e],u[e]=c(r/t),r=r%t*1e7},h=function(){for(var t=6,e="";--t>=0;)if(""!==e||0===t||0!==u[t]){var r=String(u[t]);e=""===e?r:e+a.call(p,7-r.length)+r}return e},m=function t(e,r,o){return 0===r?o:r%2==1?t(e,r-1,o*e):t(e*e,r/2,o)};o(o.P+o.F*(!!s&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!r(189)((function(){s.call({})}))),"Number",{toFixed:function(t){var e,r,o,s,c=i(this,l),u=n(t),y="",g=p;if(u<0||u>20)throw RangeError(l);if(c!=c)return"NaN";if(c<=-1e21||c>=1e21)return String(c);if(c<0&&(y="-",c=-c),c>1e-21)if(e=function(t){for(var e=0,r=t;r>=4096;)e+=12,r/=4096;for(;r>=2;)e+=1,r/=2;return e}(c*m(2,69,1))-69,r=e<0?c*m(2,-e,1):c/m(2,e,1),r*=4503599627370496,(e=52-e)>0){for(f(0,r),o=u;o>=7;)f(1e7,0),o-=7;for(f(m(10,o,1),0),o=e-1;o>=23;)d(1<<23),o-=23;d(1<<o),f(1,1),d(2),g=h()}else f(0,r),f(1<<-e,0),g=h()+a.call(p,u);return u>0?y+((s=g.length)<=u?"0."+a.call(p,u-s)+g:g.slice(0,s-u)+"."+g.slice(s-u)):y+g}})},5075:function(t,e,r){"use strict";var o=r(661),n=r(189),i=r(4400),a=1..toPrecision;o(o.P+o.F*(n((function(){return"1"!==a.call(1,void 0)}))||!n((function(){a.call({})}))),"Number",{toPrecision:function(t){var e=i(this,"Number#toPrecision: incorrect invocation!");return void 0===t?a.call(e):a.call(e,t)}})},9272:function(t,e,r){var o=r(661);o(o.S+o.F,"Object",{assign:r(2888)})},7041:function(t,e,r){var o=r(661);o(o.S,"Object",{create:r(1889)})},390:function(t,e,r){var o=r(661);o(o.S+o.F*!r(6869),"Object",{defineProperties:r(6388)})},5514:function(t,e,r){var o=r(661);o(o.S+o.F*!r(6869),"Object",{defineProperty:r(3473).f})},3050:function(t,e,r){var o=r(775),n=r(1994).onFreeze;r(4661)("freeze",(function(t){return function(e){return t&&o(e)?t(n(e)):e}}))},8320:function(t,e,r){var o=r(7703),n=r(7171).f;r(4661)("getOwnPropertyDescriptor",(function(){return function(t,e){return n(o(t),e)}}))},5797:function(t,e,r){r(4661)("getOwnPropertyNames",(function(){return r(2835).f}))},9458:function(t,e,r){var o=r(4004),n=r(1233);r(4661)("getPrototypeOf",(function(){return function(t){return n(o(t))}}))},2031:function(t,e,r){var o=r(775);r(4661)("isExtensible",(function(t){return function(e){return!!o(e)&&(!t||t(e))}}))},500:function(t,e,r){var o=r(775);r(4661)("isFrozen",(function(t){return function(e){return!o(e)||!!t&&t(e)}}))},8246:function(t,e,r){var o=r(775);r(4661)("isSealed",(function(t){return function(e){return!o(e)||!!t&&t(e)}}))},4921:function(t,e,r){var o=r(661);o(o.S,"Object",{is:r(5077)})},8025:function(t,e,r){var o=r(4004),n=r(7177);r(4661)("keys",(function(){return function(t){return n(o(t))}}))},6966:function(t,e,r){var o=r(775),n=r(1994).onFreeze;r(4661)("preventExtensions",(function(t){return function(e){return t&&o(e)?t(n(e)):e}}))},9604:function(t,e,r){var o=r(775),n=r(1994).onFreeze;r(4661)("seal",(function(t){return function(e){return t&&o(e)?t(n(e)):e}}))},1174:function(t,e,r){var o=r(661);o(o.S,"Object",{setPrototypeOf:r(412).set})},7052:function(t,e,r){"use strict";var o=r(54),n={};n[r(1572)("toStringTag")]="z",n+""!="[object z]"&&r(7117)(Object.prototype,"toString",(function(){return"[object "+o(this)+"]"}),!0)},3098:function(t,e,r){var o=r(661),n=r(6455);o(o.G+o.F*(parseFloat!=n),{parseFloat:n})},1917:function(t,e,r){var o=r(661),n=r(2440);o(o.G+o.F*(parseInt!=n),{parseInt:n})},5263:function(t,e,r){"use strict";var o,n,i,a,s=r(68),c=r(5464),u=r(3442),l=r(54),p=r(661),f=r(775),d=r(93),h=r(8278),m=r(712),y=r(9060),g=r(4042).set,v=r(8022)(),b=r(3868),A=r(2658),x=r(992),w=r(2123),S="Promise",P=c.TypeError,k=c.process,C=k&&k.versions,O=C&&C.v8||"",E=c[S],T="process"==l(k),B=function(){},M=n=b.f,R=!!function(){try{var t=E.resolve(1),e=(t.constructor={})[r(1572)("species")]=function(t){t(B,B)};return(T||"function"==typeof PromiseRejectionEvent)&&t.then(B)instanceof e&&0!==O.indexOf("6.6")&&-1===x.indexOf("Chrome/66")}catch(t){}}(),_=function(t){var e;return!(!f(t)||"function"!=typeof(e=t.then))&&e},I=function(t,e){if(!t._n){t._n=!0;var r=t._c;v((function(){for(var o=t._v,n=1==t._s,i=0,a=function(e){var r,i,a,s=n?e.ok:e.fail,c=e.resolve,u=e.reject,l=e.domain;try{s?(n||(2==t._h&&L(t),t._h=1),!0===s?r=o:(l&&l.enter(),r=s(o),l&&(l.exit(),a=!0)),r===e.promise?u(P("Promise-chain cycle")):(i=_(r))?i.call(r,c,u):c(r)):u(o)}catch(t){l&&!a&&l.exit(),u(t)}};r.length>i;)a(r[i++]);t._c=[],t._n=!1,e&&!t._h&&j(t)}))}},j=function(t){g.call(c,(function(){var e,r,o,n=t._v,i=D(t);if(i&&(e=A((function(){T?k.emit("unhandledRejection",n,t):(r=c.onunhandledrejection)?r({promise:t,reason:n}):(o=c.console)&&o.error&&o.error("Unhandled promise rejection",n)})),t._h=T||D(t)?2:1),t._a=void 0,i&&e.e)throw e.v}))},D=function(t){return 1!==t._h&&0===(t._a||t._c).length},L=function(t){g.call(c,(function(){var e;T?k.emit("rejectionHandled",t):(e=c.onrejectionhandled)&&e({promise:t,reason:t._v})}))},q=function(t){var e=this;e._d||(e._d=!0,(e=e._w||e)._v=t,e._s=2,e._a||(e._a=e._c.slice()),I(e,!0))},F=function t(e){var r,o=this;if(!o._d){o._d=!0,o=o._w||o;try{if(o===e)throw P("Promise can't be resolved itself");(r=_(e))?v((function(){var n={_w:o,_d:!1};try{r.call(e,u(t,n,1),u(q,n,1))}catch(t){q.call(n,t)}})):(o._v=e,o._s=1,I(o,!1))}catch(t){q.call({_w:o,_d:!1},t)}}};R||(E=function(t){h(this,E,S,"_h"),d(t),o.call(this);try{t(u(F,this,1),u(q,this,1))}catch(t){q.call(this,t)}},(o=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=r(1795)(E.prototype,{then:function(t,e){var r=M(y(this,E));return r.ok="function"!=typeof t||t,r.fail="function"==typeof e&&e,r.domain=T?k.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&I(this,!1),r.promise},catch:function(t){return this.then(void 0,t)}}),i=function(){var t=new o;this.promise=t,this.resolve=u(F,t,1),this.reject=u(q,t,1)},b.f=M=function(t){return t===E||t===a?new i(t):n(t)}),p(p.G+p.W+p.F*!R,{Promise:E}),r(6290)(E,S),r(7348)(S),a=r(1232)[S],p(p.S+p.F*!R,S,{reject:function(t){var e=M(this);return(0,e.reject)(t),e.promise}}),p(p.S+p.F*(s||!R),S,{resolve:function(t){return w(s&&this===a?E:this,t)}}),p(p.S+p.F*!(R&&r(8653)((function(t){E.all(t).catch(B)}))),S,{all:function(t){var e=this,r=M(e),o=r.resolve,n=r.reject,i=A((function(){var r=[],i=0,a=1;m(t,!1,(function(t){var s=i++,c=!1;r.push(void 0),a++,e.resolve(t).then((function(t){c||(c=!0,r[s]=t,--a||o(r))}),n)})),--a||o(r)}));return i.e&&n(i.v),r.promise},race:function(t){var e=this,r=M(e),o=r.reject,n=A((function(){m(t,!1,(function(t){e.resolve(t).then(r.resolve,o)}))}));return n.e&&o(n.v),r.promise}})},2805:function(t,e,r){var o=r(661),n=r(93),i=r(3282),a=(r(5464).Reflect||{}).apply,s=Function.apply;o(o.S+o.F*!r(189)((function(){a((function(){}))})),"Reflect",{apply:function(t,e,r){var o=n(t),c=i(r);return a?a(o,e,c):s.call(o,e,c)}})},5e3:function(t,e,r){var o=r(661),n=r(1889),i=r(93),a=r(3282),s=r(775),c=r(189),u=r(7064),l=(r(5464).Reflect||{}).construct,p=c((function(){function t(){}return!(l((function(){}),[],t)instanceof t)})),f=!c((function(){l((function(){}))}));o(o.S+o.F*(p||f),"Reflect",{construct:function(t,e){i(t),a(e);var r=arguments.length<3?t:i(arguments[2]);if(f&&!p)return l(t,e,r);if(t==r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var o=[null];return o.push.apply(o,e),new(u.apply(t,o))}var c=r.prototype,d=n(s(c)?c:Object.prototype),h=Function.apply.call(t,d,e);return s(h)?h:d}})},1066:function(t,e,r){var o=r(3473),n=r(661),i=r(3282),a=r(4250);n(n.S+n.F*r(189)((function(){Reflect.defineProperty(o.f({},1,{value:1}),1,{value:2})})),"Reflect",{defineProperty:function(t,e,r){i(t),e=a(e,!0),i(r);try{return o.f(t,e,r),!0}catch(t){return!1}}})},6306:function(t,e,r){var o=r(661),n=r(7171).f,i=r(3282);o(o.S,"Reflect",{deleteProperty:function(t,e){var r=n(i(t),e);return!(r&&!r.configurable)&&delete t[e]}})},9735:function(t,e,r){"use strict";var o=r(661),n=r(3282),i=function(t){this._t=n(t),this._i=0;var e,r=this._k=[];for(e in t)r.push(e)};r(3654)(i,"Object",(function(){var t,e=this,r=e._k;do{if(e._i>=r.length)return{value:void 0,done:!0}}while(!((t=r[e._i++])in e._t));return{value:t,done:!1}})),o(o.S,"Reflect",{enumerate:function(t){return new i(t)}})},5888:function(t,e,r){var o=r(7171),n=r(661),i=r(3282);n(n.S,"Reflect",{getOwnPropertyDescriptor:function(t,e){return o.f(i(t),e)}})},2978:function(t,e,r){var o=r(661),n=r(1233),i=r(3282);o(o.S,"Reflect",{getPrototypeOf:function(t){return n(i(t))}})},6721:function(t,e,r){var o=r(7171),n=r(1233),i=r(2923),a=r(661),s=r(775),c=r(3282);a(a.S,"Reflect",{get:function t(e,r){var a,u,l=arguments.length<3?e:arguments[2];return c(e)===l?e[r]:(a=o.f(e,r))?i(a,"value")?a.value:void 0!==a.get?a.get.call(l):void 0:s(u=n(e))?t(u,r,l):void 0}})},9498:function(t,e,r){var o=r(661);o(o.S,"Reflect",{has:function(t,e){return e in t}})},9247:function(t,e,r){var o=r(661),n=r(3282),i=Object.isExtensible;o(o.S,"Reflect",{isExtensible:function(t){return n(t),!i||i(t)}})},1646:function(t,e,r){var o=r(661);o(o.S,"Reflect",{ownKeys:r(3992)})},2310:function(t,e,r){var o=r(661),n=r(3282),i=Object.preventExtensions;o(o.S,"Reflect",{preventExtensions:function(t){n(t);try{return i&&i(t),!0}catch(t){return!1}}})},2630:function(t,e,r){var o=r(661),n=r(412);n&&o(o.S,"Reflect",{setPrototypeOf:function(t,e){n.check(t,e);try{return n.set(t,e),!0}catch(t){return!1}}})},2261:function(t,e,r){var o=r(3473),n=r(7171),i=r(1233),a=r(2923),s=r(661),c=r(6194),u=r(3282),l=r(775);s(s.S,"Reflect",{set:function t(e,r,s){var p,f,d=arguments.length<4?e:arguments[3],h=n.f(u(e),r);if(!h){if(l(f=i(e)))return t(f,r,s,d);h=c(0)}if(a(h,"value")){if(!1===h.writable||!l(d))return!1;if(p=n.f(d,r)){if(p.get||p.set||!1===p.writable)return!1;p.value=s,o.f(d,r,p)}else o.f(d,r,c(0,s));return!0}return void 0!==h.set&&(h.set.call(d,s),!0)}})},6835:function(t,e,r){var o=r(5464),n=r(7278),i=r(3473).f,a=r(685).f,s=r(2209),c=r(4492),u=o.RegExp,l=u,p=u.prototype,f=/a/g,d=/a/g,h=new u(f)!==f;if(r(6869)&&(!h||r(189)((function(){return d[r(1572)("match")]=!1,u(f)!=f||u(d)==d||"/a/i"!=u(f,"i")})))){u=function(t,e){var r=this instanceof u,o=s(t),i=void 0===e;return!r&&o&&t.constructor===u&&i?t:n(h?new l(o&&!i?t.source:t,e):l((o=t instanceof u)?t.source:t,o&&i?c.call(t):e),r?this:p,u)};for(var m=function(t){t in u||i(u,t,{configurable:!0,get:function(){return l[t]},set:function(e){l[t]=e}})},y=a(l),g=0;y.length>g;)m(y[g++]);p.constructor=u,u.prototype=p,r(7117)(o,"RegExp",u)}r(7348)("RegExp")},8018:function(t,e,r){"use strict";var o=r(6950);r(661)({target:"RegExp",proto:!0,forced:o!==/./.exec},{exec:o})},4888:function(t,e,r){r(6869)&&"g"!=/./g.flags&&r(3473).f(RegExp.prototype,"flags",{configurable:!0,get:r(4492)})},6466:function(t,e,r){"use strict";var o=r(3282),n=r(9043),i=r(8258),a=r(7513);r(4910)("match",1,(function(t,e,r,s){return[function(r){var o=t(this),n=null==r?void 0:r[e];return void 0!==n?n.call(r,o):new RegExp(r)[e](String(o))},function(t){var e=s(r,t,this);if(e.done)return e.value;var c=o(t),u=String(this);if(!c.global)return a(c,u);var l=c.unicode;c.lastIndex=0;for(var p,f=[],d=0;null!==(p=a(c,u));){var h=String(p[0]);f[d]=h,""===h&&(c.lastIndex=i(u,n(c.lastIndex),l)),d++}return 0===d?null:f}]}))},1751:function(t,e,r){"use strict";var o=r(3282),n=r(4004),i=r(9043),a=r(5941),s=r(8258),c=r(7513),u=Math.max,l=Math.min,p=Math.floor,f=/\$([$&`']|\d\d?|<[^>]*>)/g,d=/\$([$&`']|\d\d?)/g;r(4910)("replace",2,(function(t,e,r,h){return[function(o,n){var i=t(this),a=null==o?void 0:o[e];return void 0!==a?a.call(o,i,n):r.call(String(i),o,n)},function(t,e){var n=h(r,t,this,e);if(n.done)return n.value;var p=o(t),f=String(this),d="function"==typeof e;d||(e=String(e));var y=p.global;if(y){var g=p.unicode;p.lastIndex=0}for(var v=[];;){var b=c(p,f);if(null===b)break;if(v.push(b),!y)break;""===String(b[0])&&(p.lastIndex=s(f,i(p.lastIndex),g))}for(var A,x="",w=0,S=0;S<v.length;S++){b=v[S];for(var P=String(b[0]),k=u(l(a(b.index),f.length),0),C=[],O=1;O<b.length;O++)C.push(void 0===(A=b[O])?A:String(A));var E=b.groups;if(d){var T=[P].concat(C,k,f);void 0!==E&&T.push(E);var B=String(e.apply(void 0,T))}else B=m(P,f,k,C,E,e);k>=w&&(x+=f.slice(w,k)+B,w=k+P.length)}return x+f.slice(w)}];function m(t,e,o,i,a,s){var c=o+t.length,u=i.length,l=d;return void 0!==a&&(a=n(a),l=f),r.call(s,l,(function(r,n){var s;switch(n.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,o);case"'":return e.slice(c);case"<":s=a[n.slice(1,-1)];break;default:var l=+n;if(0===l)return r;if(l>u){var f=p(l/10);return 0===f?r:f<=u?void 0===i[f-1]?n.charAt(1):i[f-1]+n.charAt(1):r}s=i[l-1]}return void 0===s?"":s}))}}))},5531:function(t,e,r){"use strict";var o=r(3282),n=r(5077),i=r(7513);r(4910)("search",1,(function(t,e,r,a){return[function(r){var o=t(this),n=null==r?void 0:r[e];return void 0!==n?n.call(r,o):new RegExp(r)[e](String(o))},function(t){var e=a(r,t,this);if(e.done)return e.value;var s=o(t),c=String(this),u=s.lastIndex;n(u,0)||(s.lastIndex=0);var l=i(s,c);return n(s.lastIndex,u)||(s.lastIndex=u),null===l?-1:l.index}]}))},1383:function(t,e,r){"use strict";var o=r(2209),n=r(3282),i=r(9060),a=r(8258),s=r(9043),c=r(7513),u=r(6950),l=r(189),p=Math.min,f=[].push,d="split",h="length",m="lastIndex",y=4294967295,g=!l((function(){RegExp(y,"y")}));r(4910)("split",2,(function(t,e,r,l){var v;return v="c"=="abbc"[d](/(b)*/)[1]||4!="test"[d](/(?:)/,-1)[h]||2!="ab"[d](/(?:ab)*/)[h]||4!="."[d](/(.?)(.?)/)[h]||"."[d](/()()/)[h]>1||""[d](/.?/)[h]?function(t,e){var n=String(this);if(void 0===t&&0===e)return[];if(!o(t))return r.call(n,t,e);for(var i,a,s,c=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,d=void 0===e?y:e>>>0,g=new RegExp(t.source,l+"g");(i=u.call(g,n))&&!((a=g[m])>p&&(c.push(n.slice(p,i.index)),i[h]>1&&i.index<n[h]&&f.apply(c,i.slice(1)),s=i[0][h],p=a,c[h]>=d));)g[m]===i.index&&g[m]++;return p===n[h]?!s&&g.test("")||c.push(""):c.push(n.slice(p)),c[h]>d?c.slice(0,d):c}:"0"[d](void 0,0)[h]?function(t,e){return void 0===t&&0===e?[]:r.call(this,t,e)}:r,[function(r,o){var n=t(this),i=null==r?void 0:r[e];return void 0!==i?i.call(r,n,o):v.call(String(n),r,o)},function(t,e){var o=l(v,t,this,e,v!==r);if(o.done)return o.value;var u=n(t),f=String(this),d=i(u,RegExp),h=u.unicode,m=(u.ignoreCase?"i":"")+(u.multiline?"m":"")+(u.unicode?"u":"")+(g?"y":"g"),b=new d(g?u:"^(?:"+u.source+")",m),A=void 0===e?y:e>>>0;if(0===A)return[];if(0===f.length)return null===c(b,f)?[f]:[];for(var x=0,w=0,S=[];w<f.length;){b.lastIndex=g?w:0;var P,k=c(b,g?f:f.slice(w));if(null===k||(P=p(s(b.lastIndex+(g?0:w)),f.length))===x)w=a(f,w,h);else{if(S.push(f.slice(x,w)),S.length===A)return S;for(var C=1;C<=k.length-1;C++)if(S.push(k[C]),S.length===A)return S;w=x=P}}return S.push(f.slice(x)),S}]}))},4806:function(t,e,r){"use strict";r(4888);var o=r(3282),n=r(4492),i=r(6869),a="toString",s=/./[a],c=function(t){r(7117)(RegExp.prototype,a,t,!0)};r(189)((function(){return"/a/b"!=s.call({source:"a",flags:"b"})}))?c((function(){var t=o(this);return"/".concat(t.source,"/","flags"in t?t.flags:!i&&t instanceof RegExp?n.call(t):void 0)})):s.name!=a&&c((function(){return s.call(this)}))},914:function(t,e,r){"use strict";var o=r(4327),n=r(6970);t.exports=r(6103)("Set",(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return o.def(n(this,"Set"),t=0===t?0:t,t)}},o)},2154:function(t,e,r){"use strict";r(6134)("anchor",(function(t){return function(e){return t(this,"a","name",e)}}))},7385:function(t,e,r){"use strict";r(6134)("big",(function(t){return function(){return t(this,"big","","")}}))},3569:function(t,e,r){"use strict";r(6134)("blink",(function(t){return function(){return t(this,"blink","","")}}))},7424:function(t,e,r){"use strict";r(6134)("bold",(function(t){return function(){return t(this,"b","","")}}))},8299:function(t,e,r){"use strict";var o=r(661),n=r(2742)(!1);o(o.P,"String",{codePointAt:function(t){return n(this,t)}})},8194:function(t,e,r){"use strict";var o=r(661),n=r(9043),i=r(9856),a="endsWith",s=""[a];o(o.P+o.F*r(9897)(a),"String",{endsWith:function(t){var e=i(this,t,a),r=arguments.length>1?arguments[1]:void 0,o=n(e.length),c=void 0===r?o:Math.min(n(r),o),u=String(t);return s?s.call(e,u,c):e.slice(c-u.length,c)===u}})},9597:function(t,e,r){"use strict";r(6134)("fixed",(function(t){return function(){return t(this,"tt","","")}}))},7535:function(t,e,r){"use strict";r(6134)("fontcolor",(function(t){return function(e){return t(this,"font","color",e)}}))},3347:function(t,e,r){"use strict";r(6134)("fontsize",(function(t){return function(e){return t(this,"font","size",e)}}))},6510:function(t,e,r){var o=r(661),n=r(8075),i=String.fromCharCode,a=String.fromCodePoint;o(o.S+o.F*(!!a&&1!=a.length),"String",{fromCodePoint:function(t){for(var e,r=[],o=arguments.length,a=0;o>a;){if(e=+arguments[a++],n(e,1114111)!==e)throw RangeError(e+" is not a valid code point");r.push(e<65536?i(e):i(55296+((e-=65536)>>10),e%1024+56320))}return r.join("")}})},3806:function(t,e,r){"use strict";var o=r(661),n=r(9856),i="includes";o(o.P+o.F*r(9897)(i),"String",{includes:function(t){return!!~n(this,t,i).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},4762:function(t,e,r){"use strict";r(6134)("italics",(function(t){return function(){return t(this,"i","","")}}))},8733:function(t,e,r){"use strict";var o=r(2742)(!0);r(6621)(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,r=this._i;return r>=e.length?{value:void 0,done:!0}:(t=o(e,r),this._i+=t.length,{value:t,done:!1})}))},1871:function(t,e,r){"use strict";r(6134)("link",(function(t){return function(e){return t(this,"a","href",e)}}))},2065:function(t,e,r){var o=r(661),n=r(7703),i=r(9043);o(o.S,"String",{raw:function(t){for(var e=n(t.raw),r=i(e.length),o=arguments.length,a=[],s=0;r>s;)a.push(String(e[s++])),s<o&&a.push(String(arguments[s]));return a.join("")}})},4852:function(t,e,r){var o=r(661);o(o.P,"String",{repeat:r(1100)})},244:function(t,e,r){"use strict";r(6134)("small",(function(t){return function(){return t(this,"small","","")}}))},8279:function(t,e,r){"use strict";var o=r(661),n=r(9043),i=r(9856),a="startsWith",s=""[a];o(o.P+o.F*r(9897)(a),"String",{startsWith:function(t){var e=i(this,t,a),r=n(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),o=String(t);return s?s.call(e,o,r):e.slice(r,r+o.length)===o}})},797:function(t,e,r){"use strict";r(6134)("strike",(function(t){return function(){return t(this,"strike","","")}}))},7781:function(t,e,r){"use strict";r(6134)("sub",(function(t){return function(){return t(this,"sub","","")}}))},7043:function(t,e,r){"use strict";r(6134)("sup",(function(t){return function(){return t(this,"sup","","")}}))},771:function(t,e,r){"use strict";r(9803)("trim",(function(t){return function(){return t(this,3)}}))},9920:function(t,e,r){"use strict";function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=r(5464),i=r(2923),a=r(6869),s=r(661),c=r(7117),u=r(1994).KEY,l=r(189),p=r(7330),f=r(6290),d=r(5497),h=r(1572),m=r(5078),y=r(8354),g=r(6319),v=r(4887),b=r(3282),A=r(775),x=r(4004),w=r(7703),S=r(4250),P=r(6194),k=r(1889),C=r(2835),O=r(7171),E=r(7546),T=r(3473),B=r(7177),M=O.f,R=T.f,_=C.f,I=n.Symbol,j=n.JSON,D=j&&j.stringify,L="prototype",q=h("_hidden"),F=h("toPrimitive"),N={}.propertyIsEnumerable,U=p("symbol-registry"),G=p("symbols"),H=p("op-symbols"),z=Object[L],K="function"==typeof I&&!!E.f,W=n.QObject,V=!W||!W[L]||!W[L].findChild,Q=a&&l((function(){return 7!=k(R({},"a",{get:function(){return R(this,"a",{value:7}).a}})).a}))?function(t,e,r){var o=M(z,e);o&&delete z[e],R(t,e,r),o&&t!==z&&R(z,e,o)}:R,X=function(t){var e=G[t]=k(I[L]);return e._k=t,e},$=K&&"symbol"==o(I.iterator)?function(t){return"symbol"==o(t)}:function(t){return t instanceof I},Y=function(t,e,r){return t===z&&Y(H,e,r),b(t),e=S(e,!0),b(r),i(G,e)?(r.enumerable?(i(t,q)&&t[q][e]&&(t[q][e]=!1),r=k(r,{enumerable:P(0,!1)})):(i(t,q)||R(t,q,P(1,{})),t[q][e]=!0),Q(t,e,r)):R(t,e,r)},Z=function(t,e){b(t);for(var r,o=g(e=w(e)),n=0,i=o.length;i>n;)Y(t,r=o[n++],e[r]);return t},J=function(t){var e=N.call(this,t=S(t,!0));return!(this===z&&i(G,t)&&!i(H,t))&&(!(e||!i(this,t)||!i(G,t)||i(this,q)&&this[q][t])||e)},tt=function(t,e){if(t=w(t),e=S(e,!0),t!==z||!i(G,e)||i(H,e)){var r=M(t,e);return!r||!i(G,e)||i(t,q)&&t[q][e]||(r.enumerable=!0),r}},et=function(t){for(var e,r=_(w(t)),o=[],n=0;r.length>n;)i(G,e=r[n++])||e==q||e==u||o.push(e);return o},rt=function(t){for(var e,r=t===z,o=_(r?H:w(t)),n=[],a=0;o.length>a;)!i(G,e=o[a++])||r&&!i(z,e)||n.push(G[e]);return n};K||(I=function(){if(this instanceof I)throw TypeError("Symbol is not a constructor!");var t=d(arguments.length>0?arguments[0]:void 0);return a&&V&&Q(z,t,{configurable:!0,set:function e(r){this===z&&e.call(H,r),i(this,q)&&i(this[q],t)&&(this[q][t]=!1),Q(this,t,P(1,r))}}),X(t)},c(I[L],"toString",(function(){return this._k})),O.f=tt,T.f=Y,r(685).f=C.f=et,r(8815).f=J,E.f=rt,a&&!r(68)&&c(z,"propertyIsEnumerable",J,!0),m.f=function(t){return X(h(t))}),s(s.G+s.W+s.F*!K,{Symbol:I});for(var ot="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),nt=0;ot.length>nt;)h(ot[nt++]);for(var it=B(h.store),at=0;it.length>at;)y(it[at++]);s(s.S+s.F*!K,"Symbol",{for:function(t){return i(U,t+="")?U[t]:U[t]=I(t)},keyFor:function(t){if(!$(t))throw TypeError(t+" is not a symbol!");for(var e in U)if(U[e]===t)return e},useSetter:function(){V=!0},useSimple:function(){V=!1}}),s(s.S+s.F*!K,"Object",{create:function(t,e){return void 0===e?k(t):Z(k(t),e)},defineProperty:Y,defineProperties:Z,getOwnPropertyDescriptor:tt,getOwnPropertyNames:et,getOwnPropertySymbols:rt});var st=l((function(){E.f(1)}));s(s.S+s.F*st,"Object",{getOwnPropertySymbols:function(t){return E.f(x(t))}}),j&&s(s.S+s.F*(!K||l((function(){var t=I();return"[null]"!=D([t])||"{}"!=D({a:t})||"{}"!=D(Object(t))}))),"JSON",{stringify:function(t){for(var e,r,o=[t],n=1;arguments.length>n;)o.push(arguments[n++]);if(r=e=o[1],(A(e)||void 0!==t)&&!$(t))return v(e)||(e=function(t,e){if("function"==typeof r&&(e=r.call(this,t,e)),!$(e))return e}),o[1]=e,D.apply(j,o)}}),I[L][F]||r(3507)(I[L],F,I[L].valueOf),f(I,"Symbol"),f(Math,"Math",!0),f(n.JSON,"JSON",!0)},2688:function(t,e,r){"use strict";var o=r(661),n=r(2063),i=r(7022),a=r(3282),s=r(8075),c=r(9043),u=r(775),l=r(5464).ArrayBuffer,p=r(9060),f=i.ArrayBuffer,d=i.DataView,h=n.ABV&&l.isView,m=f.prototype.slice,y=n.VIEW,g="ArrayBuffer";o(o.G+o.W+o.F*(l!==f),{ArrayBuffer:f}),o(o.S+o.F*!n.CONSTR,g,{isView:function(t){return h&&h(t)||u(t)&&y in t}}),o(o.P+o.U+o.F*r(189)((function(){return!new f(2).slice(1,void 0).byteLength})),g,{slice:function(t,e){if(void 0!==m&&void 0===e)return m.call(a(this),t);for(var r=a(this).byteLength,o=s(t,r),n=s(void 0===e?r:e,r),i=new(p(this,f))(c(n-o)),u=new d(this),l=new d(i),h=0;o<n;)l.setUint8(h++,u.getUint8(o++));return i}}),r(7348)(g)},2574:function(t,e,r){var o=r(661);o(o.G+o.W+o.F*!r(2063).ABV,{DataView:r(7022).DataView})},8391:function(t,e,r){r(7699)("Float32",4,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},2e3:function(t,e,r){r(7699)("Float64",8,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},3502:function(t,e,r){r(7699)("Int16",2,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},6904:function(t,e,r){r(7699)("Int32",4,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},3957:function(t,e,r){r(7699)("Int8",1,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},4341:function(t,e,r){r(7699)("Uint16",2,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},7443:function(t,e,r){r(7699)("Uint32",4,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},9492:function(t,e,r){r(7699)("Uint8",1,(function(t){return function(e,r,o){return t(this,e,r,o)}}))},975:function(t,e,r){r(7699)("Uint8",1,(function(t){return function(e,r,o){return t(this,e,r,o)}}),!0)},7679:function(t,e,r){"use strict";var o,n=r(5464),i=r(1013)(0),a=r(7117),s=r(1994),c=r(2888),u=r(1312),l=r(775),p=r(6970),f=r(6970),d=!n.ActiveXObject&&"ActiveXObject"in n,h="WeakMap",m=s.getWeak,y=Object.isExtensible,g=u.ufstore,v=function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}},b={get:function(t){if(l(t)){var e=m(t);return!0===e?g(p(this,h)).get(t):e?e[this._i]:void 0}},set:function(t,e){return u.def(p(this,h),t,e)}},A=t.exports=r(6103)(h,v,b,u,!0,!0);f&&d&&(c((o=u.getConstructor(v,h)).prototype,b),s.NEED=!0,i(["delete","has","get","set"],(function(t){var e=A.prototype,r=e[t];a(e,t,(function(e,n){if(l(e)&&!y(e)){this._f||(this._f=new o);var i=this._f[t](e,n);return"set"==t?this:i}return r.call(this,e,n)}))})))},5057:function(t,e,r){"use strict";var o=r(1312),n=r(6970),i="WeakSet";r(6103)(i,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return o.def(n(this,i),t,!0)}},o,!1,!0)},4800:function(t,e,r){"use strict";var o=r(661),n=r(6472),i=r(4004),a=r(9043),s=r(93),c=r(9478);o(o.P,"Array",{flatMap:function(t){var e,r,o=i(this);return s(t),e=a(o.length),r=c(o,0),n(r,o,o,e,0,1,t,arguments[1]),r}}),r(7562)("flatMap")},7297:function(t,e,r){"use strict";var o=r(661),n=r(5386)(!0);o(o.P,"Array",{includes:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),r(7562)("includes")},5060:function(t,e,r){var o=r(661),n=r(2436)(!0);o(o.S,"Object",{entries:function(t){return n(t)}})},6788:function(t,e,r){var o=r(661),n=r(3992),i=r(7703),a=r(7171),s=r(905);o(o.S,"Object",{getOwnPropertyDescriptors:function(t){for(var e,r,o=i(t),c=a.f,u=n(o),l={},p=0;u.length>p;)void 0!==(r=c(o,e=u[p++]))&&s(l,e,r);return l}})},3036:function(t,e,r){var o=r(661),n=r(2436)(!1);o(o.S,"Object",{values:function(t){return n(t)}})},2677:function(t,e,r){"use strict";var o=r(661),n=r(1232),i=r(5464),a=r(9060),s=r(2123);o(o.P+o.R,"Promise",{finally:function(t){var e=a(this,n.Promise||i.Promise),r="function"==typeof t;return this.then(r?function(r){return s(e,t()).then((function(){return r}))}:t,r?function(r){return s(e,t()).then((function(){throw r}))}:t)}})},4055:function(t,e,r){"use strict";var o=r(661),n=r(2494),i=r(992),a=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(i);o(o.P+o.F*a,"String",{padEnd:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0,!1)}})},1834:function(t,e,r){"use strict";var o=r(661),n=r(2494),i=r(992),a=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(i);o(o.P+o.F*a,"String",{padStart:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0,!0)}})},3372:function(t,e,r){"use strict";r(9803)("trimLeft",(function(t){return function(){return t(this,1)}}),"trimStart")},8343:function(t,e,r){"use strict";r(9803)("trimRight",(function(t){return function(){return t(this,2)}}),"trimEnd")},1554:function(t,e,r){r(8354)("asyncIterator")},6764:function(t,e,r){for(var o=r(9279),n=r(7177),i=r(7117),a=r(5464),s=r(3507),c=r(100),u=r(1572),l=u("iterator"),p=u("toStringTag"),f=c.Array,d={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},h=n(d),m=0;m<h.length;m++){var y,g=h[m],v=d[g],b=a[g],A=b&&b.prototype;if(A&&(A[l]||s(A,l,f),A[p]||s(A,p,g),c[g]=f,v))for(y in o)A[y]||i(A,y,o[y],!0)}},4511:function(t,e,r){var o=r(661),n=r(4042);o(o.G+o.B,{setImmediate:n.set,clearImmediate:n.clear})},4414:function(t,e,r){var o=r(5464),n=r(661),i=r(992),a=[].slice,s=/MSIE .\./.test(i),c=function(t){return function(e,r){var o=arguments.length>2,n=!!o&&a.call(arguments,2);return t(o?function(){("function"==typeof e?e:Function(e)).apply(this,n)}:e,r)}};n(n.G+n.B+n.F*s,{setTimeout:c(o.setTimeout),setInterval:c(o.setInterval)})},6349:function(t,e,r){r(4414),r(4511),r(6764),t.exports=r(1232)},2885:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.BlockCipher,o=e.algo,n=[],i=[],a=[],s=[],c=[],u=[],l=[],p=[],f=[],d=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,o=0;for(e=0;e<256;e++){var h=o^o<<1^o<<2^o<<3^o<<4;h=h>>>8^255&h^99,n[r]=h,i[h]=r;var m=t[r],y=t[m],g=t[y],v=257*t[h]^16843008*h;a[r]=v<<24|v>>>8,s[r]=v<<16|v>>>16,c[r]=v<<8|v>>>24,u[r]=v,v=16843009*g^65537*y^257*m^16843008*r,l[h]=v<<24|v>>>8,p[h]=v<<16|v>>>16,f[h]=v<<8|v>>>24,d[h]=v,r?(r=m^t[t[t[g^m]]],o^=t[t[o]]):r=o=1}}();var h=[0,1,2,4,8,16,32,64,128,27,54],m=o.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,o=4*((this._nRounds=r+6)+1),i=this._keySchedule=[],a=0;a<o;a++)a<r?i[a]=e[a]:(u=i[a-1],a%r?r>6&&a%r==4&&(u=n[u>>>24]<<24|n[u>>>16&255]<<16|n[u>>>8&255]<<8|n[255&u]):(u=n[(u=u<<8|u>>>24)>>>24]<<24|n[u>>>16&255]<<16|n[u>>>8&255]<<8|n[255&u],u^=h[a/r|0]<<24),i[a]=i[a-r]^u);for(var s=this._invKeySchedule=[],c=0;c<o;c++){if(a=o-c,c%4)var u=i[a];else u=i[a-4];s[c]=c<4||a<=4?u:l[n[u>>>24]]^p[n[u>>>16&255]]^f[n[u>>>8&255]]^d[n[255&u]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,a,s,c,u,n)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,l,p,f,d,i),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,o,n,i,a,s){for(var c=this._nRounds,u=t[e]^r[0],l=t[e+1]^r[1],p=t[e+2]^r[2],f=t[e+3]^r[3],d=4,h=1;h<c;h++){var m=o[u>>>24]^n[l>>>16&255]^i[p>>>8&255]^a[255&f]^r[d++],y=o[l>>>24]^n[p>>>16&255]^i[f>>>8&255]^a[255&u]^r[d++],g=o[p>>>24]^n[f>>>16&255]^i[u>>>8&255]^a[255&l]^r[d++],v=o[f>>>24]^n[u>>>16&255]^i[l>>>8&255]^a[255&p]^r[d++];u=m,l=y,p=g,f=v}m=(s[u>>>24]<<24|s[l>>>16&255]<<16|s[p>>>8&255]<<8|s[255&f])^r[d++],y=(s[l>>>24]<<24|s[p>>>16&255]<<16|s[f>>>8&255]<<8|s[255&u])^r[d++],g=(s[p>>>24]<<24|s[f>>>16&255]<<16|s[u>>>8&255]<<8|s[255&l])^r[d++],v=(s[f>>>24]<<24|s[u>>>16&255]<<16|s[l>>>8&255]<<8|s[255&p])^r[d++],t[e]=m,t[e+1]=y,t[e+2]=g,t[e+3]=v},keySize:8});e.AES=r._createHelper(m)}(),t.AES},"object"===s(e)?t.exports=e=a(r(8019),r(4760),r(5258),r(8832),r(9127)):(n=[r(8019),r(4760),r(5258),r(8832),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},7158:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.BlockCipher,o=e.algo,n=16,i=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],a=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]],s={pbox:[],sbox:[]};function c(t,e){var r=e>>24&255,o=e>>16&255,n=e>>8&255,i=255&e,a=t.sbox[0][r]+t.sbox[1][o];return(a^=t.sbox[2][n])+t.sbox[3][i]}function u(t,e,r){for(var o,i=e,a=r,s=0;s<n;++s)o=i^=t.pbox[s],i=a=c(t,i)^a,a=o;return o=i,i=a,a=o,a^=t.pbox[n],{left:i^=t.pbox[17],right:a}}var l=o.Blowfish=r.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4;!function(t,e,r){for(var o=0;o<4;o++){t.sbox[o]=[];for(var n=0;n<256;n++)t.sbox[o][n]=a[o][n]}for(var s=0,c=0;c<18;c++)t.pbox[c]=i[c]^e[s],++s>=r&&(s=0);for(var l=0,p=0,f=0,d=0;d<18;d+=2)l=(f=u(t,l,p)).left,p=f.right,t.pbox[d]=l,t.pbox[d+1]=p;for(var h=0;h<4;h++)for(var m=0;m<256;m+=2)l=(f=u(t,l,p)).left,p=f.right,t.sbox[h][m]=l,t.sbox[h][m+1]=p}(s,e,r)}},encryptBlock:function(t,e){var r=u(s,t[e],t[e+1]);t[e]=r.left,t[e+1]=r.right},decryptBlock:function(t,e){var r=function(t,e,r){for(var o,n=e,i=r,a=17;a>1;--a)o=n^=t.pbox[a],n=i=c(t,n)^i,i=o;return o=n,n=i,i=o,i^=t.pbox[1],{left:n^=t.pbox[0],right:i}}(s,t[e],t[e+1]);t[e]=r.left,t[e+1]=r.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=r._createHelper(l)}(),t.Blowfish},"object"===s(e)?t.exports=e=a(r(8019),r(4760),r(5258),r(8832),r(9127)):(n=[r(8019),r(4760),r(5258),r(8832),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},9127:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i,a,s,c,u,l,p,f,d,h,m,y,g,v;t.lib.Cipher||(o=(r=(e=t).lib).Base,n=r.WordArray,i=r.BufferedBlockAlgorithm,(a=e.enc).Utf8,s=a.Base64,c=e.algo.EvpKDF,u=r.Cipher=i.extend({cfg:o.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?v:y}return function(e){return{encrypt:function(r,o,n){return t(o).encrypt(e,r,o,n)},decrypt:function(r,o,n){return t(o).decrypt(e,r,o,n)}}}}()}),r.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),l=e.mode={},p=r.BlockCipherMode=o.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),f=l.CBC=function(){var t=p.extend();function e(t,e,r){var o,n=this._iv;n?(o=n,this._iv=void 0):o=this._prevBlock;for(var i=0;i<r;i++)t[e+i]^=o[i]}return t.Encryptor=t.extend({processBlock:function(t,r){var o=this._cipher,n=o.blockSize;e.call(this,t,r,n),o.encryptBlock(t,r),this._prevBlock=t.slice(r,r+n)}}),t.Decryptor=t.extend({processBlock:function(t,r){var o=this._cipher,n=o.blockSize,i=t.slice(r,r+n);o.decryptBlock(t,r),e.call(this,t,r,n),this._prevBlock=i}}),t}(),d=(e.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,o=r-t.sigBytes%r,i=o<<24|o<<16|o<<8|o,a=[],s=0;s<o;s+=4)a.push(i);var c=n.create(a,o);t.concat(c)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},r.BlockCipher=u.extend({cfg:u.cfg.extend({mode:f,padding:d}),reset:function(){var t;u.reset.call(this);var e=this.cfg,r=e.iv,o=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=o.createEncryptor:(t=o.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(o,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4}),h=r.CipherParams=o.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),m=(e.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,r=t.salt;return(r?n.create([1398893684,1701076831]).concat(r).concat(e):e).toString(s)},parse:function(t){var e,r=s.parse(t),o=r.words;return 1398893684==o[0]&&1701076831==o[1]&&(e=n.create(o.slice(2,4)),o.splice(0,4),r.sigBytes-=16),h.create({ciphertext:r,salt:e})}},y=r.SerializableCipher=o.extend({cfg:o.extend({format:m}),encrypt:function(t,e,r,o){o=this.cfg.extend(o);var n=t.createEncryptor(r,o),i=n.finalize(e),a=n.cfg;return h.create({ciphertext:i,key:r,iv:a.iv,algorithm:t,mode:a.mode,padding:a.padding,blockSize:t.blockSize,formatter:o.format})},decrypt:function(t,e,r,o){return o=this.cfg.extend(o),e=this._parse(e,o.format),t.createDecryptor(r,o).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),g=(e.kdf={}).OpenSSL={execute:function(t,e,r,o,i){if(o||(o=n.random(8)),i)a=c.create({keySize:e+r,hasher:i}).compute(t,o);else var a=c.create({keySize:e+r}).compute(t,o);var s=n.create(a.words.slice(e),4*r);return a.sigBytes=4*e,h.create({key:a,iv:s,salt:o})}},v=r.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:g}),encrypt:function(t,e,r,o){var n=(o=this.cfg.extend(o)).kdf.execute(r,t.keySize,t.ivSize,o.salt,o.hasher);o.iv=n.iv;var i=y.encrypt.call(this,t,e,n.key,o);return i.mixIn(n),i},decrypt:function(t,e,r,o){o=this.cfg.extend(o),e=this._parse(e,o.format);var n=o.kdf.execute(r,t.keySize,t.ivSize,e.salt,o.hasher);return o.iv=n.iv,y.decrypt.call(this,t,e,n.key,o)}}))},"object"===s(e)?t.exports=e=a(r(8019),r(8832)):(n=[r(8019),r(8832)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8019:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(){var t=t||function(t,e){var o;if("undefined"!=typeof window&&window.crypto&&(o=window.crypto),"undefined"!=typeof self&&self.crypto&&(o=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(o=globalThis.crypto),!o&&"undefined"!=typeof window&&window.msCrypto&&(o=window.msCrypto),!o&&void 0!==r.g&&r.g.crypto&&(o=r.g.crypto),!o)try{o=r(477)}catch(t){}var n=function(){if(o){if("function"==typeof o.getRandomValues)try{return o.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof o.randomBytes)try{return o.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function t(){}return function(e){var r;return t.prototype=e,r=new t,t.prototype=null,r}}(),a={},s=a.lib={},c=s.Base={extend:function(t){var e=i(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},u=s.WordArray=c.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||p).stringify(this)},concat:function(t){var e=this.words,r=t.words,o=this.sigBytes,n=t.sigBytes;if(this.clamp(),o%4)for(var i=0;i<n;i++){var a=r[i>>>2]>>>24-i%4*8&255;e[o+i>>>2]|=a<<24-(o+i)%4*8}else for(var s=0;s<n;s+=4)e[o+s>>>2]=r[s>>>2];return this.sigBytes+=n,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=c.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(n());return new u.init(e,t)}}),l=a.enc={},p=l.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,o=[],n=0;n<r;n++){var i=e[n>>>2]>>>24-n%4*8&255;o.push((i>>>4).toString(16)),o.push((15&i).toString(16))}return o.join("")},parse:function(t){for(var e=t.length,r=[],o=0;o<e;o+=2)r[o>>>3]|=parseInt(t.substr(o,2),16)<<24-o%8*4;return new u.init(r,e/2)}},f=l.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,o=[],n=0;n<r;n++){var i=e[n>>>2]>>>24-n%4*8&255;o.push(String.fromCharCode(i))}return o.join("")},parse:function(t){for(var e=t.length,r=[],o=0;o<e;o++)r[o>>>2]|=(255&t.charCodeAt(o))<<24-o%4*8;return new u.init(r,e)}},d=l.Utf8={stringify:function(t){try{return decodeURIComponent(escape(f.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return f.parse(unescape(encodeURIComponent(t)))}},h=s.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=d.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r,o=this._data,n=o.words,i=o.sigBytes,a=this.blockSize,s=i/(4*a),c=(s=e?t.ceil(s):t.max((0|s)-this._minBufferSize,0))*a,l=t.min(4*c,i);if(c){for(var p=0;p<c;p+=a)this._doProcessBlock(n,p);r=n.splice(0,c),o.sigBytes-=l}return new u.init(r,l)},clone:function(){var t=c.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),m=(s.Hasher=h.extend({cfg:c.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){h.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new m.HMAC.init(t,r).finalize(e)}}}),a.algo={});return a}(Math);return t},"object"===s(e)?t.exports=e=a():(n=[],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},4760:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r;return r=(e=t).lib.WordArray,e.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,o=this._map;t.clamp();for(var n=[],i=0;i<r;i+=3)for(var a=(e[i>>>2]>>>24-i%4*8&255)<<16|(e[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|e[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<r;s++)n.push(o.charAt(a>>>6*(3-s)&63));var c=o.charAt(64);if(c)for(;n.length%4;)n.push(c);return n.join("")},parse:function(t){var e=t.length,o=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var i=0;i<o.length;i++)n[o.charCodeAt(i)]=i}var a=o.charAt(64);if(a){var s=t.indexOf(a);-1!==s&&(e=s)}return function(t,e,o){for(var n=[],i=0,a=0;a<e;a++)if(a%4){var s=o[t.charCodeAt(a-1)]<<a%4*2|o[t.charCodeAt(a)]>>>6-a%4*2;n[i>>>2]|=s<<24-i%4*8,i++}return r.create(n,i)}(t,e,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},t.enc.Base64},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2643:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r;return r=(e=t).lib.WordArray,e.enc.Base64url={stringify:function(t,e){void 0===e&&(e=!0);var r=t.words,o=t.sigBytes,n=e?this._safe_map:this._map;t.clamp();for(var i=[],a=0;a<o;a+=3)for(var s=(r[a>>>2]>>>24-a%4*8&255)<<16|(r[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|r[a+2>>>2]>>>24-(a+2)%4*8&255,c=0;c<4&&a+.75*c<o;c++)i.push(n.charAt(s>>>6*(3-c)&63));var u=n.charAt(64);if(u)for(;i.length%4;)i.push(u);return i.join("")},parse:function(t,e){void 0===e&&(e=!0);var o=t.length,n=e?this._safe_map:this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var a=0;a<n.length;a++)i[n.charCodeAt(a)]=a}var s=n.charAt(64);if(s){var c=t.indexOf(s);-1!==c&&(o=c)}return function(t,e,o){for(var n=[],i=0,a=0;a<e;a++)if(a%4){var s=o[t.charCodeAt(a-1)]<<a%4*2|o[t.charCodeAt(a)]>>>6-a%4*2;n[i>>>2]|=s<<24-i%4*8,i++}return r.create(n,i)}(t,o,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},t.enc.Base64url},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},5997:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.WordArray,o=e.enc;function n(t){return t<<8&4278255360|t>>>8&16711935}o.Utf16=o.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,o=[],n=0;n<r;n+=2){var i=e[n>>>2]>>>16-n%4*8&65535;o.push(String.fromCharCode(i))}return o.join("")},parse:function(t){for(var e=t.length,o=[],n=0;n<e;n++)o[n>>>1]|=t.charCodeAt(n)<<16-n%2*16;return r.create(o,2*e)}},o.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,o=[],i=0;i<r;i+=2){var a=n(e[i>>>2]>>>16-i%4*8&65535);o.push(String.fromCharCode(a))}return o.join("")},parse:function(t){for(var e=t.length,o=[],i=0;i<e;i++)o[i>>>1]|=n(t.charCodeAt(i)<<16-i%2*16);return r.create(o,2*e)}}}(),t.enc.Utf16},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8832:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i,a,s;return o=(r=(e=t).lib).Base,n=r.WordArray,a=(i=e.algo).MD5,s=i.EvpKDF=o.extend({cfg:o.extend({keySize:4,hasher:a,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r,o=this.cfg,i=o.hasher.create(),a=n.create(),s=a.words,c=o.keySize,u=o.iterations;s.length<c;){r&&i.update(r),r=i.update(t).finalize(e),i.reset();for(var l=1;l<u;l++)r=i.finalize(r),i.reset();a.concat(r)}return a.sigBytes=4*c,a}}),e.EvpKDF=function(t,e,r){return s.create(r).compute(t,e)},t.EvpKDF},"object"===s(e)?t.exports=e=a(r(8019),r(4965),r(4135)):(n=[r(8019),r(4965),r(4135)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8143:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o;return r=(e=t).lib.CipherParams,o=e.enc.Hex,e.format.Hex={stringify:function(t){return t.ciphertext.toString(o)},parse:function(t){var e=o.parse(t);return r.create({ciphertext:e})}},t.format.Hex},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},4135:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o;r=(e=t).lib.Base,o=e.enc.Utf8,e.algo.HMAC=r.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=o.parse(e));var r=t.blockSize,n=4*r;e.sigBytes>n&&(e=t.finalize(e)),e.clamp();for(var i=this._oKey=e.clone(),a=this._iKey=e.clone(),s=i.words,c=a.words,u=0;u<r;u++)s[u]^=1549556828,c[u]^=909522486;i.sigBytes=a.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,r=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(r))}})},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2062:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t},"object"===s(e)?t.exports=e=a(r(8019),r(2730),r(9054),r(5997),r(4760),r(2643),r(5258),r(4965),r(4183),r(2202),r(8542),r(4047),r(6947),r(8334),r(4135),r(1381),r(8832),r(9127),r(4651),r(8665),r(5218),r(8751),r(8676),r(1307),r(2819),r(6004),r(2469),r(5026),r(8143),r(2885),r(7274),r(5615),r(6768),r(2570),r(7158)):(n=[r(8019),r(2730),r(9054),r(5997),r(4760),r(2643),r(5258),r(4965),r(4183),r(2202),r(8542),r(4047),r(6947),r(8334),r(4135),r(1381),r(8832),r(9127),r(4651),r(8665),r(5218),r(8751),r(8676),r(1307),r(2819),r(6004),r(2469),r(5026),r(8143),r(2885),r(7274),r(5615),r(6768),r(2570),r(7158)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},9054:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){if("function"==typeof ArrayBuffer){var e=t.lib.WordArray,r=e.init,o=e.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var e=t.byteLength,o=[],n=0;n<e;n++)o[n>>>2]|=t[n]<<24-n%4*8;r.call(this,o,e)}else r.apply(this,arguments)};o.prototype=e}}(),t.lib.WordArray},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},5258:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(e){var r=t,o=r.lib,n=o.WordArray,i=o.Hasher,a=r.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=a.MD5=i.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var o=e+r,n=t[o];t[o]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var i=this._hash.words,a=t[e+0],c=t[e+1],d=t[e+2],h=t[e+3],m=t[e+4],y=t[e+5],g=t[e+6],v=t[e+7],b=t[e+8],A=t[e+9],x=t[e+10],w=t[e+11],S=t[e+12],P=t[e+13],k=t[e+14],C=t[e+15],O=i[0],E=i[1],T=i[2],B=i[3];O=u(O,E,T,B,a,7,s[0]),B=u(B,O,E,T,c,12,s[1]),T=u(T,B,O,E,d,17,s[2]),E=u(E,T,B,O,h,22,s[3]),O=u(O,E,T,B,m,7,s[4]),B=u(B,O,E,T,y,12,s[5]),T=u(T,B,O,E,g,17,s[6]),E=u(E,T,B,O,v,22,s[7]),O=u(O,E,T,B,b,7,s[8]),B=u(B,O,E,T,A,12,s[9]),T=u(T,B,O,E,x,17,s[10]),E=u(E,T,B,O,w,22,s[11]),O=u(O,E,T,B,S,7,s[12]),B=u(B,O,E,T,P,12,s[13]),T=u(T,B,O,E,k,17,s[14]),O=l(O,E=u(E,T,B,O,C,22,s[15]),T,B,c,5,s[16]),B=l(B,O,E,T,g,9,s[17]),T=l(T,B,O,E,w,14,s[18]),E=l(E,T,B,O,a,20,s[19]),O=l(O,E,T,B,y,5,s[20]),B=l(B,O,E,T,x,9,s[21]),T=l(T,B,O,E,C,14,s[22]),E=l(E,T,B,O,m,20,s[23]),O=l(O,E,T,B,A,5,s[24]),B=l(B,O,E,T,k,9,s[25]),T=l(T,B,O,E,h,14,s[26]),E=l(E,T,B,O,b,20,s[27]),O=l(O,E,T,B,P,5,s[28]),B=l(B,O,E,T,d,9,s[29]),T=l(T,B,O,E,v,14,s[30]),O=p(O,E=l(E,T,B,O,S,20,s[31]),T,B,y,4,s[32]),B=p(B,O,E,T,b,11,s[33]),T=p(T,B,O,E,w,16,s[34]),E=p(E,T,B,O,k,23,s[35]),O=p(O,E,T,B,c,4,s[36]),B=p(B,O,E,T,m,11,s[37]),T=p(T,B,O,E,v,16,s[38]),E=p(E,T,B,O,x,23,s[39]),O=p(O,E,T,B,P,4,s[40]),B=p(B,O,E,T,a,11,s[41]),T=p(T,B,O,E,h,16,s[42]),E=p(E,T,B,O,g,23,s[43]),O=p(O,E,T,B,A,4,s[44]),B=p(B,O,E,T,S,11,s[45]),T=p(T,B,O,E,C,16,s[46]),O=f(O,E=p(E,T,B,O,d,23,s[47]),T,B,a,6,s[48]),B=f(B,O,E,T,v,10,s[49]),T=f(T,B,O,E,k,15,s[50]),E=f(E,T,B,O,y,21,s[51]),O=f(O,E,T,B,S,6,s[52]),B=f(B,O,E,T,h,10,s[53]),T=f(T,B,O,E,x,15,s[54]),E=f(E,T,B,O,c,21,s[55]),O=f(O,E,T,B,b,6,s[56]),B=f(B,O,E,T,C,10,s[57]),T=f(T,B,O,E,g,15,s[58]),E=f(E,T,B,O,P,21,s[59]),O=f(O,E,T,B,m,6,s[60]),B=f(B,O,E,T,w,10,s[61]),T=f(T,B,O,E,d,15,s[62]),E=f(E,T,B,O,A,21,s[63]),i[0]=i[0]+O|0,i[1]=i[1]+E|0,i[2]=i[2]+T|0,i[3]=i[3]+B|0},_doFinalize:function(){var t=this._data,r=t.words,o=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var i=e.floor(o/4294967296),a=o;r[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),r[14+(n+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});function u(t,e,r,o,n,i,a){var s=t+(e&r|~e&o)+n+a;return(s<<i|s>>>32-i)+e}function l(t,e,r,o,n,i,a){var s=t+(e&o|r&~o)+n+a;return(s<<i|s>>>32-i)+e}function p(t,e,r,o,n,i,a){var s=t+(e^r^o)+n+a;return(s<<i|s>>>32-i)+e}function f(t,e,r,o,n,i,a){var s=t+(r^(e|~o))+n+a;return(s<<i|s>>>32-i)+e}r.MD5=i._createHelper(c),r.HmacMD5=i._createHmacHelper(c)}(Math),t.MD5},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},4651:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();function r(t,e,r,o){var n,i=this._iv;i?(n=i.slice(0),this._iv=void 0):n=this._prevBlock,o.encryptBlock(n,0);for(var a=0;a<r;a++)t[e+a]^=n[a]}return e.Encryptor=e.extend({processBlock:function(t,e){var o=this._cipher,n=o.blockSize;r.call(this,t,e,n,o),this._prevBlock=t.slice(e,e+n)}}),e.Decryptor=e.extend({processBlock:function(t,e){var o=this._cipher,n=o.blockSize,i=t.slice(e,e+n);r.call(this,t,e,n,o),this._prevBlock=i}}),e}(),t.mode.CFB},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},5218:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function r(t){if(255&~(t>>24))t+=1<<24;else{var e=t>>16&255,r=t>>8&255,o=255&t;255===e?(e=0,255===r?(r=0,255===o?o=0:++o):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=o}return t}var o=e.Encryptor=e.extend({processBlock:function(t,e){var o=this._cipher,n=o.blockSize,i=this._iv,a=this._counter;i&&(a=this._counter=i.slice(0),this._iv=void 0),function(t){0===(t[0]=r(t[0]))&&(t[1]=r(t[1]))}(a);var s=a.slice(0);o.encryptBlock(s,0);for(var c=0;c<n;c++)t[e+c]^=s[c]}});return e.Decryptor=o,e}(),t.mode.CTRGladman},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8665:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r;return t.mode.CTR=(r=(e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,o=r.blockSize,n=this._iv,i=this._counter;n&&(i=this._counter=n.slice(0),this._iv=void 0);var a=i.slice(0);r.encryptBlock(a,0),i[o-1]=i[o-1]+1|0;for(var s=0;s<o;s++)t[e+s]^=a[s]}}),e.Decryptor=r,e),t.mode.CTR},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8676:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e;return t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),e.Decryptor=e.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),e),t.mode.ECB},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8751:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r;return t.mode.OFB=(r=(e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,o=r.blockSize,n=this._iv,i=this._keystream;n&&(i=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(i,0);for(var a=0;a<o;a++)t[e+a]^=i[a]}}),e.Decryptor=r,e),t.mode.OFB},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},1307:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,o=4*e,n=o-r%o,i=r+n-1;t.clamp(),t.words[i>>>2]|=n<<24-i%4*8,t.sigBytes+=n},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Ansix923},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2819:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.pad.Iso10126={pad:function(e,r){var o=4*r,n=o-e.sigBytes%o;e.concat(t.lib.WordArray.random(n-1)).concat(t.lib.WordArray.create([n<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Iso10126},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},6004:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.pad.Iso97971={pad:function(e,r){e.concat(t.lib.WordArray.create([2147483648],1)),t.pad.ZeroPadding.pad(e,r)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},5026:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2469:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return t.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){var e=t.words,r=t.sigBytes-1;for(r=t.sigBytes-1;r>=0;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},t.pad.ZeroPadding},"object"===s(e)?t.exports=e=a(r(8019),r(9127)):(n=[r(8019),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},1381:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i,a,s,c;return o=(r=(e=t).lib).Base,n=r.WordArray,a=(i=e.algo).SHA256,s=i.HMAC,c=i.PBKDF2=o.extend({cfg:o.extend({keySize:4,hasher:a,iterations:25e4}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,o=s.create(r.hasher,t),i=n.create(),a=n.create([1]),c=i.words,u=a.words,l=r.keySize,p=r.iterations;c.length<l;){var f=o.update(e).finalize(a);o.reset();for(var d=f.words,h=d.length,m=f,y=1;y<p;y++){m=o.finalize(m),o.reset();for(var g=m.words,v=0;v<h;v++)d[v]^=g[v]}i.concat(f),u[0]++}return i.sigBytes=4*l,i}}),e.PBKDF2=function(t,e,r){return c.create(r).compute(t,e)},t.PBKDF2},"object"===s(e)?t.exports=e=a(r(8019),r(4183),r(4135)):(n=[r(8019),r(4183),r(4135)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2570:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.StreamCipher,o=e.algo,n=[],i=[],a=[],s=o.RabbitLegacy=r.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],o=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var n=0;n<4;n++)c.call(this);for(n=0;n<8;n++)o[n]^=r[n+4&7];if(e){var i=e.words,a=i[0],s=i[1],u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),p=u>>>16|4294901760&l,f=l<<16|65535&u;for(o[0]^=u,o[1]^=p,o[2]^=l,o[3]^=f,o[4]^=u,o[5]^=p,o[6]^=l,o[7]^=f,n=0;n<4;n++)c.call(this)}},_doProcessBlock:function(t,e){var r=this._X;c.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var o=0;o<4;o++)n[o]=16711935&(n[o]<<8|n[o]>>>24)|4278255360&(n[o]<<24|n[o]>>>8),t[e+o]^=n[o]},blockSize:4,ivSize:2});function c(){for(var t=this._X,e=this._C,r=0;r<8;r++)i[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<i[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<i[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<i[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<i[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<i[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<i[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<i[6]>>>0?1:0)|0,this._b=e[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var o=t[r]+e[r],n=65535&o,s=o>>>16,c=((n*n>>>17)+n*s>>>15)+s*s,u=((4294901760&o)*o|0)+((65535&o)*o|0);a[r]=c^u}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=r._createHelper(s)}(),t.RabbitLegacy},"object"===s(e)?t.exports=e=a(r(8019),r(4760),r(5258),r(8832),r(9127)):(n=[r(8019),r(4760),r(5258),r(8832),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},6768:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.StreamCipher,o=e.algo,n=[],i=[],a=[],s=o.Rabbit=r.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);var o=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];for(this._b=0,r=0;r<4;r++)c.call(this);for(r=0;r<8;r++)n[r]^=o[r+4&7];if(e){var i=e.words,a=i[0],s=i[1],u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),p=u>>>16|4294901760&l,f=l<<16|65535&u;for(n[0]^=u,n[1]^=p,n[2]^=l,n[3]^=f,n[4]^=u,n[5]^=p,n[6]^=l,n[7]^=f,r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(t,e){var r=this._X;c.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var o=0;o<4;o++)n[o]=16711935&(n[o]<<8|n[o]>>>24)|4278255360&(n[o]<<24|n[o]>>>8),t[e+o]^=n[o]},blockSize:4,ivSize:2});function c(){for(var t=this._X,e=this._C,r=0;r<8;r++)i[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<i[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<i[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<i[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<i[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<i[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<i[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<i[6]>>>0?1:0)|0,this._b=e[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var o=t[r]+e[r],n=65535&o,s=o>>>16,c=((n*n>>>17)+n*s>>>15)+s*s,u=((4294901760&o)*o|0)+((65535&o)*o|0);a[r]=c^u}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=r._createHelper(s)}(),t.Rabbit},"object"===s(e)?t.exports=e=a(r(8019),r(4760),r(5258),r(8832),r(9127)):(n=[r(8019),r(4760),r(5258),r(8832),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},5615:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.StreamCipher,o=e.algo,n=o.RC4=r.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,o=this._S=[],n=0;n<256;n++)o[n]=n;n=0;for(var i=0;n<256;n++){var a=n%r,s=e[a>>>2]>>>24-a%4*8&255;i=(i+o[n]+s)%256;var c=o[n];o[n]=o[i],o[i]=c}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var t=this._S,e=this._i,r=this._j,o=0,n=0;n<4;n++){r=(r+t[e=(e+1)%256])%256;var i=t[e];t[e]=t[r],t[r]=i,o|=t[(t[e]+t[r])%256]<<24-8*n}return this._i=e,this._j=r,o}e.RC4=r._createHelper(n);var a=o.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)i.call(this)}});e.RC4Drop=r._createHelper(a)}(),t.RC4},"object"===s(e)?t.exports=e=a(r(8019),r(4760),r(5258),r(8832),r(9127)):(n=[r(8019),r(4760),r(5258),r(8832),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8334:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(e){var r=t,o=r.lib,n=o.WordArray,i=o.Hasher,a=r.algo,s=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),u=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),p=n.create([0,1518500249,1859775393,2400959708,2840853838]),f=n.create([1352829926,1548603684,1836072691,2053994217,0]),d=a.RIPEMD160=i.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var o=e+r,n=t[o];t[o]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var i,a,d,A,x,w,S,P,k,C,O,E=this._hash.words,T=p.words,B=f.words,M=s.words,R=c.words,_=u.words,I=l.words;for(w=i=E[0],S=a=E[1],P=d=E[2],k=A=E[3],C=x=E[4],r=0;r<80;r+=1)O=i+t[e+M[r]]|0,O+=r<16?h(a,d,A)+T[0]:r<32?m(a,d,A)+T[1]:r<48?y(a,d,A)+T[2]:r<64?g(a,d,A)+T[3]:v(a,d,A)+T[4],O=(O=b(O|=0,_[r]))+x|0,i=x,x=A,A=b(d,10),d=a,a=O,O=w+t[e+R[r]]|0,O+=r<16?v(S,P,k)+B[0]:r<32?g(S,P,k)+B[1]:r<48?y(S,P,k)+B[2]:r<64?m(S,P,k)+B[3]:h(S,P,k)+B[4],O=(O=b(O|=0,I[r]))+C|0,w=C,C=k,k=b(P,10),P=S,S=O;O=E[1]+d+k|0,E[1]=E[2]+A+C|0,E[2]=E[3]+x+w|0,E[3]=E[4]+i+S|0,E[4]=E[0]+a+P|0,E[0]=O},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;e[o>>>5]|=128<<24-o%32,e[14+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var n=this._hash,i=n.words,a=0;a<5;a++){var s=i[a];i[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return n},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});function h(t,e,r){return t^e^r}function m(t,e,r){return t&e|~t&r}function y(t,e,r){return(t|~e)^r}function g(t,e,r){return t&r|e&~r}function v(t,e,r){return t^(e|~r)}function b(t,e){return t<<e|t>>>32-e}r.RIPEMD160=i._createHelper(d),r.HmacRIPEMD160=i._createHmacHelper(d)}(Math),t.RIPEMD160},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},4965:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i,a,s;return r=(e=t).lib,o=r.WordArray,n=r.Hasher,i=e.algo,a=[],s=i.SHA1=n.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,o=r[0],n=r[1],i=r[2],s=r[3],c=r[4],u=0;u<80;u++){if(u<16)a[u]=0|t[e+u];else{var l=a[u-3]^a[u-8]^a[u-14]^a[u-16];a[u]=l<<1|l>>>31}var p=(o<<5|o>>>27)+c+a[u];p+=u<20?1518500249+(n&i|~n&s):u<40?1859775393+(n^i^s):u<60?(n&i|n&s|i&s)-1894007588:(n^i^s)-899497514,c=s,s=i,i=n<<30|n>>>2,n=o,o=p}r[0]=r[0]+o|0,r[1]=r[1]+n|0,r[2]=r[2]+i|0,r[3]=r[3]+s|0,r[4]=r[4]+c|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return e[o>>>5]|=128<<24-o%32,e[14+(o+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(o+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t}}),e.SHA1=n._createHelper(s),e.HmacSHA1=n._createHmacHelper(s),t.SHA1},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2202:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i;return r=(e=t).lib.WordArray,o=e.algo,n=o.SHA256,i=o.SHA224=n.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=n._doFinalize.call(this);return t.sigBytes-=4,t}}),e.SHA224=n._createHelper(i),e.HmacSHA224=n._createHmacHelper(i),t.SHA224},"object"===s(e)?t.exports=e=a(r(8019),r(4183)):(n=[r(8019),r(4183)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},4183:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(e){var r=t,o=r.lib,n=o.WordArray,i=o.Hasher,a=r.algo,s=[],c=[];!function(){function t(t){for(var r=e.sqrt(t),o=2;o<=r;o++)if(!(t%o))return!1;return!0}function r(t){return 4294967296*(t-(0|t))|0}for(var o=2,n=0;n<64;)t(o)&&(n<8&&(s[n]=r(e.pow(o,.5))),c[n]=r(e.pow(o,1/3)),n++),o++}();var u=[],l=a.SHA256=i.extend({_doReset:function(){this._hash=new n.init(s.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,o=r[0],n=r[1],i=r[2],a=r[3],s=r[4],l=r[5],p=r[6],f=r[7],d=0;d<64;d++){if(d<16)u[d]=0|t[e+d];else{var h=u[d-15],m=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,y=u[d-2],g=(y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10;u[d]=m+u[d-7]+g+u[d-16]}var v=o&n^o&i^n&i,b=(o<<30|o>>>2)^(o<<19|o>>>13)^(o<<10|o>>>22),A=f+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&p)+c[d]+u[d];f=p,p=l,l=s,s=a+A|0,a=i,i=n,n=o,o=A+(b+v)|0}r[0]=r[0]+o|0,r[1]=r[1]+n|0,r[2]=r[2]+i|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+l|0,r[6]=r[6]+p|0,r[7]=r[7]+f|0},_doFinalize:function(){var t=this._data,r=t.words,o=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=e.floor(o/4294967296),r[15+(n+64>>>9<<4)]=o,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});r.SHA256=i._createHelper(l),r.HmacSHA256=i._createHmacHelper(l)}(Math),t.SHA256},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},6947:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(e){var r=t,o=r.lib,n=o.WordArray,i=o.Hasher,a=r.x64.Word,s=r.algo,c=[],u=[],l=[];!function(){for(var t=1,e=0,r=0;r<24;r++){c[t+5*e]=(r+1)*(r+2)/2%64;var o=(2*t+3*e)%5;t=e%5,e=o}for(t=0;t<5;t++)for(e=0;e<5;e++)u[t+5*e]=e+(2*t+3*e)%5*5;for(var n=1,i=0;i<24;i++){for(var s=0,p=0,f=0;f<7;f++){if(1&n){var d=(1<<f)-1;d<32?p^=1<<d:s^=1<<d-32}128&n?n=n<<1^113:n<<=1}l[i]=a.create(s,p)}}();var p=[];!function(){for(var t=0;t<25;t++)p[t]=a.create()}();var f=s.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,o=this.blockSize/2,n=0;n<o;n++){var i=t[e+2*n],a=t[e+2*n+1];i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(E=r[n]).high^=a,E.low^=i}for(var s=0;s<24;s++){for(var f=0;f<5;f++){for(var d=0,h=0,m=0;m<5;m++)d^=(E=r[f+5*m]).high,h^=E.low;var y=p[f];y.high=d,y.low=h}for(f=0;f<5;f++){var g=p[(f+4)%5],v=p[(f+1)%5],b=v.high,A=v.low;for(d=g.high^(b<<1|A>>>31),h=g.low^(A<<1|b>>>31),m=0;m<5;m++)(E=r[f+5*m]).high^=d,E.low^=h}for(var x=1;x<25;x++){var w=(E=r[x]).high,S=E.low,P=c[x];P<32?(d=w<<P|S>>>32-P,h=S<<P|w>>>32-P):(d=S<<P-32|w>>>64-P,h=w<<P-32|S>>>64-P);var k=p[u[x]];k.high=d,k.low=h}var C=p[0],O=r[0];for(C.high=O.high,C.low=O.low,f=0;f<5;f++)for(m=0;m<5;m++){var E=r[x=f+5*m],T=p[x],B=p[(f+1)%5+5*m],M=p[(f+2)%5+5*m];E.high=T.high^~B.high&M.high,E.low=T.low^~B.low&M.low}E=r[0];var R=l[s];E.high^=R.high,E.low^=R.low}},_doFinalize:function(){var t=this._data,r=t.words,o=(this._nDataBytes,8*t.sigBytes),i=32*this.blockSize;r[o>>>5]|=1<<24-o%32,r[(e.ceil((o+1)/i)*i>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,c=s/8,u=[],l=0;l<c;l++){var p=a[l],f=p.high,d=p.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),u.push(d),u.push(f)}return new n.init(u,s)},clone:function(){for(var t=i.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});r.SHA3=i._createHelper(f),r.HmacSHA3=i._createHmacHelper(f)}(Math),t.SHA3},"object"===s(e)?t.exports=e=a(r(8019),r(2730)):(n=[r(8019),r(2730)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},4047:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i,a,s;return r=(e=t).x64,o=r.Word,n=r.WordArray,i=e.algo,a=i.SHA512,s=i.SHA384=a.extend({_doReset:function(){this._hash=new n.init([new o.init(3418070365,3238371032),new o.init(1654270250,914150663),new o.init(2438529370,812702999),new o.init(355462360,4144912697),new o.init(1731405415,4290775857),new o.init(2394180231,1750603025),new o.init(3675008525,1694076839),new o.init(1203062813,3204075428)])},_doFinalize:function(){var t=a._doFinalize.call(this);return t.sigBytes-=16,t}}),e.SHA384=a._createHelper(s),e.HmacSHA384=a._createHmacHelper(s),t.SHA384},"object"===s(e)?t.exports=e=a(r(8019),r(2730),r(8542)):(n=[r(8019),r(2730),r(8542)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},8542:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib.Hasher,o=e.x64,n=o.Word,i=o.WordArray,a=e.algo;function s(){return n.create.apply(n,arguments)}var c=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],u=[];!function(){for(var t=0;t<80;t++)u[t]=s()}();var l=a.SHA512=r.extend({_doReset:function(){this._hash=new i.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,o=r[0],n=r[1],i=r[2],a=r[3],s=r[4],l=r[5],p=r[6],f=r[7],d=o.high,h=o.low,m=n.high,y=n.low,g=i.high,v=i.low,b=a.high,A=a.low,x=s.high,w=s.low,S=l.high,P=l.low,k=p.high,C=p.low,O=f.high,E=f.low,T=d,B=h,M=m,R=y,_=g,I=v,j=b,D=A,L=x,q=w,F=S,N=P,U=k,G=C,H=O,z=E,K=0;K<80;K++){var W,V,Q=u[K];if(K<16)V=Q.high=0|t[e+2*K],W=Q.low=0|t[e+2*K+1];else{var X=u[K-15],$=X.high,Y=X.low,Z=($>>>1|Y<<31)^($>>>8|Y<<24)^$>>>7,J=(Y>>>1|$<<31)^(Y>>>8|$<<24)^(Y>>>7|$<<25),tt=u[K-2],et=tt.high,rt=tt.low,ot=(et>>>19|rt<<13)^(et<<3|rt>>>29)^et>>>6,nt=(rt>>>19|et<<13)^(rt<<3|et>>>29)^(rt>>>6|et<<26),it=u[K-7],at=it.high,st=it.low,ct=u[K-16],ut=ct.high,lt=ct.low;V=(V=(V=Z+at+((W=J+st)>>>0<J>>>0?1:0))+ot+((W+=nt)>>>0<nt>>>0?1:0))+ut+((W+=lt)>>>0<lt>>>0?1:0),Q.high=V,Q.low=W}var pt,ft=L&F^~L&U,dt=q&N^~q&G,ht=T&M^T&_^M&_,mt=B&R^B&I^R&I,yt=(T>>>28|B<<4)^(T<<30|B>>>2)^(T<<25|B>>>7),gt=(B>>>28|T<<4)^(B<<30|T>>>2)^(B<<25|T>>>7),vt=(L>>>14|q<<18)^(L>>>18|q<<14)^(L<<23|q>>>9),bt=(q>>>14|L<<18)^(q>>>18|L<<14)^(q<<23|L>>>9),At=c[K],xt=At.high,wt=At.low,St=H+vt+((pt=z+bt)>>>0<z>>>0?1:0),Pt=gt+mt;H=U,z=G,U=F,G=N,F=L,N=q,L=j+(St=(St=(St=St+ft+((pt+=dt)>>>0<dt>>>0?1:0))+xt+((pt+=wt)>>>0<wt>>>0?1:0))+V+((pt+=W)>>>0<W>>>0?1:0))+((q=D+pt|0)>>>0<D>>>0?1:0)|0,j=_,D=I,_=M,I=R,M=T,R=B,T=St+(yt+ht+(Pt>>>0<gt>>>0?1:0))+((B=pt+Pt|0)>>>0<pt>>>0?1:0)|0}h=o.low=h+B,o.high=d+T+(h>>>0<B>>>0?1:0),y=n.low=y+R,n.high=m+M+(y>>>0<R>>>0?1:0),v=i.low=v+I,i.high=g+_+(v>>>0<I>>>0?1:0),A=a.low=A+D,a.high=b+j+(A>>>0<D>>>0?1:0),w=s.low=w+q,s.high=x+L+(w>>>0<q>>>0?1:0),P=l.low=P+N,l.high=S+F+(P>>>0<N>>>0?1:0),C=p.low=C+G,p.high=k+U+(C>>>0<G>>>0?1:0),E=f.low=E+z,f.high=O+H+(E>>>0<z>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return e[o>>>5]|=128<<24-o%32,e[30+(o+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(o+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});e.SHA512=r._createHelper(l),e.HmacSHA512=r._createHmacHelper(l)}(),t.SHA512},"object"===s(e)?t.exports=e=a(r(8019),r(2730)):(n=[r(8019),r(2730)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},7274:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){return function(){var e=t,r=e.lib,o=r.WordArray,n=r.BlockCipher,i=e.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],u=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],p=i.DES=n.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;r<56;r++){var o=a[r]-1;e[r]=t[o>>>5]>>>31-o%32&1}for(var n=this._subKeys=[],i=0;i<16;i++){var u=n[i]=[],l=c[i];for(r=0;r<24;r++)u[r/6|0]|=e[(s[r]-1+l)%28]<<31-r%6,u[4+(r/6|0)]|=e[28+(s[r+24]-1+l)%28]<<31-r%6;for(u[0]=u[0]<<1|u[0]>>>31,r=1;r<7;r++)u[r]=u[r]>>>4*(r-1)+3;u[7]=u[7]<<5|u[7]>>>27}var p=this._invSubKeys=[];for(r=0;r<16;r++)p[r]=n[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],f.call(this,4,252645135),f.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),f.call(this,1,1431655765);for(var o=0;o<16;o++){for(var n=r[o],i=this._lBlock,a=this._rBlock,s=0,c=0;c<8;c++)s|=u[c][((a^n[c])&l[c])>>>0];this._lBlock=a,this._rBlock=i^s}var p=this._lBlock;this._lBlock=this._rBlock,this._rBlock=p,f.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function f(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function d(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}e.DES=n._createHelper(p);var h=i.TripleDES=n.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),r=t.length<4?t.slice(0,2):t.slice(2,4),n=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=p.createEncryptor(o.create(e)),this._des2=p.createEncryptor(o.create(r)),this._des3=p.createEncryptor(o.create(n))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=n._createHelper(h)}(),t.TripleDES},"object"===s(e)?t.exports=e=a(r(8019),r(4760),r(5258),r(8832),r(9127)):(n=[r(8019),r(4760),r(5258),r(8832),r(9127)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},2730:function(t,e,r){var o,n,i,a;function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}a=function(t){var e,r,o,n,i;return r=(e=t).lib,o=r.Base,n=r.WordArray,(i=e.x64={}).Word=o.extend({init:function(t,e){this.high=t,this.low=e}}),i.WordArray=o.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],o=0;o<e;o++){var i=t[o];r.push(i.high),r.push(i.low)}return n.create(r,this.sigBytes)},clone:function(){for(var t=o.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}}),t},"object"===s(e)?t.exports=e=a(r(8019)):(n=[r(8019)],void 0===(i="function"==typeof(o=a)?o.apply(e,n):o)||(t.exports=i))},5846:function(t,e,r){function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}var n=function(t){"use strict";var e,r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},s=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function p(t,e,r,o){var n=e&&e.prototype instanceof v?e:v,a=Object.create(n.prototype),s=new M(o||[]);return i(a,"_invoke",{value:O(t,r,s)}),a}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=p;var d="suspendedStart",h="suspendedYield",m="executing",y="completed",g={};function v(){}function b(){}function A(){}var x={};l(x,s,(function(){return this}));var w=Object.getPrototypeOf,S=w&&w(w(R([])));S&&S!==r&&n.call(S,s)&&(x=S);var P=A.prototype=v.prototype=Object.create(x);function k(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function r(i,a,s,c){var u=f(t[i],t,a);if("throw"!==u.type){var l=u.arg,p=l.value;return p&&"object"===o(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){r("next",t,s,c)}),(function(t){r("throw",t,s,c)})):e.resolve(p).then((function(t){l.value=t,s(l)}),(function(t){return r("throw",t,s,c)}))}c(u.arg)}var a;i(this,"_invoke",{value:function(t,o){function n(){return new e((function(e,n){r(t,o,e,n)}))}return a=a?a.then(n,n):n()}})}function O(t,e,r){var o=d;return function(n,i){if(o===m)throw new Error("Generator is already running");if(o===y){if("throw"===n)throw i;return _()}for(r.method=n,r.arg=i;;){var a=r.delegate;if(a){var s=E(a,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var c=f(t,e,r);if("normal"===c.type){if(o=r.done?y:h,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=y,r.method="throw",r.arg=c.arg)}}}function E(t,r){var o=r.method,n=t.iterator[o];if(n===e)return r.delegate=null,"throw"===o&&t.iterator.return&&(r.method="return",r.arg=e,E(t,r),"throw"===r.method)||"return"!==o&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var i=f(n,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function T(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function B(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(T,this),this.reset(!0)}function R(t){if(t){var r=t[s];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}return{next:_}}function _(){return{value:e,done:!0}}return b.prototype=A,i(P,"constructor",{value:A,configurable:!0}),i(A,"constructor",{value:b,configurable:!0}),b.displayName=l(A,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,A):(t.__proto__=A,l(t,u,"GeneratorFunction")),t.prototype=Object.create(P),t},t.awrap=function(t){return{__await:t}},k(C.prototype),l(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,r,o,n,i){void 0===i&&(i=Promise);var a=new C(p(e,r,o,n),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(P),l(P,u,"Generator"),l(P,s,(function(){return this})),l(P,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=R,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(B),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(o,n){return s.type="throw",s.arg=t,r.next=o,n&&(r.method="next",r.arg=e),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),B(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var n=o.arg;B(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,o){return this.delegate={iterator:R(t),resultName:r,nextLoc:o},"next"===this.method&&(this.arg=e),g}},t}("object"===o(t=r.nmd(t))?t.exports:{});try{regeneratorRuntime=n}catch(t){"object"===("undefined"==typeof globalThis?"undefined":o(globalThis))?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},5472:function(t,e,r){var o,n;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}t=r.nmd(t),function(a,s){"use strict";"object"===i(t)&&t.exports?t.exports=s():void 0===(n="function"==typeof(o=s)?o.call(e,r,e,t):o)||(t.exports=n)}(0,(function(t){"use strict";var e=t&&t.IPv6;return{best:function(t){var e,r,o=t.toLowerCase().split(":"),n=o.length,i=8;for(""===o[0]&&""===o[1]&&""===o[2]?(o.shift(),o.shift()):""===o[0]&&""===o[1]?o.shift():""===o[n-1]&&""===o[n-2]&&o.pop(),-1!==o[(n=o.length)-1].indexOf(".")&&(i=7),e=0;e<n&&""!==o[e];e++);if(e<i)for(o.splice(e,1,"0000");o.length<i;)o.splice(e,0,"0000");for(var a=0;a<i;a++){r=o[a].split("");for(var s=0;s<3&&"0"===r[0]&&r.length>1;s++)r.splice(0,1);o[a]=r.join("")}var c=-1,u=0,l=0,p=-1,f=!1;for(a=0;a<i;a++)f?"0"===o[a]?l+=1:(f=!1,l>u&&(c=p,u=l)):"0"===o[a]&&(f=!0,p=a,l=1);l>u&&(c=p,u=l),u>1&&o.splice(c,u,""),n=o.length;var d="";for(""===o[0]&&(d=":"),a=0;a<n&&(d+=o[a],a!==n-1);a++)d+=":";return""===o[n-1]&&(d+=":"),d},noConflict:function(){return t.IPv6===this&&(t.IPv6=e),this}}}))},562:function(t,e,r){var o,n;function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}t=r.nmd(t),function(a,s){"use strict";"object"===i(t)&&t.exports?t.exports=s():void 0===(n="function"==typeof(o=s)?o.call(e,r,e,t):o)||(t.exports=n)}(0,(function(t){"use strict";var e=t&&t.SecondLevelDomains,r={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return!1;var o=t.lastIndexOf(".",e-1);if(o<=0||o>=e-1)return!1;var n=r.list[t.slice(e+1)];return!!n&&n.indexOf(" "+t.slice(o+1,e)+" ")>=0},is:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return!1;if(t.lastIndexOf(".",e-1)>=0)return!1;var o=r.list[t.slice(e+1)];return!!o&&o.indexOf(" "+t.slice(0,e)+" ")>=0},get:function(t){var e=t.lastIndexOf(".");if(e<=0||e>=t.length-1)return null;var o=t.lastIndexOf(".",e-1);if(o<=0||o>=e-1)return null;var n=r.list[t.slice(e+1)];return n?n.indexOf(" "+t.slice(o+1,e)+" ")<0?null:t.slice(o+1):null},noConflict:function(){return t.SecondLevelDomains===this&&(t.SecondLevelDomains=e),this}};return r}))},107:function(t,e,r){var o,n,i;function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}t=r.nmd(t),function(s,c){"use strict";"object"===a(t)&&t.exports?t.exports=c(r(9538),r(5472),r(562)):(n=[r(9538),r(5472),r(562)],void 0===(i="function"==typeof(o=c)?o.apply(e,n):o)||(t.exports=i))}(0,(function(t,e,r,o){"use strict";var n=o&&o.URI;function i(t,e){var r=arguments.length>=1;if(!(this instanceof i))return r?arguments.length>=2?new i(t,e):new i(t):new i;if(void 0===t){if(r)throw new TypeError("undefined is not a valid argument for URI");t="undefined"!=typeof location?location.href+"":""}if(null===t&&r)throw new TypeError("null is not a valid argument for URI");return this.href(t),void 0!==e?this.absoluteTo(e):this}i.version="1.19.11";var s=i.prototype,c=Object.prototype.hasOwnProperty;function u(t){return t.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(t){return void 0===t?"Undefined":String(Object.prototype.toString.call(t)).slice(8,-1)}function p(t){return"Array"===l(t)}function f(t,e){var r,o,n={};if("RegExp"===l(e))n=null;else if(p(e))for(r=0,o=e.length;r<o;r++)n[e[r]]=!0;else n[e]=!0;for(r=0,o=t.length;r<o;r++)(n&&void 0!==n[t[r]]||!n&&e.test(t[r]))&&(t.splice(r,1),o--,r--);return t}function d(t,e){var r,o;if(p(e)){for(r=0,o=e.length;r<o;r++)if(!d(t,e[r]))return!1;return!0}var n=l(e);for(r=0,o=t.length;r<o;r++)if("RegExp"===n){if("string"==typeof t[r]&&t[r].match(e))return!0}else if(t[r]===e)return!0;return!1}function h(t,e){if(!p(t)||!p(e))return!1;if(t.length!==e.length)return!1;t.sort(),e.sort();for(var r=0,o=t.length;r<o;r++)if(t[r]!==e[r])return!1;return!0}function m(t){return t.replace(/^\/+|\/+$/g,"")}function y(t){return escape(t)}function g(t){return encodeURIComponent(t).replace(/[!'()*]/g,y).replace(/\*/g,"%2A")}i._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:i.preventInvalidHostname,duplicateQueryParameters:i.duplicateQueryParameters,escapeQuerySpace:i.escapeQuerySpace}},i.preventInvalidHostname=!1,i.duplicateQueryParameters=!1,i.escapeQuerySpace=!0,i.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,i.idn_expression=/[^a-z0-9\._-]/i,i.punycode_expression=/(xn--)/i,i.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,i.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,i.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/gi,i.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},i.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,i.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,i.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},i.hostProtocols=["http","https"],i.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,i.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},i.getDomAttribute=function(t){if(t&&t.nodeName){var e=t.nodeName.toLowerCase();if("input"!==e||"image"===t.type)return i.domAttributes[e]}},i.encode=g,i.decode=decodeURIComponent,i.iso8859=function(){i.encode=escape,i.decode=unescape},i.unicode=function(){i.encode=g,i.decode=decodeURIComponent},i.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/gi,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/gi,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/gi,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},i.encodeQuery=function(t,e){var r=i.encode(t+"");return void 0===e&&(e=i.escapeQuerySpace),e?r.replace(/%20/g,"+"):r},i.decodeQuery=function(t,e){t+="",void 0===e&&(e=i.escapeQuerySpace);try{return i.decode(e?t.replace(/\+/g,"%20"):t)}catch(e){return t}};var v,b={encode:"encode",decode:"decode"},A=function(t,e){return function(r){try{return i[e](r+"").replace(i.characters[t][e].expression,(function(r){return i.characters[t][e].map[r]}))}catch(t){return r}}};for(v in b)i[v+"PathSegment"]=A("pathname",b[v]),i[v+"UrnPathSegment"]=A("urnpath",b[v]);var x=function(t,e,r){return function(o){var n;n=r?function(t){return i[e](i[r](t))}:i[e];for(var a=(o+"").split(t),s=0,c=a.length;s<c;s++)a[s]=n(a[s]);return a.join(t)}};function w(t){return function(e,r){return void 0===e?this._parts[t]||"":(this._parts[t]=e||null,this.build(!r),this)}}function S(t,e){return function(r,o){return void 0===r?this._parts[t]||"":(null!==r&&(r+="").charAt(0)===e&&(r=r.substring(1)),this._parts[t]=r,this.build(!o),this)}}i.decodePath=x("/","decodePathSegment"),i.decodeUrnPath=x(":","decodeUrnPathSegment"),i.recodePath=x("/","encodePathSegment","decode"),i.recodeUrnPath=x(":","encodeUrnPathSegment","decode"),i.encodeReserved=A("reserved","encode"),i.parse=function(t,e){var r;return e||(e={preventInvalidHostname:i.preventInvalidHostname}),(r=(t=(t=t.replace(i.leading_whitespace_expression,"")).replace(i.ascii_tab_whitespace,"")).indexOf("#"))>-1&&(e.fragment=t.substring(r+1)||null,t=t.substring(0,r)),(r=t.indexOf("?"))>-1&&(e.query=t.substring(r+1)||null,t=t.substring(0,r)),"//"===(t=(t=t.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://")).replace(/^[/\\]{2,}/i,"//")).substring(0,2)?(e.protocol=null,t=t.substring(2),t=i.parseAuthority(t,e)):(r=t.indexOf(":"))>-1&&(e.protocol=t.substring(0,r)||null,e.protocol&&!e.protocol.match(i.protocol_expression)?e.protocol=void 0:"//"===t.substring(r+1,r+3).replace(/\\/g,"/")?(t=t.substring(r+3),t=i.parseAuthority(t,e)):(t=t.substring(r+1),e.urn=!0)),e.path=t,e},i.parseHost=function(t,e){t||(t="");var r,o,n=(t=t.replace(/\\/g,"/")).indexOf("/");if(-1===n&&(n=t.length),"["===t.charAt(0))r=t.indexOf("]"),e.hostname=t.substring(1,r)||null,e.port=t.substring(r+2,n)||null,"/"===e.port&&(e.port=null);else{var a=t.indexOf(":"),s=t.indexOf("/"),c=t.indexOf(":",a+1);-1!==c&&(-1===s||c<s)?(e.hostname=t.substring(0,n)||null,e.port=null):(o=t.substring(0,n).split(":"),e.hostname=o[0]||null,e.port=o[1]||null)}return e.hostname&&"/"!==t.substring(n).charAt(0)&&(n++,t="/"+t),e.preventInvalidHostname&&i.ensureValidHostname(e.hostname,e.protocol),e.port&&i.ensureValidPort(e.port),t.substring(n)||"/"},i.parseAuthority=function(t,e){return t=i.parseUserinfo(t,e),i.parseHost(t,e)},i.parseUserinfo=function(t,e){var r=t;-1!==t.indexOf("\\")&&(t=t.replace(/\\/g,"/"));var o,n=t.indexOf("/"),a=t.lastIndexOf("@",n>-1?n:t.length-1);return a>-1&&(-1===n||a<n)?(o=t.substring(0,a).split(":"),e.username=o[0]?i.decode(o[0]):null,o.shift(),e.password=o[0]?i.decode(o.join(":")):null,t=r.substring(a+1)):(e.username=null,e.password=null),t},i.parseQuery=function(t,e){if(!t)return{};if(!(t=t.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,"")))return{};for(var r,o,n,a={},s=t.split("&"),u=s.length,l=0;l<u;l++)r=s[l].split("="),o=i.decodeQuery(r.shift(),e),n=r.length?i.decodeQuery(r.join("="),e):null,"__proto__"!==o&&(c.call(a,o)?("string"!=typeof a[o]&&null!==a[o]||(a[o]=[a[o]]),a[o].push(n)):a[o]=n);return a},i.build=function(t){var e="",r=!1;return t.protocol&&(e+=t.protocol+":"),t.urn||!e&&!t.hostname||(e+="//",r=!0),e+=i.buildAuthority(t)||"","string"==typeof t.path&&("/"!==t.path.charAt(0)&&r&&(e+="/"),e+=t.path),"string"==typeof t.query&&t.query&&(e+="?"+t.query),"string"==typeof t.fragment&&t.fragment&&(e+="#"+t.fragment),e},i.buildHost=function(t){var e="";return t.hostname?(i.ip6_expression.test(t.hostname)?e+="["+t.hostname+"]":e+=t.hostname,t.port&&(e+=":"+t.port),e):""},i.buildAuthority=function(t){return i.buildUserinfo(t)+i.buildHost(t)},i.buildUserinfo=function(t){var e="";return t.username&&(e+=i.encode(t.username)),t.password&&(e+=":"+i.encode(t.password)),e&&(e+="@"),e},i.buildQuery=function(t,e,r){var o,n,a,s,u="";for(n in t)if("__proto__"!==n&&c.call(t,n))if(p(t[n]))for(o={},a=0,s=t[n].length;a<s;a++)void 0!==t[n][a]&&void 0===o[t[n][a]+""]&&(u+="&"+i.buildQueryParameter(n,t[n][a],r),!0!==e&&(o[t[n][a]+""]=!0));else void 0!==t[n]&&(u+="&"+i.buildQueryParameter(n,t[n],r));return u.substring(1)},i.buildQueryParameter=function(t,e,r){return i.encodeQuery(t,r)+(null!==e?"="+i.encodeQuery(e,r):"")},i.addQuery=function(t,e,r){if("object"===a(e))for(var o in e)c.call(e,o)&&i.addQuery(t,o,e[o]);else{if("string"!=typeof e)throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");if(void 0===t[e])return void(t[e]=r);"string"==typeof t[e]&&(t[e]=[t[e]]),p(r)||(r=[r]),t[e]=(t[e]||[]).concat(r)}},i.setQuery=function(t,e,r){if("object"===a(e))for(var o in e)c.call(e,o)&&i.setQuery(t,o,e[o]);else{if("string"!=typeof e)throw new TypeError("URI.setQuery() accepts an object, string as the name parameter");t[e]=void 0===r?null:r}},i.removeQuery=function(t,e,r){var o,n,s;if(p(e))for(o=0,n=e.length;o<n;o++)t[e[o]]=void 0;else if("RegExp"===l(e))for(s in t)e.test(s)&&(t[s]=void 0);else if("object"===a(e))for(s in e)c.call(e,s)&&i.removeQuery(t,s,e[s]);else{if("string"!=typeof e)throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter");void 0!==r?"RegExp"===l(r)?!p(t[e])&&r.test(t[e])?t[e]=void 0:t[e]=f(t[e],r):t[e]!==String(r)||p(r)&&1!==r.length?p(t[e])&&(t[e]=f(t[e],r)):t[e]=void 0:t[e]=void 0}},i.hasQuery=function(t,e,r,o){switch(l(e)){case"String":break;case"RegExp":for(var n in t)if(c.call(t,n)&&e.test(n)&&(void 0===r||i.hasQuery(t,n,r)))return!0;return!1;case"Object":for(var a in e)if(c.call(e,a)&&!i.hasQuery(t,a,e[a]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(r)){case"Undefined":return e in t;case"Boolean":return r===Boolean(p(t[e])?t[e].length:t[e]);case"Function":return!!r(t[e],e,t);case"Array":return!!p(t[e])&&(o?d:h)(t[e],r);case"RegExp":return p(t[e])?!!o&&d(t[e],r):Boolean(t[e]&&t[e].match(r));case"Number":r=String(r);case"String":return p(t[e])?!!o&&d(t[e],r):t[e]===r;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},i.joinPaths=function(){for(var t=[],e=[],r=0,o=0;o<arguments.length;o++){var n=new i(arguments[o]);t.push(n);for(var a=n.segment(),s=0;s<a.length;s++)"string"==typeof a[s]&&e.push(a[s]),a[s]&&r++}if(!e.length||!r)return new i("");var c=new i("").segment(e);return""!==t[0].path()&&"/"!==t[0].path().slice(0,1)||c.path("/"+c.path()),c.normalize()},i.commonPath=function(t,e){var r,o=Math.min(t.length,e.length);for(r=0;r<o;r++)if(t.charAt(r)!==e.charAt(r)){r--;break}return r<1?t.charAt(0)===e.charAt(0)&&"/"===t.charAt(0)?"/":"":("/"===t.charAt(r)&&"/"===e.charAt(r)||(r=t.substring(0,r).lastIndexOf("/")),t.substring(0,r+1))},i.withinString=function(t,e,r){r||(r={});var o=r.start||i.findUri.start,n=r.end||i.findUri.end,a=r.trim||i.findUri.trim,s=r.parens||i.findUri.parens,c=/[a-z0-9-]=["']?$/i;for(o.lastIndex=0;;){var u=o.exec(t);if(!u)break;var l=u.index;if(r.ignoreHtml){var p=t.slice(Math.max(l-3,0),l);if(p&&c.test(p))continue}for(var f=l+t.slice(l).search(n),d=t.slice(l,f),h=-1;;){var m=s.exec(d);if(!m)break;var y=m.index+m[0].length;h=Math.max(h,y)}if(!((d=h>-1?d.slice(0,h)+d.slice(h).replace(a,""):d.replace(a,"")).length<=u[0].length||r.ignore&&r.ignore.test(d))){var g=e(d,l,f=l+d.length,t);void 0!==g?(g=String(g),t=t.slice(0,l)+g+t.slice(f),o.lastIndex=l+g.length):o.lastIndex=f}}return o.lastIndex=0,t},i.ensureValidHostname=function(e,r){var o=!!e,n=!1;if(!!r&&(n=d(i.hostProtocols,r)),n&&!o)throw new TypeError("Hostname cannot be empty, if protocol is "+r);if(e&&e.match(i.invalid_hostname_characters)){if(!t)throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(t.toASCII(e).match(i.invalid_hostname_characters))throw new TypeError('Hostname "'+e+'" contains characters other than [A-Z0-9.-:_]')}},i.ensureValidPort=function(t){if(t){var e=Number(t);if(!(/^[0-9]+$/.test(e)&&e>0&&e<65536))throw new TypeError('Port "'+t+'" is not a valid port')}},i.noConflict=function(t){if(t){var e={URI:this.noConflict()};return o.URITemplate&&"function"==typeof o.URITemplate.noConflict&&(e.URITemplate=o.URITemplate.noConflict()),o.IPv6&&"function"==typeof o.IPv6.noConflict&&(e.IPv6=o.IPv6.noConflict()),o.SecondLevelDomains&&"function"==typeof o.SecondLevelDomains.noConflict&&(e.SecondLevelDomains=o.SecondLevelDomains.noConflict()),e}return o.URI===this&&(o.URI=n),this},s.build=function(t){return!0===t?this._deferred_build=!0:(void 0===t||this._deferred_build)&&(this._string=i.build(this._parts),this._deferred_build=!1),this},s.clone=function(){return new i(this)},s.valueOf=s.toString=function(){return this.build(!1)._string},s.protocol=w("protocol"),s.username=w("username"),s.password=w("password"),s.hostname=w("hostname"),s.port=w("port"),s.query=S("query","?"),s.fragment=S("fragment","#"),s.search=function(t,e){var r=this.query(t,e);return"string"==typeof r&&r.length?"?"+r:r},s.hash=function(t,e){var r=this.fragment(t,e);return"string"==typeof r&&r.length?"#"+r:r},s.pathname=function(t,e){if(void 0===t||!0===t){var r=this._parts.path||(this._parts.hostname?"/":"");return t?(this._parts.urn?i.decodeUrnPath:i.decodePath)(r):r}return this._parts.urn?this._parts.path=t?i.recodeUrnPath(t):"":this._parts.path=t?i.recodePath(t):"/",this.build(!e),this},s.path=s.pathname,s.href=function(t,e){var r;if(void 0===t)return this.toString();this._string="",this._parts=i._parts();var o=t instanceof i,n="object"===a(t)&&(t.hostname||t.path||t.pathname);if(t.nodeName&&(t=t[i.getDomAttribute(t)]||"",n=!1),!o&&n&&void 0!==t.pathname&&(t=t.toString()),"string"==typeof t||t instanceof String)this._parts=i.parse(String(t),this._parts);else{if(!o&&!n)throw new TypeError("invalid input");var s=o?t._parts:t;for(r in s)"query"!==r&&c.call(this._parts,r)&&(this._parts[r]=s[r]);s.query&&this.query(s.query,!1)}return this.build(!e),this},s.is=function(t){var e=!1,o=!1,n=!1,a=!1,s=!1,c=!1,u=!1,l=!this._parts.urn;switch(this._parts.hostname&&(l=!1,o=i.ip4_expression.test(this._parts.hostname),n=i.ip6_expression.test(this._parts.hostname),s=(a=!(e=o||n))&&r&&r.has(this._parts.hostname),c=a&&i.idn_expression.test(this._parts.hostname),u=a&&i.punycode_expression.test(this._parts.hostname)),t.toLowerCase()){case"relative":return l;case"absolute":return!l;case"domain":case"name":return a;case"sld":return s;case"ip":return e;case"ip4":case"ipv4":case"inet4":return o;case"ip6":case"ipv6":case"inet6":return n;case"idn":return c;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return u}return null};var P=s.protocol,k=s.port,C=s.hostname;s.protocol=function(t,e){if(t&&!(t=t.replace(/:(\/\/)?$/,"")).match(i.protocol_expression))throw new TypeError('Protocol "'+t+"\" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]");return P.call(this,t,e)},s.scheme=s.protocol,s.port=function(t,e){return this._parts.urn?void 0===t?"":this:(void 0!==t&&(0===t&&(t=null),t&&(":"===(t+="").charAt(0)&&(t=t.substring(1)),i.ensureValidPort(t))),k.call(this,t,e))},s.hostname=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0!==t){var r={preventInvalidHostname:this._parts.preventInvalidHostname};if("/"!==i.parseHost(t,r))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');t=r.hostname,this._parts.preventInvalidHostname&&i.ensureValidHostname(t,this._parts.protocol)}return C.call(this,t,e)},s.origin=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){var r=this.protocol();return this.authority()?(r?r+"://":"")+this.authority():""}var o=i(t);return this.protocol(o.protocol()).authority(o.authority()).build(!e),this},s.host=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t)return this._parts.hostname?i.buildHost(this._parts):"";if("/"!==i.parseHost(t,this._parts))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!e),this},s.authority=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t)return this._parts.hostname?i.buildAuthority(this._parts):"";if("/"!==i.parseAuthority(t,this._parts))throw new TypeError('Hostname "'+t+'" contains characters other than [A-Z0-9.-]');return this.build(!e),this},s.userinfo=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){var r=i.buildUserinfo(this._parts);return r?r.substring(0,r.length-1):r}return"@"!==t[t.length-1]&&(t+="@"),i.parseUserinfo(t,this._parts),this.build(!e),this},s.resource=function(t,e){var r;return void 0===t?this.path()+this.search()+this.hash():(r=i.parse(t),this._parts.path=r.path,this._parts.query=r.query,this._parts.fragment=r.fragment,this.build(!e),this)},s.subdomain=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,r)||""}var o=this._parts.hostname.length-this.domain().length,n=this._parts.hostname.substring(0,o),a=new RegExp("^"+u(n));if(t&&"."!==t.charAt(t.length-1)&&(t+="."),-1!==t.indexOf(":"))throw new TypeError("Domains cannot contain colons");return t&&i.ensureValidHostname(t,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(a,t),this.build(!e),this},s.domain=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("boolean"==typeof t&&(e=t,t=void 0),void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var r=this._parts.hostname.match(/\./g);if(r&&r.length<2)return this._parts.hostname;var o=this._parts.hostname.length-this.tld(e).length-1;return o=this._parts.hostname.lastIndexOf(".",o-1)+1,this._parts.hostname.substring(o)||""}if(!t)throw new TypeError("cannot set domain empty");if(-1!==t.indexOf(":"))throw new TypeError("Domains cannot contain colons");if(i.ensureValidHostname(t,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=t;else{var n=new RegExp(u(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(n,t)}return this.build(!e),this},s.tld=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("boolean"==typeof t&&(e=t,t=void 0),void 0===t){if(!this._parts.hostname||this.is("IP"))return"";var o=this._parts.hostname.lastIndexOf("."),n=this._parts.hostname.substring(o+1);return!0!==e&&r&&r.list[n.toLowerCase()]&&r.get(this._parts.hostname)||n}var i;if(!t)throw new TypeError("cannot set TLD empty");if(t.match(/[^a-zA-Z0-9-]/)){if(!r||!r.is(t))throw new TypeError('TLD "'+t+'" contains characters other than [A-Z0-9]');i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,t)}else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");i=new RegExp(u(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(i,t)}return this.build(!e),this},s.directory=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t||!0===t){if(!this._parts.path&&!this._parts.hostname)return"";if("/"===this._parts.path)return"/";var r=this._parts.path.length-this.filename().length-1,o=this._parts.path.substring(0,r)||(this._parts.hostname?"/":"");return t?i.decodePath(o):o}var n=this._parts.path.length-this.filename().length,a=this._parts.path.substring(0,n),s=new RegExp("^"+u(a));return this.is("relative")||(t||(t="/"),"/"!==t.charAt(0)&&(t="/"+t)),t&&"/"!==t.charAt(t.length-1)&&(t+="/"),t=i.recodePath(t),this._parts.path=this._parts.path.replace(s,t),this.build(!e),this},s.filename=function(t,e){if(this._parts.urn)return void 0===t?"":this;if("string"!=typeof t){if(!this._parts.path||"/"===this._parts.path)return"";var r=this._parts.path.lastIndexOf("/"),o=this._parts.path.substring(r+1);return t?i.decodePathSegment(o):o}var n=!1;"/"===t.charAt(0)&&(t=t.substring(1)),t.match(/\.?\//)&&(n=!0);var a=new RegExp(u(this.filename())+"$");return t=i.recodePath(t),this._parts.path=this._parts.path.replace(a,t),n?this.normalizePath(e):this.build(!e),this},s.suffix=function(t,e){if(this._parts.urn)return void 0===t?"":this;if(void 0===t||!0===t){if(!this._parts.path||"/"===this._parts.path)return"";var r,o,n=this.filename(),a=n.lastIndexOf(".");return-1===a?"":(r=n.substring(a+1),o=/^[a-z0-9%]+$/i.test(r)?r:"",t?i.decodePathSegment(o):o)}"."===t.charAt(0)&&(t=t.substring(1));var s,c=this.suffix();if(c)s=t?new RegExp(u(c)+"$"):new RegExp(u("."+c)+"$");else{if(!t)return this;this._parts.path+="."+i.recodePath(t)}return s&&(t=i.recodePath(t),this._parts.path=this._parts.path.replace(s,t)),this.build(!e),this},s.segment=function(t,e,r){var o=this._parts.urn?":":"/",n=this.path(),i="/"===n.substring(0,1),a=n.split(o);if(void 0!==t&&"number"!=typeof t&&(r=e,e=t,t=void 0),void 0!==t&&"number"!=typeof t)throw new Error('Bad segment "'+t+'", must be 0-based integer');if(i&&a.shift(),t<0&&(t=Math.max(a.length+t,0)),void 0===e)return void 0===t?a:a[t];if(null===t||void 0===a[t])if(p(e)){a=[];for(var s=0,c=e.length;s<c;s++)(e[s].length||a.length&&a[a.length-1].length)&&(a.length&&!a[a.length-1].length&&a.pop(),a.push(m(e[s])))}else(e||"string"==typeof e)&&(e=m(e),""===a[a.length-1]?a[a.length-1]=e:a.push(e));else e?a[t]=m(e):a.splice(t,1);return i&&a.unshift(""),this.path(a.join(o),r)},s.segmentCoded=function(t,e,r){var o,n,a;if("number"!=typeof t&&(r=e,e=t,t=void 0),void 0===e){if(p(o=this.segment(t,e,r)))for(n=0,a=o.length;n<a;n++)o[n]=i.decode(o[n]);else o=void 0!==o?i.decode(o):void 0;return o}if(p(e))for(n=0,a=e.length;n<a;n++)e[n]=i.encode(e[n]);else e="string"==typeof e||e instanceof String?i.encode(e):e;return this.segment(t,e,r)};var O=s.query;return s.query=function(t,e){if(!0===t)return i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("function"==typeof t){var r=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace),o=t.call(this,r);return this._parts.query=i.buildQuery(o||r,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!e),this}return void 0!==t&&"string"!=typeof t?(this._parts.query=i.buildQuery(t,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!e),this):O.call(this,t,e)},s.setQuery=function(t,e,r){var o=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if("string"==typeof t||t instanceof String)o[t]=void 0!==e?e:null;else{if("object"!==a(t))throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");for(var n in t)c.call(t,n)&&(o[n]=t[n])}return this._parts.query=i.buildQuery(o,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},s.addQuery=function(t,e,r){var o=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.addQuery(o,t,void 0===e?null:e),this._parts.query=i.buildQuery(o,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},s.removeQuery=function(t,e,r){var o=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.removeQuery(o,t,e),this._parts.query=i.buildQuery(o,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),"string"!=typeof t&&(r=e),this.build(!r),this},s.hasQuery=function(t,e,r){var o=i.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return i.hasQuery(o,t,e,r)},s.setSearch=s.setQuery,s.addSearch=s.addQuery,s.removeSearch=s.removeQuery,s.hasSearch=s.hasQuery,s.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},s.normalizeProtocol=function(t){return"string"==typeof this._parts.protocol&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!t)),this},s.normalizeHostname=function(r){return this._parts.hostname&&(this.is("IDN")&&t?this._parts.hostname=t.toASCII(this._parts.hostname):this.is("IPv6")&&e&&(this._parts.hostname=e.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!r)),this},s.normalizePort=function(t){return"string"==typeof this._parts.protocol&&this._parts.port===i.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!t)),this},s.normalizePath=function(t){var e,r=this._parts.path;if(!r)return this;if(this._parts.urn)return this._parts.path=i.recodeUrnPath(this._parts.path),this.build(!t),this;if("/"===this._parts.path)return this;var o,n,a="";for("/"!==(r=i.recodePath(r)).charAt(0)&&(e=!0,r="/"+r),"/.."!==r.slice(-3)&&"/."!==r.slice(-2)||(r+="/"),r=r.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),e&&(a=r.substring(1).match(/^(\.\.\/)+/)||"")&&(a=a[0]);-1!==(o=r.search(/\/\.\.(\/|$)/));)0!==o?(-1===(n=r.substring(0,o).lastIndexOf("/"))&&(n=o),r=r.substring(0,n)+r.substring(o+3)):r=r.substring(3);return e&&this.is("relative")&&(r=a+r.substring(1)),this._parts.path=r,this.build(!t),this},s.normalizePathname=s.normalizePath,s.normalizeQuery=function(t){return"string"==typeof this._parts.query&&(this._parts.query.length?this.query(i.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!t)),this},s.normalizeFragment=function(t){return this._parts.fragment||(this._parts.fragment=null,this.build(!t)),this},s.normalizeSearch=s.normalizeQuery,s.normalizeHash=s.normalizeFragment,s.iso8859=function(){var t=i.encode,e=i.decode;i.encode=escape,i.decode=decodeURIComponent;try{this.normalize()}finally{i.encode=t,i.decode=e}return this},s.unicode=function(){var t=i.encode,e=i.decode;i.encode=g,i.decode=unescape;try{this.normalize()}finally{i.encode=t,i.decode=e}return this},s.readable=function(){var e=this.clone();e.username("").password("").normalize();var r="";if(e._parts.protocol&&(r+=e._parts.protocol+"://"),e._parts.hostname&&(e.is("punycode")&&t?(r+=t.toUnicode(e._parts.hostname),e._parts.port&&(r+=":"+e._parts.port)):r+=e.host()),e._parts.hostname&&e._parts.path&&"/"!==e._parts.path.charAt(0)&&(r+="/"),r+=e.path(!0),e._parts.query){for(var o="",n=0,a=e._parts.query.split("&"),s=a.length;n<s;n++){var c=(a[n]||"").split("=");o+="&"+i.decodeQuery(c[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),void 0!==c[1]&&(o+="="+i.decodeQuery(c[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}r+="?"+o.substring(1)}return r+i.decodeQuery(e.hash(),!0)},s.absoluteTo=function(t){var e,r,o,n=this.clone(),a=["protocol","username","password","hostname","port"];if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t instanceof i||(t=new i(t)),n._parts.protocol)return n;if(n._parts.protocol=t._parts.protocol,this._parts.hostname)return n;for(r=0;o=a[r];r++)n._parts[o]=t._parts[o];return n._parts.path?(".."===n._parts.path.substring(-2)&&(n._parts.path+="/"),"/"!==n.path().charAt(0)&&(e=(e=t.directory())||(0===t.path().indexOf("/")?"/":""),n._parts.path=(e?e+"/":"")+n._parts.path,n.normalizePath())):(n._parts.path=t._parts.path,n._parts.query||(n._parts.query=t._parts.query)),n.build(),n},s.relativeTo=function(t){var e,r,o,n,a,s=this.clone().normalize();if(s._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(t=new i(t).normalize(),e=s._parts,r=t._parts,n=s.path(),a=t.path(),"/"!==n.charAt(0))throw new Error("URI is already relative");if("/"!==a.charAt(0))throw new Error("Cannot calculate a URI relative to another relative URI");if(e.protocol===r.protocol&&(e.protocol=null),e.username!==r.username||e.password!==r.password)return s.build();if(null!==e.protocol||null!==e.username||null!==e.password)return s.build();if(e.hostname!==r.hostname||e.port!==r.port)return s.build();if(e.hostname=null,e.port=null,n===a)return e.path="",s.build();if(!(o=i.commonPath(n,a)))return s.build();var c=r.path.substring(o.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return e.path=c+e.path.substring(o.length)||"./",s.build()},s.equals=function(t){var e,r,o,n,a,s=this.clone(),u=new i(t),l={};if(s.normalize(),u.normalize(),s.toString()===u.toString())return!0;if(o=s.query(),n=u.query(),s.query(""),u.query(""),s.toString()!==u.toString())return!1;if(o.length!==n.length)return!1;for(a in e=i.parseQuery(o,this._parts.escapeQuerySpace),r=i.parseQuery(n,this._parts.escapeQuerySpace),e)if(c.call(e,a)){if(p(e[a])){if(!h(e[a],r[a]))return!1}else if(e[a]!==r[a])return!1;l[a]=!0}for(a in r)if(c.call(r,a)&&!l[a])return!1;return!0},s.preventInvalidHostname=function(t){return this._parts.preventInvalidHostname=!!t,this},s.duplicateQueryParameters=function(t){return this._parts.duplicateQueryParameters=!!t,this},s.escapeQuerySpace=function(t){return this._parts.escapeQuerySpace=!!t,this},i}))},9538:function(t,e,r){var o;function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}t=r.nmd(t),function(i){var a="object"==n(e)&&e&&!e.nodeType&&e,s="object"==n(t)&&t&&!t.nodeType&&t,c="object"==(void 0===r.g?"undefined":n(r.g))&&r.g;c.global!==c&&c.window!==c&&c.self!==c||(i=c);var u,l,p=2147483647,f=36,d=26,h=38,m=700,y=/^xn--/,g=/[^\x20-\x7E]/,v=/[\x2E\u3002\uFF0E\uFF61]/g,b={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},A=f-1,x=Math.floor,w=String.fromCharCode;function S(t){throw new RangeError(b[t])}function P(t,e){for(var r=t.length,o=[];r--;)o[r]=e(t[r]);return o}function k(t,e){var r=t.split("@"),o="";return r.length>1&&(o=r[0]+"@",t=r[1]),o+P((t=t.replace(v,".")).split("."),e).join(".")}function C(t){for(var e,r,o=[],n=0,i=t.length;n<i;)(e=t.charCodeAt(n++))>=55296&&e<=56319&&n<i?56320==(64512&(r=t.charCodeAt(n++)))?o.push(((1023&e)<<10)+(1023&r)+65536):(o.push(e),n--):o.push(e);return o}function O(t){return P(t,(function(t){var e="";return t>65535&&(e+=w((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+w(t)})).join("")}function E(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function T(t,e,r){var o=0;for(t=r?x(t/m):t>>1,t+=x(t/e);t>A*d>>1;o+=f)t=x(t/A);return x(o+(A+1)*t/(t+h))}function B(t){var e,r,o,n,i,a,s,c,u,l,h,m=[],y=t.length,g=0,v=128,b=72;for((r=t.lastIndexOf("-"))<0&&(r=0),o=0;o<r;++o)t.charCodeAt(o)>=128&&S("not-basic"),m.push(t.charCodeAt(o));for(n=r>0?r+1:0;n<y;){for(i=g,a=1,s=f;n>=y&&S("invalid-input"),((c=(h=t.charCodeAt(n++))-48<10?h-22:h-65<26?h-65:h-97<26?h-97:f)>=f||c>x((p-g)/a))&&S("overflow"),g+=c*a,!(c<(u=s<=b?1:s>=b+d?d:s-b));s+=f)a>x(p/(l=f-u))&&S("overflow"),a*=l;b=T(g-i,e=m.length+1,0==i),x(g/e)>p-v&&S("overflow"),v+=x(g/e),g%=e,m.splice(g++,0,v)}return O(m)}function M(t){var e,r,o,n,i,a,s,c,u,l,h,m,y,g,v,b=[];for(m=(t=C(t)).length,e=128,r=0,i=72,a=0;a<m;++a)(h=t[a])<128&&b.push(w(h));for(o=n=b.length,n&&b.push("-");o<m;){for(s=p,a=0;a<m;++a)(h=t[a])>=e&&h<s&&(s=h);for(s-e>x((p-r)/(y=o+1))&&S("overflow"),r+=(s-e)*y,e=s,a=0;a<m;++a)if((h=t[a])<e&&++r>p&&S("overflow"),h==e){for(c=r,u=f;!(c<(l=u<=i?1:u>=i+d?d:u-i));u+=f)v=c-l,g=f-l,b.push(w(E(l+v%g,0))),c=x(v/g);b.push(w(E(c,0))),i=T(r,y,o==n),r=0,++o}++r,++e}return b.join("")}if(u={version:"1.3.2",ucs2:{decode:C,encode:O},decode:B,encode:M,toASCII:function(t){return k(t,(function(t){return g.test(t)?"xn--"+M(t):t}))},toUnicode:function(t){return k(t,(function(t){return y.test(t)?B(t.slice(4).toLowerCase()):t}))}},"object"==n(r.amdO)&&r.amdO)void 0===(o=function(){return u}.call(e,r,e,t))||(t.exports=o);else if(a&&s)if(t.exports==a)s.exports=u;else for(l in u)u.hasOwnProperty(l)&&(a[l]=u[l]);else i.punycode=u}(this)},3784:function(t,e){e.AclPrivate="private",e.AclPublicRead="public-read",e.AclPublicReadWrite="public-read-write",e.AclPublicReadDelivered="public-read-delivered",e.AclPublicReadWriteDelivered="public-read-write-delivered",e.AclAuthenticatedRead="authenticated-read",e.AclBucketOwnerRead="bucket-owner-read",e.AclBucketOwnerFullControl="bucket-owner-full-control",e.AclLogDeliveryWrite="log-delivery-write",e.StorageClassStandard="STANDARD",e.StorageClassWarm="WARM",e.StorageClassCold="COLD",e.StorageClassDeepArchive="DEEP_ARCHIVE",e.StorageClassIntelligentTiering="INTELLIGENT_TIERING",e.PermissionRead="READ",e.PermissionWrite="WRITE",e.PermissionReadAcp="READ_ACP",e.PermissionWriteAcp="WRITE_ACP",e.PermissionFullControl="FULL_CONTROL",e.GroupAllUsers="AllUsers",e.GroupAuthenticatedUsers="AuthenticatedUsers",e.GroupLogDelivery="LogDelivery",e.RestoreTierExpedited="Expedited",e.RestoreTierStandard="Standard",e.RestoreTierBulk="Bulk",e.GranteeGroup="Group",e.GranteeUser="CanonicalUser",e.CopyMetadata="COPY",e.ReplaceMetadata="REPLACE",e.EventObjectCreatedAll="ObjectCreated:*",e.EventObjectCreatedPut="ObjectCreated:Put",e.EventObjectCreatedPost="ObjectCreated:Post",e.EventObjectCreatedCopy="ObjectCreated:Copy",e.EventObjectCreatedCompleteMultipartUpload="ObjectCreated:CompleteMultipartUpload",e.EventObjectRemovedAll="ObjectRemoved:*",e.EventObjectRemovedDelete="ObjectRemoved:Delete",e.EventObjectRemovedDeleteMarkerCreated="ObjectRemoved:DeleteMarkerCreated",e.ContentMD5="Content-MD5",e.ContentSHA256="Content-SHA256"},477:function(){}},o={};function n(t){var e=o[t];if(void 0!==e)return e.exports;var i=o[t]={id:t,loaded:!1,exports:{}};return r[t].call(i.exports,i,i.exports,n),i.loaded=!0,i.exports}n.amdO={},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},e=Object.getPrototypeOf?function(t){return Object.getPrototypeOf(t)}:function(t){return t.__proto__},n.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"==typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"==typeof r.then)return r}var i=Object.create(null);n.r(i);var a={};t=t||[null,e({}),e([]),e(e)];for(var s=2&o&&r;"object"==typeof s&&!~t.indexOf(s);s=e(s))Object.getOwnPropertyNames(s).forEach((function(t){a[t]=function(){return r[t]}}));return a.default=function(){return r},n.d(i,a),i},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t};var i={};return function(){"use strict";n(5250);var t,e=(t=n(8910))&&t.__esModule?t:{default:t};e.default._babelPolyfill&&"undefined"!=typeof console&&console.warn&&console.warn("@babel/polyfill is loaded more than once on this page. This is probably not desirable/intended and may have consequences if different versions of the polyfills are applied sequentially. If you do need to load the polyfill more than once, use @babel/polyfill/noConflict instead to bypass the warning."),e.default._babelPolyfill=!0}(),function(){"use strict";n.d(i,{default:function(){return qo}});var t=n(107),e=n.n(t),r=n(6783),o=n.n(r);function a(t,e,r){return e=c(e),function(t,e){if(e&&("object"===p(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,s()?Reflect.construct(e,r||[],c(t).constructor):e.apply(t,r))}function s(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(s=function(){return!!t})()}function c(t){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},c(t)}function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&l(t,e)}function l(t,e){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},l(t,e)}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function d(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,m(o.key),o)}}function h(t,e,r){return e&&d(t.prototype,e),r&&d(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function m(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!=p(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==p(e)?e:e+""}var y="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",g="ARRAYBUFFER not supported by this environment",v="UINT8ARRAY not supported by this environment";function b(t,e,r,o){var n,i,a,s=e||[0],c=(r=r||0)>>>3,u=-1===o?3:0;for(n=0;n<t.length;n+=1)i=(a=n+c)>>>2,s.length<=i&&s.push(0),s[i]|=t[n]<<8*(u+o*(a%4));return{value:s,binLen:8*t.length+r}}function A(t,e,r){switch(e){case"UTF8":case"UTF16BE":case"UTF16LE":break;default:throw new Error("encoding must be UTF8, UTF16BE, or UTF16LE")}switch(t){case"HEX":return function(t,e,o){return function(t,e,r,o){var n,i,a,s;if(0!=t.length%2)throw new Error("String of HEX type must be in byte increments");var c=e||[0],u=(r=r||0)>>>3,l=-1===o?3:0;for(n=0;n<t.length;n+=2){if(i=parseInt(t.substr(n,2),16),isNaN(i))throw new Error("String of HEX type contains invalid characters");for(a=(s=(n>>>1)+u)>>>2;c.length<=a;)c.push(0);c[a]|=i<<8*(l+o*(s%4))}return{value:c,binLen:4*t.length+r}}(t,e,o,r)};case"TEXT":return function(t,o,n){return function(t,e,r,o,n){var i,a,s,c,u,l,p,f,d=0,h=r||[0],m=(o=o||0)>>>3;if("UTF8"===e)for(p=-1===n?3:0,s=0;s<t.length;s+=1)for(a=[],128>(i=t.charCodeAt(s))?a.push(i):2048>i?(a.push(192|i>>>6),a.push(128|63&i)):55296>i||57344<=i?a.push(224|i>>>12,128|i>>>6&63,128|63&i):(s+=1,i=65536+((1023&i)<<10|1023&t.charCodeAt(s)),a.push(240|i>>>18,128|i>>>12&63,128|i>>>6&63,128|63&i)),c=0;c<a.length;c+=1){for(u=(l=d+m)>>>2;h.length<=u;)h.push(0);h[u]|=a[c]<<8*(p+n*(l%4)),d+=1}else for(p=-1===n?2:0,f="UTF16LE"===e&&1!==n||"UTF16LE"!==e&&1===n,s=0;s<t.length;s+=1){for(i=t.charCodeAt(s),!0===f&&(i=(c=255&i)<<8|i>>>8),u=(l=d+m)>>>2;h.length<=u;)h.push(0);h[u]|=i<<8*(p+n*(l%4)),d+=2}return{value:h,binLen:8*d+o}}(t,e,o,n,r)};case"B64":return function(t,e,o){return function(t,e,r,o){var n,i,a,s,c,u,l=0,p=e||[0],f=(r=r||0)>>>3,d=-1===o?3:0,h=t.indexOf("=");if(-1===t.search(/^[a-zA-Z0-9=+/]+$/))throw new Error("Invalid character in base-64 string");if(t=t.replace(/=/g,""),-1!==h&&h<t.length)throw new Error("Invalid '=' found in base-64 string");for(n=0;n<t.length;n+=4){for(s=t.substr(n,4),a=0,i=0;i<s.length;i+=1)a|=y.indexOf(s.charAt(i))<<18-6*i;for(i=0;i<s.length-1;i+=1){for(c=(u=l+f)>>>2;p.length<=c;)p.push(0);p[c]|=(a>>>16-8*i&255)<<8*(d+o*(u%4)),l+=1}}return{value:p,binLen:8*l+r}}(t,e,o,r)};case"BYTES":return function(t,e,o){return function(t,e,r,o){var n,i,a,s,c=e||[0],u=(r=r||0)>>>3,l=-1===o?3:0;for(i=0;i<t.length;i+=1)n=t.charCodeAt(i),a=(s=i+u)>>>2,c.length<=a&&c.push(0),c[a]|=n<<8*(l+o*(s%4));return{value:c,binLen:8*t.length+r}}(t,e,o,r)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(t){throw new Error(g)}return function(t,e,o){return function(t,e,r,o){return b(new Uint8Array(t),e,r,o)}(t,e,o,r)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(t){throw new Error(v)}return function(t,e,o){return b(t,e,o,r)};default:throw new Error("format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}function x(t,e,r,o){switch(t){case"HEX":return function(t){return function(t,e,r,o){var n,i,a="0123456789abcdef",s="",c=e/8,u=-1===r?3:0;for(n=0;n<c;n+=1)i=t[n>>>2]>>>8*(u+r*(n%4)),s+=a.charAt(i>>>4&15)+a.charAt(15&i);return o.outputUpper?s.toUpperCase():s}(t,e,r,o)};case"B64":return function(t){return function(t,e,r,o){var n,i,a,s,c,u="",l=e/8,p=-1===r?3:0;for(n=0;n<l;n+=3)for(s=n+1<l?t[n+1>>>2]:0,c=n+2<l?t[n+2>>>2]:0,a=(t[n>>>2]>>>8*(p+r*(n%4))&255)<<16|(s>>>8*(p+r*((n+1)%4))&255)<<8|c>>>8*(p+r*((n+2)%4))&255,i=0;i<4;i+=1)u+=8*n+6*i<=e?y.charAt(a>>>6*(3-i)&63):o.b64Pad;return u}(t,e,r,o)};case"BYTES":return function(t){return function(t,e,r){var o,n,i="",a=e/8,s=-1===r?3:0;for(o=0;o<a;o+=1)n=t[o>>>2]>>>8*(s+r*(o%4))&255,i+=String.fromCharCode(n);return i}(t,e,r)};case"ARRAYBUFFER":try{new ArrayBuffer(0)}catch(t){throw new Error(g)}return function(t){return function(t,e,r){var o,n=e/8,i=new ArrayBuffer(n),a=new Uint8Array(i),s=-1===r?3:0;for(o=0;o<n;o+=1)a[o]=t[o>>>2]>>>8*(s+r*(o%4))&255;return i}(t,e,r)};case"UINT8ARRAY":try{new Uint8Array(0)}catch(t){throw new Error(v)}return function(t){return function(t,e,r){var o,n=e/8,i=-1===r?3:0,a=new Uint8Array(n);for(o=0;o<n;o+=1)a[o]=t[o>>>2]>>>8*(i+r*(o%4))&255;return a}(t,e,r)};default:throw new Error("format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY")}}var w=4294967296,S=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],P=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],k=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],C="Chosen SHA variant is not supported",O="Cannot set numRounds with MAC";function E(t,e){var r,o,n=t.binLen>>>3,i=e.binLen>>>3,a=n<<3,s=4-n<<3;if(n%4!=0){for(r=0;r<i;r+=4)o=n+r>>>2,t.value[o]|=e.value[r>>>2]<<a,t.value.push(0),t.value[o+1]|=e.value[r>>>2]>>>s;return(t.value.length<<2)-4>=i+n&&t.value.pop(),{value:t.value,binLen:t.binLen+e.binLen}}return{value:t.value.concat(e.value),binLen:t.binLen+e.binLen}}function T(t){var e={outputUpper:!1,b64Pad:"=",outputLen:-1},r=t||{},o="Output length must be a multiple of 8";if(e.outputUpper=r.outputUpper||!1,r.b64Pad&&(e.b64Pad=r.b64Pad),r.outputLen){if(r.outputLen%8!=0)throw new Error(o);e.outputLen=r.outputLen}else if(r.shakeLen){if(r.shakeLen%8!=0)throw new Error(o);e.outputLen=r.shakeLen}if("boolean"!=typeof e.outputUpper)throw new Error("Invalid outputUpper formatting option");if("string"!=typeof e.b64Pad)throw new Error("Invalid b64Pad formatting option");return e}function B(t,e,r,o){var n=t+" must include a value and format";if(!e){if(!o)throw new Error(n);return o}if(void 0===e.value||!e.format)throw new Error(n);return A(e.format,e.encoding||"UTF8",r)(e.value)}var M=function(){return h((function t(e,r,o){f(this,t);var n=o||{};if(this.t=r,this.i=n.encoding||"UTF8",this.numRounds=n.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error("numRounds must a integer >= 1");this.o=e,this.h=[],this.u=0,this.l=!1,this.A=0,this.H=!1,this.S=[],this.p=[]}),[{key:"update",value:function(t){var e,r=0,o=this.m>>>5,n=this.C(t,this.h,this.u),i=n.binLen,a=n.value,s=i>>>5;for(e=0;e<s;e+=o)r+this.m<=i&&(this.U=this.v(a.slice(e,e+o),this.U),r+=this.m);return this.A+=r,this.h=a.slice(r>>>5),this.u=i%this.m,this.l=!0,this}},{key:"getHash",value:function(t,e){var r,o,n=this.R,i=T(e);if(this.K){if(-1===i.outputLen)throw new Error("Output length must be specified in options");n=i.outputLen}var a=x(t,n,this.T,i);if(this.H&&this.g)return a(this.g(i));for(o=this.F(this.h.slice(),this.u,this.A,this.L(this.U),n),r=1;r<this.numRounds;r+=1)this.K&&n%32!=0&&(o[o.length-1]&=16777215>>>24-n%32),o=this.F(o,n,0,this.B(this.o),n);return a(o)}},{key:"setHMACKey",value:function(t,e,r){if(!this.M)throw new Error("Variant does not support HMAC");if(this.l)throw new Error("Cannot set MAC key after calling update");var o=A(e,(r||{}).encoding||"UTF8",this.T);this.k(o(t))}},{key:"k",value:function(t){var e,r=this.m>>>3,o=r/4-1;if(1!==this.numRounds)throw new Error(O);if(this.H)throw new Error("MAC key already set");for(r<t.binLen/8&&(t.value=this.F(t.value,t.binLen,0,this.B(this.o),this.R));t.value.length<=o;)t.value.push(0);for(e=0;e<=o;e+=1)this.S[e]=909522486^t.value[e],this.p[e]=1549556828^t.value[e];this.U=this.v(this.S,this.U),this.A=this.m,this.H=!0}},{key:"getHMAC",value:function(t,e){var r=T(e);return x(t,this.R,this.T,r)(this.Y())}},{key:"Y",value:function(){var t;if(!this.H)throw new Error("Cannot call getHMAC without first setting MAC key");var e=this.F(this.h.slice(),this.u,this.A,this.L(this.U),this.R);return t=this.v(this.p,this.B(this.o)),this.F(e,this.R,this.m,t,this.R)}}])}();function R(t,e){return t<<e|t>>>32-e}function _(t,e){return t>>>e|t<<32-e}function I(t,e){return t>>>e}function j(t,e,r){return t^e^r}function D(t,e,r){return t&e^~t&r}function L(t,e,r){return t&e^t&r^e&r}function q(t){return _(t,2)^_(t,13)^_(t,22)}function F(t,e){var r=(65535&t)+(65535&e);return(65535&(t>>>16)+(e>>>16)+(r>>>16))<<16|65535&r}function N(t,e,r,o){var n=(65535&t)+(65535&e)+(65535&r)+(65535&o);return(65535&(t>>>16)+(e>>>16)+(r>>>16)+(o>>>16)+(n>>>16))<<16|65535&n}function U(t,e,r,o,n){var i=(65535&t)+(65535&e)+(65535&r)+(65535&o)+(65535&n);return(65535&(t>>>16)+(e>>>16)+(r>>>16)+(o>>>16)+(n>>>16)+(i>>>16))<<16|65535&i}function G(t){return _(t,7)^_(t,18)^I(t,3)}function H(t){return _(t,6)^_(t,11)^_(t,25)}function z(t){return[1732584193,4023233417,2562383102,271733878,3285377520]}function K(t,e){var r,o,n,i,a,s,c,u=[];for(r=e[0],o=e[1],n=e[2],i=e[3],a=e[4],c=0;c<80;c+=1)u[c]=c<16?t[c]:R(u[c-3]^u[c-8]^u[c-14]^u[c-16],1),s=c<20?U(R(r,5),D(o,n,i),a,1518500249,u[c]):c<40?U(R(r,5),j(o,n,i),a,1859775393,u[c]):c<60?U(R(r,5),L(o,n,i),a,2400959708,u[c]):U(R(r,5),j(o,n,i),a,3395469782,u[c]),a=i,i=n,n=R(o,30),o=r,r=s;return e[0]=F(r,e[0]),e[1]=F(o,e[1]),e[2]=F(n,e[2]),e[3]=F(i,e[3]),e[4]=F(a,e[4]),e}function W(t,e,r,o){for(var n,i=15+(e+65>>>9<<4),a=e+r;t.length<=i;)t.push(0);for(t[e>>>5]|=128<<24-e%32,t[i]=4294967295&a,t[i-1]=a/w|0,n=0;n<t.length;n+=16)o=K(t.slice(n,n+16),o);return o}var V=function(t){function e(t,r,o){var n;if(f(this,e),"SHA-1"!==t)throw new Error(C);var i=o||{};return(n=a(this,e,[t,r,o])).M=!0,n.g=n.Y,n.T=-1,n.C=A(n.t,n.i,n.T),n.v=K,n.L=function(t){return t.slice()},n.B=z,n.F=W,n.U=[1732584193,4023233417,2562383102,271733878,3285377520],n.m=512,n.R=160,n.K=!1,i.hmacKey&&n.k(B("hmacKey",i.hmacKey,n.T)),n}return u(e,t),h(e)}(M);function Q(t){return"SHA-224"==t?P.slice():k.slice()}function X(t,e){var r,o,n,i,a,s,c,u,l,p,f,d,h=[];for(r=e[0],o=e[1],n=e[2],i=e[3],a=e[4],s=e[5],c=e[6],u=e[7],f=0;f<64;f+=1)h[f]=f<16?t[f]:N(_(d=h[f-2],17)^_(d,19)^I(d,10),h[f-7],G(h[f-15]),h[f-16]),l=U(u,H(a),D(a,s,c),S[f],h[f]),p=F(q(r),L(r,o,n)),u=c,c=s,s=a,a=F(i,l),i=n,n=o,o=r,r=F(l,p);return e[0]=F(r,e[0]),e[1]=F(o,e[1]),e[2]=F(n,e[2]),e[3]=F(i,e[3]),e[4]=F(a,e[4]),e[5]=F(s,e[5]),e[6]=F(c,e[6]),e[7]=F(u,e[7]),e}var $=function(t){function e(t,r,o){var n;if(f(this,e),"SHA-224"!==t&&"SHA-256"!==t)throw new Error(C);var i=o||{};return(n=a(this,e,[t,r,o])).g=n.Y,n.M=!0,n.T=-1,n.C=A(n.t,n.i,n.T),n.v=X,n.L=function(t){return t.slice()},n.B=Q,n.F=function(e,r,o,n){return function(t,e,r,o,n){for(var i,a=15+(e+65>>>9<<4),s=e+r;t.length<=a;)t.push(0);for(t[e>>>5]|=128<<24-e%32,t[a]=4294967295&s,t[a-1]=s/w|0,i=0;i<t.length;i+=16)o=X(t.slice(i,i+16),o);return"SHA-224"===n?[o[0],o[1],o[2],o[3],o[4],o[5],o[6]]:o}(e,r,o,n,t)},n.U=Q(t),n.m=512,n.R="SHA-224"===t?224:256,n.K=!1,i.hmacKey&&n.k(B("hmacKey",i.hmacKey,n.T)),n}return u(e,t),h(e)}(M),Y=h((function t(e,r){f(this,t),this.N=e,this.I=r}));function Z(t,e){var r;return e>32?(r=64-e,new Y(t.I<<e|t.N>>>r,t.N<<e|t.I>>>r)):0!==e?(r=32-e,new Y(t.N<<e|t.I>>>r,t.I<<e|t.N>>>r)):t}function J(t,e){var r;return e<32?(r=32-e,new Y(t.N>>>e|t.I<<r,t.I>>>e|t.N<<r)):(r=64-e,new Y(t.I>>>e|t.N<<r,t.N>>>e|t.I<<r))}function tt(t,e){return new Y(t.N>>>e,t.I>>>e|t.N<<32-e)}function et(t,e,r){return new Y(t.N&e.N^t.N&r.N^e.N&r.N,t.I&e.I^t.I&r.I^e.I&r.I)}function rt(t){var e=J(t,28),r=J(t,34),o=J(t,39);return new Y(e.N^r.N^o.N,e.I^r.I^o.I)}function ot(t,e){var r,o;r=(65535&t.I)+(65535&e.I);var n=(65535&(o=(t.I>>>16)+(e.I>>>16)+(r>>>16)))<<16|65535&r;return r=(65535&t.N)+(65535&e.N)+(o>>>16),o=(t.N>>>16)+(e.N>>>16)+(r>>>16),new Y((65535&o)<<16|65535&r,n)}function nt(t,e,r,o){var n,i;n=(65535&t.I)+(65535&e.I)+(65535&r.I)+(65535&o.I);var a=(65535&(i=(t.I>>>16)+(e.I>>>16)+(r.I>>>16)+(o.I>>>16)+(n>>>16)))<<16|65535&n;return n=(65535&t.N)+(65535&e.N)+(65535&r.N)+(65535&o.N)+(i>>>16),i=(t.N>>>16)+(e.N>>>16)+(r.N>>>16)+(o.N>>>16)+(n>>>16),new Y((65535&i)<<16|65535&n,a)}function it(t,e,r,o,n){var i,a;i=(65535&t.I)+(65535&e.I)+(65535&r.I)+(65535&o.I)+(65535&n.I);var s=(65535&(a=(t.I>>>16)+(e.I>>>16)+(r.I>>>16)+(o.I>>>16)+(n.I>>>16)+(i>>>16)))<<16|65535&i;return i=(65535&t.N)+(65535&e.N)+(65535&r.N)+(65535&o.N)+(65535&n.N)+(a>>>16),a=(t.N>>>16)+(e.N>>>16)+(r.N>>>16)+(o.N>>>16)+(n.N>>>16)+(i>>>16),new Y((65535&a)<<16|65535&i,s)}function at(t,e){return new Y(t.N^e.N,t.I^e.I)}function st(t){var e=J(t,19),r=J(t,61),o=tt(t,6);return new Y(e.N^r.N^o.N,e.I^r.I^o.I)}function ct(t){var e=J(t,1),r=J(t,8),o=tt(t,7);return new Y(e.N^r.N^o.N,e.I^r.I^o.I)}function ut(t){var e=J(t,14),r=J(t,18),o=J(t,41);return new Y(e.N^r.N^o.N,e.I^r.I^o.I)}var lt=[new Y(S[0],3609767458),new Y(S[1],602891725),new Y(S[2],3964484399),new Y(S[3],2173295548),new Y(S[4],4081628472),new Y(S[5],3053834265),new Y(S[6],2937671579),new Y(S[7],3664609560),new Y(S[8],2734883394),new Y(S[9],1164996542),new Y(S[10],1323610764),new Y(S[11],3590304994),new Y(S[12],4068182383),new Y(S[13],991336113),new Y(S[14],633803317),new Y(S[15],3479774868),new Y(S[16],2666613458),new Y(S[17],944711139),new Y(S[18],2341262773),new Y(S[19],2007800933),new Y(S[20],1495990901),new Y(S[21],1856431235),new Y(S[22],3175218132),new Y(S[23],2198950837),new Y(S[24],3999719339),new Y(S[25],766784016),new Y(S[26],2566594879),new Y(S[27],3203337956),new Y(S[28],1034457026),new Y(S[29],2466948901),new Y(S[30],3758326383),new Y(S[31],168717936),new Y(S[32],1188179964),new Y(S[33],1546045734),new Y(S[34],1522805485),new Y(S[35],2643833823),new Y(S[36],2343527390),new Y(S[37],1014477480),new Y(S[38],1206759142),new Y(S[39],344077627),new Y(S[40],1290863460),new Y(S[41],3158454273),new Y(S[42],3505952657),new Y(S[43],106217008),new Y(S[44],3606008344),new Y(S[45],1432725776),new Y(S[46],1467031594),new Y(S[47],851169720),new Y(S[48],3100823752),new Y(S[49],1363258195),new Y(S[50],3750685593),new Y(S[51],3785050280),new Y(S[52],3318307427),new Y(S[53],3812723403),new Y(S[54],2003034995),new Y(S[55],3602036899),new Y(S[56],1575990012),new Y(S[57],1125592928),new Y(S[58],2716904306),new Y(S[59],442776044),new Y(S[60],593698344),new Y(S[61],3733110249),new Y(S[62],2999351573),new Y(S[63],3815920427),new Y(3391569614,3928383900),new Y(3515267271,566280711),new Y(3940187606,3454069534),new Y(4118630271,4000239992),new Y(116418474,1914138554),new Y(174292421,2731055270),new Y(289380356,3203993006),new Y(460393269,320620315),new Y(685471733,587496836),new Y(852142971,1086792851),new Y(1017036298,365543100),new Y(1126000580,2618297676),new Y(1288033470,3409855158),new Y(1501505948,4234509866),new Y(1607167915,987167468),new Y(1816402316,1246189591)];function pt(t){return"SHA-384"===t?[new Y(3418070365,P[0]),new Y(1654270250,P[1]),new Y(2438529370,P[2]),new Y(355462360,P[3]),new Y(1731405415,P[4]),new Y(41048885895,P[5]),new Y(3675008525,P[6]),new Y(1203062813,P[7])]:[new Y(k[0],4089235720),new Y(k[1],2227873595),new Y(k[2],4271175723),new Y(k[3],1595750129),new Y(k[4],2917565137),new Y(k[5],725511199),new Y(k[6],4215389547),new Y(k[7],327033209)]}function ft(t,e){var r,o,n,i,a,s,c,u,l,p,f,d,h,m,y,g=[];for(r=e[0],o=e[1],n=e[2],i=e[3],a=e[4],s=e[5],c=e[6],u=e[7],f=0;f<80;f+=1)f<16?(d=2*f,g[f]=new Y(t[d],t[d+1])):g[f]=nt(st(g[f-2]),g[f-7],ct(g[f-15]),g[f-16]),l=it(u,ut(a),(m=s,y=c,new Y((h=a).N&m.N^~h.N&y.N,h.I&m.I^~h.I&y.I)),lt[f],g[f]),p=ot(rt(r),et(r,o,n)),u=c,c=s,s=a,a=ot(i,l),i=n,n=o,o=r,r=ot(l,p);return e[0]=ot(r,e[0]),e[1]=ot(o,e[1]),e[2]=ot(n,e[2]),e[3]=ot(i,e[3]),e[4]=ot(a,e[4]),e[5]=ot(s,e[5]),e[6]=ot(c,e[6]),e[7]=ot(u,e[7]),e}var dt=function(t){function e(t,r,o){var n;if(f(this,e),"SHA-384"!==t&&"SHA-512"!==t)throw new Error(C);var i=o||{};return(n=a(this,e,[t,r,o])).g=n.Y,n.M=!0,n.T=-1,n.C=A(n.t,n.i,n.T),n.v=ft,n.L=function(t){return t.slice()},n.B=pt,n.F=function(e,r,o,n){return function(t,e,r,o,n){for(var i,a=31+(e+129>>>10<<5),s=e+r;t.length<=a;)t.push(0);for(t[e>>>5]|=128<<24-e%32,t[a]=4294967295&s,t[a-1]=s/w|0,i=0;i<t.length;i+=32)o=ft(t.slice(i,i+32),o);return"SHA-384"===n?[o[0].N,o[0].I,o[1].N,o[1].I,o[2].N,o[2].I,o[3].N,o[3].I,o[4].N,o[4].I,o[5].N,o[5].I]:[o[0].N,o[0].I,o[1].N,o[1].I,o[2].N,o[2].I,o[3].N,o[3].I,o[4].N,o[4].I,o[5].N,o[5].I,o[6].N,o[6].I,o[7].N,o[7].I]}(e,r,o,n,t)},n.U=pt(t),n.m=1024,n.R="SHA-384"===t?384:512,n.K=!1,i.hmacKey&&n.k(B("hmacKey",i.hmacKey,n.T)),n}return u(e,t),h(e)}(M),ht=[new Y(0,1),new Y(0,32898),new Y(2147483648,32906),new Y(2147483648,2147516416),new Y(0,32907),new Y(0,2147483649),new Y(2147483648,2147516545),new Y(2147483648,32777),new Y(0,138),new Y(0,136),new Y(0,2147516425),new Y(0,2147483658),new Y(0,2147516555),new Y(2147483648,139),new Y(2147483648,32905),new Y(2147483648,32771),new Y(2147483648,32770),new Y(2147483648,128),new Y(0,32778),new Y(2147483648,2147483658),new Y(2147483648,2147516545),new Y(2147483648,32896),new Y(0,2147483649),new Y(2147483648,2147516424)],mt=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]];function yt(t){var e,r=[];for(e=0;e<5;e+=1)r[e]=[new Y(0,0),new Y(0,0),new Y(0,0),new Y(0,0),new Y(0,0)];return r}function gt(t){var e,r=[];for(e=0;e<5;e+=1)r[e]=t[e].slice();return r}function vt(t,e){var r,o,n,i,a,s,c,u,l,p=[],f=[];if(null!==t)for(o=0;o<t.length;o+=2)e[(o>>>1)%5][(o>>>1)/5|0]=at(e[(o>>>1)%5][(o>>>1)/5|0],new Y(t[o+1],t[o]));for(r=0;r<24;r+=1){for(i=yt(),o=0;o<5;o+=1)p[o]=(a=e[o][0],s=e[o][1],c=e[o][2],u=e[o][3],l=e[o][4],new Y(a.N^s.N^c.N^u.N^l.N,a.I^s.I^c.I^u.I^l.I));for(o=0;o<5;o+=1)f[o]=at(p[(o+4)%5],Z(p[(o+1)%5],1));for(o=0;o<5;o+=1)for(n=0;n<5;n+=1)e[o][n]=at(e[o][n],f[o]);for(o=0;o<5;o+=1)for(n=0;n<5;n+=1)i[n][(2*o+3*n)%5]=Z(e[o][n],mt[o][n]);for(o=0;o<5;o+=1)for(n=0;n<5;n+=1)e[o][n]=at(i[o][n],new Y(~i[(o+1)%5][n].N&i[(o+2)%5][n].N,~i[(o+1)%5][n].I&i[(o+2)%5][n].I));e[0][0]=at(e[0][0],ht[r])}return e}function bt(t){var e,r,o=0,n=[0,0],i=[4294967295&t,t/w&2097151];for(e=6;e>=0;e--)0==(r=i[e>>2]>>>8*e&255)&&0===o||(n[o+1>>2]|=r<<8*(o+1),o+=1);return o=0!==o?o:1,n[0]|=o,{value:o+1>4?n:[n[0]],binLen:8+8*o}}function At(t){return E(bt(t.binLen),t)}function xt(t,e){var r,o=bt(e),n=e>>>2,i=(n-(o=E(o,t)).value.length%n)%n;for(r=0;r<i;r++)o.value.push(0);return o.value}var wt=function(t){function e(t,r,o){var n;f(this,e);var i=6,s=0,c=o||{};if(1!==(n=a(this,e,[t,r,o])).numRounds){if(c.kmacKey||c.hmacKey)throw new Error(O);if("CSHAKE128"===n.o||"CSHAKE256"===n.o)throw new Error("Cannot set numRounds for CSHAKE variants")}switch(n.T=1,n.C=A(n.t,n.i,n.T),n.v=vt,n.L=gt,n.B=yt,n.U=yt(),n.K=!1,t){case"SHA3-224":n.m=s=1152,n.R=224,n.M=!0,n.g=n.Y;break;case"SHA3-256":n.m=s=1088,n.R=256,n.M=!0,n.g=n.Y;break;case"SHA3-384":n.m=s=832,n.R=384,n.M=!0,n.g=n.Y;break;case"SHA3-512":n.m=s=576,n.R=512,n.M=!0,n.g=n.Y;break;case"SHAKE128":i=31,n.m=s=1344,n.R=-1,n.K=!0,n.M=!1,n.g=null;break;case"SHAKE256":i=31,n.m=s=1088,n.R=-1,n.K=!0,n.M=!1,n.g=null;break;case"KMAC128":i=4,n.m=s=1344,n.X(o),n.R=-1,n.K=!0,n.M=!1,n.g=n._;break;case"KMAC256":i=4,n.m=s=1088,n.X(o),n.R=-1,n.K=!0,n.M=!1,n.g=n._;break;case"CSHAKE128":n.m=s=1344,i=n.O(o),n.R=-1,n.K=!0,n.M=!1,n.g=null;break;case"CSHAKE256":n.m=s=1088,i=n.O(o),n.R=-1,n.K=!0,n.M=!1,n.g=null;break;default:throw new Error(C)}return n.F=function(t,e,r,o,n){return function(t,e,r,o,n,i,a){var s,c,u=0,l=[],p=n>>>5,f=e>>>5;for(s=0;s<f&&e>=n;s+=p)o=vt(t.slice(s,s+p),o),e-=n;for(t=t.slice(s),e%=n;t.length<p;)t.push(0);for(t[(s=e>>>3)>>2]^=i<<s%4*8,t[p-1]^=2147483648,o=vt(t,o);32*l.length<a&&(c=o[u%5][u/5|0],l.push(c.I),!(32*l.length>=a));)l.push(c.N),0==64*(u+=1)%n&&(vt(null,o),u=0);return l}(t,e,0,o,s,i,n)},c.hmacKey&&n.k(B("hmacKey",c.hmacKey,n.T)),n}return u(e,t),h(e,[{key:"O",value:function(t,e){var r=function(t){var e=t||{};return{funcName:B("funcName",e.funcName,1,{value:[],binLen:0}),customization:B("Customization",e.customization,1,{value:[],binLen:0})}}(t||{});e&&(r.funcName=e);var o=E(At(r.funcName),At(r.customization));if(0!==r.customization.binLen||0!==r.funcName.binLen){for(var n=xt(o,this.m>>>3),i=0;i<n.length;i+=this.m>>>5)this.U=this.v(n.slice(i,i+(this.m>>>5)),this.U),this.A+=this.m;return 4}return 31}},{key:"X",value:function(t){var e=function(t){var e=t||{};return{kmacKey:B("kmacKey",e.kmacKey,1),funcName:{value:[1128353099],binLen:32},customization:B("Customization",e.customization,1,{value:[],binLen:0})}}(t||{});this.O(t,e.funcName);for(var r=xt(At(e.kmacKey),this.m>>>3),o=0;o<r.length;o+=this.m>>>5)this.U=this.v(r.slice(o,o+(this.m>>>5)),this.U),this.A+=this.m;this.H=!0}},{key:"_",value:function(t){var e=E({value:this.h.slice(),binLen:this.u},function(t){var e,r,o=0,n=[0,0],i=[4294967295&t,t/w&2097151];for(e=6;e>=0;e--)0==(r=i[e>>2]>>>8*e&255)&&0===o||(n[o>>2]|=r<<8*o,o+=1);return n[(o=0!==o?o:1)>>2]|=o<<8*o,{value:o+1>4?n:[n[0]],binLen:8+8*o}}(t.outputLen));return this.F(e.value,e.binLen,this.A,this.L(this.U),t.outputLen)}}])}(M),St=function(){return h((function t(e,r,o){if(f(this,t),"SHA-1"==e)this.P=new V(e,r,o);else if("SHA-224"==e||"SHA-256"==e)this.P=new $(e,r,o);else if("SHA-384"==e||"SHA-512"==e)this.P=new dt(e,r,o);else{if("SHA3-224"!=e&&"SHA3-256"!=e&&"SHA3-384"!=e&&"SHA3-512"!=e&&"SHAKE128"!=e&&"SHAKE256"!=e&&"CSHAKE128"!=e&&"CSHAKE256"!=e&&"KMAC128"!=e&&"KMAC256"!=e)throw new Error(C);this.P=new wt(e,r,o)}}),[{key:"update",value:function(t){return this.P.update(t),this}},{key:"getHash",value:function(t,e){return this.P.getHash(t,e)}},{key:"setHMACKey",value:function(t,e,r){this.P.setHMACKey(t,e,r)}},{key:"getHMAC",value:function(t,e){return this.P.getHMAC(t,e)}}])}(),Pt="3.7.7",kt=Pt,Ct="function"==typeof Buffer,Ot="function"==typeof TextDecoder?new TextDecoder:void 0,Et="function"==typeof TextEncoder?new TextEncoder:void 0,Tt=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),Bt=function(t){var e={};return t.forEach((function(t,r){return e[t]=r})),e}(Tt),Mt=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,Rt=String.fromCharCode.bind(String),_t="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(t){return new Uint8Array(Array.prototype.slice.call(t,0))},It=function(t){return t.replace(/=/g,"").replace(/[+\/]/g,(function(t){return"+"==t?"-":"_"}))},jt=function(t){return t.replace(/[^A-Za-z0-9\+\/]/g,"")},Dt=function(t){for(var e,r,o,n,i="",a=t.length%3,s=0;s<t.length;){if((r=t.charCodeAt(s++))>255||(o=t.charCodeAt(s++))>255||(n=t.charCodeAt(s++))>255)throw new TypeError("invalid character found");i+=Tt[(e=r<<16|o<<8|n)>>18&63]+Tt[e>>12&63]+Tt[e>>6&63]+Tt[63&e]}return a?i.slice(0,a-3)+"===".substring(a):i},Lt="function"==typeof btoa?function(t){return btoa(t)}:Ct?function(t){return Buffer.from(t,"binary").toString("base64")}:Dt,qt=Ct?function(t){return Buffer.from(t).toString("base64")}:function(t){for(var e=[],r=0,o=t.length;r<o;r+=4096)e.push(Rt.apply(null,t.subarray(r,r+4096)));return Lt(e.join(""))},Ft=function(t){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?It(qt(t)):qt(t)},Nt=function(t){if(t.length<2)return(e=t.charCodeAt(0))<128?t:e<2048?Rt(192|e>>>6)+Rt(128|63&e):Rt(224|e>>>12&15)+Rt(128|e>>>6&63)+Rt(128|63&e);var e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return Rt(240|e>>>18&7)+Rt(128|e>>>12&63)+Rt(128|e>>>6&63)+Rt(128|63&e)},Ut=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,Gt=function(t){return t.replace(Ut,Nt)},Ht=Ct?function(t){return Buffer.from(t,"utf8").toString("base64")}:Et?function(t){return qt(Et.encode(t))}:function(t){return Lt(Gt(t))},zt=function(t){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?It(Ht(t)):Ht(t)},Kt=function(t){return zt(t,!0)},Wt=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,Vt=function(t){switch(t.length){case 4:var e=((7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3))-65536;return Rt(55296+(e>>>10))+Rt(56320+(1023&e));case 3:return Rt((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return Rt((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},Qt=function(t){return t.replace(Wt,Vt)},Xt=function(t){if(t=t.replace(/\s+/g,""),!Mt.test(t))throw new TypeError("malformed base64.");t+="==".slice(2-(3&t.length));for(var e,r,o,n="",i=0;i<t.length;)e=Bt[t.charAt(i++)]<<18|Bt[t.charAt(i++)]<<12|(r=Bt[t.charAt(i++)])<<6|(o=Bt[t.charAt(i++)]),n+=64===r?Rt(e>>16&255):64===o?Rt(e>>16&255,e>>8&255):Rt(e>>16&255,e>>8&255,255&e);return n},$t="function"==typeof atob?function(t){return atob(jt(t))}:Ct?function(t){return Buffer.from(t,"base64").toString("binary")}:Xt,Yt=Ct?function(t){return _t(Buffer.from(t,"base64"))}:function(t){return _t($t(t).split("").map((function(t){return t.charCodeAt(0)})))},Zt=function(t){return Yt(te(t))},Jt=Ct?function(t){return Buffer.from(t,"base64").toString("utf8")}:Ot?function(t){return Ot.decode(Yt(t))}:function(t){return Qt($t(t))},te=function(t){return jt(t.replace(/[-_]/g,(function(t){return"-"==t?"+":"/"})))},ee=function(t){return Jt(te(t))},re=function(t){return{value:t,enumerable:!1,writable:!0,configurable:!0}},oe=function(){var t=function(t,e){return Object.defineProperty(String.prototype,t,re(e))};t("fromBase64",(function(){return ee(this)})),t("toBase64",(function(t){return zt(this,t)})),t("toBase64URI",(function(){return zt(this,!0)})),t("toBase64URL",(function(){return zt(this,!0)})),t("toUint8Array",(function(){return Zt(this)}))},ne=function(){var t=function(t,e){return Object.defineProperty(Uint8Array.prototype,t,re(e))};t("toBase64",(function(t){return Ft(this,t)})),t("toBase64URI",(function(){return Ft(this,!0)})),t("toBase64URL",(function(){return Ft(this,!0)}))},ie={version:Pt,VERSION:kt,atob:$t,atobPolyfill:Xt,btoa:Lt,btoaPolyfill:Dt,fromBase64:ee,toBase64:zt,encode:zt,encodeURI:Kt,encodeURL:Kt,utob:Gt,btou:Qt,decode:ee,isValid:function(t){if("string"!=typeof t)return!1;var e=t.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(e)||!/[^\s0-9a-zA-Z\-_]/.test(e)},fromUint8Array:Ft,toUint8Array:Zt,extendString:oe,extendUint8Array:ne,extendBuiltins:function(){oe(),ne()}},ae=n(3264),se=n.n(ae),ce=new function(){var t=function t(e){var r={};if(1===e.nodeType){if(e.attributes.length>0){r["@attributes"]={};for(var o=0;o<e.attributes.length;o++){var n=e.attributes.item(o);r["@attributes"][n.nodeName]=n.value}}}else 3===e.nodeType&&(r=e.nodeValue);if(e.hasChildNodes())for(var i=0;i<e.childNodes.length;i++){var a=e.childNodes.item(i),s=a.nodeName;if(void 0===r[s])r[s]=t(a);else{if(void 0===r[s].push){var c=r[s];r[s]=[],r[s].push(c)}r[s].push(t(a))}}return r};this.parseString=function(e,r){var o;window.DOMParser?o=(new window.DOMParser).parseFromString(e,"text/xml"):(o=new window.ActiveXObject("Microsoft.XMLDOM")).async="false";var n,i,a=(n=t(o),-1===(i=JSON.stringify(n,void 0,2).replace(/(\\t|\\r|\\n)/g,"").replace(/"",[\n\t\r\s]+""[,]*/g,"").replace(/(\n[\t\s\r]*\n)/g,"").replace(/[\s\t]{2,}""[,]{0,1}/g,"").replace(/"[\s\t]{1,}"[,]{0,1}/g,"")).indexOf('"parsererror": {')?i:"Invalid XML format");return void 0===r?JSON.parse(a):a}},ue={location:"xml",sentAs:"Domain",type:"array",items:{type:"object",parameters:{Name:{type:"string",sentAs:"Name"},Action:{type:"string",sentAs:"Action"},Status:{type:"string",sentAs:"Status"},Agency:{type:"string",sentAs:"Agency"},Filter:{type:"object",sentAs:"Filter",parameters:{Object:{type:"object",sentAs:"Object",parameters:{FilterRule:{type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{type:"string",sentAs:"Name"},Value:{type:"string",sentAs:"Value"}}}}}}}},Events:{type:"array",items:{type:"adapter"},sentAs:"Event"}}}},le={SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseMode:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseAlgorithm:{location:"header",sentAs:"server-side-data-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}},pe={SseMode:{location:"header",sentAs:"server-side-encryption",withPrefix:!0,type:"adapter"},SseAlgorithm:{location:"header",sentAs:"server-side-data-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0}},fe={httpMethod:"POST",parameters:{ApiPath:{location:"uri"},CreateReviewTask:{required:!0,location:"body"}}},de={data:{type:"body"},parameters:{CreateReviewTask:{location:"body"}}},he={httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},me={data:{type:"body"},parameters:{result:{location:"body"}}},ye={httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},CreateReviewTask:{required:!0,location:"body"}}},ge={data:{type:"body"},parameters:{CreateReviewTask:{location:"body"}}},ve={httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"task_name"}}},be={httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Type:{location:"urlPath",sentAs:"type"},Limit:{type:"number",location:"urlPath",sentAs:"limit"},Offset:{type:"number",location:"urlPath",sentAs:"offset"}}},Ae={data:{type:"body"},parameters:{result:{location:"body"}}},xe={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Filter:{type:"object",sentAs:"Filter",parameters:{And:{type:"object",sentAs:"And",parameters:{Prefix:{sentAs:"Prefix"},Tag:{type:"array",sentAs:"Tag",items:{type:"object",location:"xml",sentAs:"Tag",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}}}}}},Transitions:{type:"array",sentAs:"Transition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"}}}},Expiration:{type:"object",sentAs:"Expiration",parameters:{Date:{sentAs:"Date"},Days:{type:"number",sentAs:"Days"},ExpiredObjectDeleteMarker:{sentAs:"ExpiredObjectDeleteMarker"}}},AbortIncompleteMultipartUpload:{type:"object",sentAs:"AbortIncompleteMultipartUpload",parameters:{DaysAfterInitiation:{type:"number",sentAs:"DaysAfterInitiation"}}},NoncurrentVersionTransitions:{type:"array",sentAs:"NoncurrentVersionTransition",items:{type:"object",parameters:{StorageClass:{sentAs:"StorageClass",type:"adapter"},NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}},NoncurrentVersionExpiration:{type:"object",sentAs:"NoncurrentVersionExpiration",parameters:{NoncurrentDays:{type:"number",sentAs:"NoncurrentDays"}}}}}},we={location:"xml",type:"object",parameters:{DefaultRetention:{type:"object",sentAs:"DefaultRetention",parameters:{Days:{sentAs:"Days"},Years:{sentAs:"Years"},Mode:{sentAs:"Mode"}}}}},Se={Name:{location:"xml",sentAs:"Name"},CertificateId:{location:"xml",sentAs:"CertificateId"},Certificate:{location:"xml",sentAs:"Certificate"},CertificateChain:{location:"xml",sentAs:"CertificateChain"},PrivateKey:{location:"xml",sentAs:"PrivateKey"}},Pe={type:"array",location:"xml",sentAs:"FunctionGraphConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionGraph:{},Event:{type:"array",items:{type:"adapter"}}}}},ke={type:"array",location:"xml",sentAs:"TopicConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},Topic:{},Event:{type:"array",items:{type:"adapter"}}}}},Ce={type:"array",location:"xml",sentAs:"EventGridConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},ChannelId:{},ObjectKeyEncode:{},UseOBSMessageLayout:{},Event:{type:"array",items:{type:"adapter"}}}}},Oe={type:"array",location:"xml",sentAs:"FunctionStageConfiguration",items:{type:"object",location:"xml",parameters:{ID:{sentAs:"Id"},Filter:{type:"object",parameters:{FilterRules:{wrapper:"Object",type:"array",sentAs:"FilterRule",items:{type:"object",parameters:{Name:{},Value:{}}}}}},FunctionStage:{},Event:{type:"array",items:{type:"adapter"}}}}};function Ee(t){return Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ee(t)}function Te(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}function Be(t,e,r){return e=function(t){var e=function(t,e){if("object"!=Ee(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!=Ee(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ee(e)?e:e+""}(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Me={httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Te(Object(r),!0).forEach((function(e){Be(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Te(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"storage-class",withPrefix:!0},ObjectLockEnabeld:{location:"header",sentAs:"bucket-object-lock-enabled",withPrefix:!0},IESLocation:{location:"header",sentAs:"ies-location",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},Redundancy:{location:"header",sentAs:"bucket-redundancy",withPrefix:!0},IsFusionAllowUpgrade:{location:"header",sentAs:"fusion-allow-upgrade",withPrefix:!0},IsFusionAllowAlternative:{location:"header",sentAs:"fusion-allow-alternative",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantFullControlDelivered:{location:"header",sentAs:"grant-full-control-delivered",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadDelivered:{location:"header",sentAs:"grant-read-delivered",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"Location"}},pe)},Re={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"}}},_e={type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{decode:!0,sentAs:"Prefix"},MTime:{sentAs:"MTime"},InodeNo:{sentAs:"InodeNo"}}}};function Ie(t){return Ie="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ie(t)}function je(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}function De(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?je(Object(r),!0).forEach((function(e){Le(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):je(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Le(t,e,r){return e=function(t){var e=function(t,e){if("object"!=Ie(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!=Ie(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Ie(e)?e:e+""}(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var qe={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"}}},Fe={type:"object",location:"xml",sentAs:"Initiator",parameters:{ID:{sentAs:"ID"}}},Ne={sentAs:"BackToSourceRule",required:!0,location:"xml",type:"array",items:{type:"object",parameters:{ID:{sentAs:"ID"},Condition:{sentAs:"Condition",type:"object",parameters:{ObjectKeyPrefixEquals:{sentAs:"ObjectKeyPrefixEquals"},HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"}}},Redirect:{sentAs:"Redirect",type:"object",parameters:{HttpRedirectCode:{sentAs:"HttpRedirectCode"},SourceEndpoint:{sentAs:"SourceEndpoint"},SourceBucketName:{sentAs:"SourceBucketName"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"},StaticUri:{sentAs:"StaticUri"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},MigrationConfiguration:{sentAs:"MigrationConfiguration",type:"object",parameters:{Agency:{sentAs:"Agency"},LogBucketName:{sentAs:"LogBucketName"},PrivateBucketConfiguration:{sentAs:"PrivateBucketConfiguration",type:"object",parameters:{SourceStorageProvider:{sentAs:"SourceStorageProvider"},SourceBucketAK:{sentAs:"SourceBucketAK"},SourceBucketSK:{sentAs:"SourceBucketSK"},SourceBucketZone:{sentAs:"SourceBucketZone"}}}}}}}}}},Ue={type:"array",location:"xml",wrapper:"AccessControlList",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID",notAllowEmpty:!0},URI:{sentAs:"Canned",type:"adapter",notAllowEmpty:!0}}},Permission:{sentAs:"Permission"},Delivered:{sentAs:"Delivered"}}}},Ge={type:"object",location:"xml",sentAs:"LoggingEnabled",parameters:{TargetBucket:{sentAs:"TargetBucket"},TargetPrefix:{sentAs:"TargetPrefix"},TargetGrants:{type:"array",wrapper:"TargetGrants",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},URI:{sentAs:"Canned",type:"adapter"}}},Permission:{sentAs:"Permission"}}}}}},He={type:"object",location:"xml",sentAs:"RedirectAllRequestsTo",parameters:{HostName:{sentAs:"HostName"},Protocol:{sentAs:"Protocol"}}},ze={type:"array",wrapper:"RoutingRules",location:"xml",sentAs:"RoutingRule",items:{type:"object",parameters:{Condition:{type:"object",sentAs:"Condition",parameters:{HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"},KeyPrefixEquals:{sentAs:"KeyPrefixEquals"}}},Redirect:{type:"object",sentAs:"Redirect",parameters:{HostName:{sentAs:"HostName"},HttpRedirectCode:{sentAs:"HttpRedirectCode"},Protocol:{sentAs:"Protocol"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"}}}}}},Ke={type:"object",location:"xml",sentAs:"IndexDocument",parameters:{Suffix:{sentAs:"Suffix"}}},We={type:"object",location:"xml",sentAs:"ErrorDocument",parameters:{Key:{sentAs:"Key"}}},Ve={required:!0,type:"array",location:"xml",sentAs:"CORSRule",items:{type:"object",parameters:{ID:{sentAs:"ID"},AllowedMethod:{type:"array",sentAs:"AllowedMethod",items:{type:"string"}},AllowedOrigin:{type:"array",sentAs:"AllowedOrigin",items:{type:"string"}},AllowedHeader:{type:"array",sentAs:"AllowedHeader",items:{type:"string"}},MaxAgeSeconds:{type:"number",sentAs:"MaxAgeSeconds"},ExposeHeader:{type:"array",sentAs:"ExposeHeader",items:{type:"string"}}}}},Qe={required:!0,type:"array",location:"xml",wrapper:"TagSet",sentAs:"Tag",items:{type:"object",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}},Xe={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Destination:{type:"object",sentAs:"Destination",parameters:{Bucket:{sentAs:"Bucket",type:"adapter"},StorageClass:{sentAs:"StorageClass",type:"adapter"},DeleteData:{sentAs:"DeleteData",type:"string"}}},HistoricalObjectReplication:{sentAs:"HistoricalObjectReplication"}}}},$e={type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"},ProjectID:{sentAs:"ProjectID"},KMSDataEncryption:{sentAs:"KMSDataEncryption"}}}}},Ye={type:"object",location:"xml",sentAs:"InventoryConfiguration",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"},Encryption:{type:"object",sentAs:"Encryption",parameters:{"SSE-KMS":{type:"object",sentAs:"SSE-KMS",parameters:{KeyId:{sentAs:"KeyId",type:"adapter"}}}}}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}}}},Ze={HeadBucket:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"}}},HeadApiVersion:{httpMethod:"HEAD",urlPath:"apiversion",parameters:{Bucket:{location:"uri"}}},HeadApiVersionOutput:{parameters:{ApiVersion:{location:"header",sentAs:"x-obs-api"}}},CreateSfsBucket:{httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:{ApiPath:{location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"storage-class",withPrefix:!0},ObjectLockEnabeld:{location:"header",sentAs:"bucket-object-lock-enabled",withPrefix:!0},IESLocation:{location:"header",sentAs:"ies-location",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},Redundancy:{location:"header",sentAs:"bucket-redundancy",withPrefix:!0},IsFusionAllowUpgrade:{location:"header",sentAs:"fusion-allow-upgrade",withPrefix:!0},IsFusionAllowAlternative:{location:"header",sentAs:"fusion-allow-alternative",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"Location"}}},DeleteSfsBucket:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"}}},CreateBucket:Me,GetBucketMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"}}},GetBucketMetadataOutput:{parameters:{StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},ObsVersion:{location:"header",sentAs:"version",withPrefix:!0},Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"az-redundancy",withPrefix:!0},Redundancy:{location:"header",sentAs:"bucket-redundancy",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},IESLocation:{location:"header",sentAs:"ies-location",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}}},DeleteBucket:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"}}},ListBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"},IESLocation:{sentAs:"IESLocation"},BucketType:{sentAs:"BucketType"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},ListSfsBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},ApiPath:{location:"uri"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListSfsBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"},IESLocation:{sentAs:"IESLocation"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},ListObjects:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},Marker:{location:"urlPath",sentAs:"marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListObjectsOutput:{data:{type:"xml",xmlRoot:"ListBucketResult"},parameters:{Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Marker:{decode:!0,location:"xml",sentAs:"Marker"},NextMarker:{decode:!0,location:"xml",sentAs:"NextMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},EncodingType:{location:"xml",sentAs:"EncodingType"},Contents:{type:"array",location:"xml",sentAs:"Contents",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},StorageClass:{sentAs:"StorageClass"},Owner:Re}}},CommonPrefixes:_e}},ListVersions:{httpMethod:"GET",urlPath:"versions",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},VersionIdMarker:{location:"urlPath",sentAs:"version-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListVersionsOutput:{data:{type:"xml",xmlRoot:"ListVersionsResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Location:{location:"header",sentAs:"bucket-location",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},VersionIdMarker:{location:"xml",sentAs:"VersionIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},NextVersionIdMarker:{location:"xml",sentAs:"NextVersionIdMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Versions:{type:"array",location:"xml",sentAs:"Version",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},Owner:Re,StorageClass:{sentAs:"StorageClass"}}}},DeleteMarkers:{type:"array",location:"xml",sentAs:"DeleteMarker",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},Owner:Re}}},CommonPrefixes:_e}},PutBackToSource:{httpMethod:"PUT",data:{xmlRoot:"BackToSourceConfiguration"},urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"},BackToSourceRules:Ne,ContentMD5:{location:"header",sentAs:"Content-MD5"}}},DeleteBackToSource:{httpMethod:"DELETE",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSource:{httpMethod:"GET",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSourceOutput:{data:{type:"xml",xmlRoot:"BackToSourceConfiguration"},parameters:{BackToSourceRules:Ne}},GetBucketLocation:{httpMethod:"GET",urlPath:"location",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLocationOutput:{data:{type:"xml"},parameters:{Location:{location:"xml",sentAs:"Location"}}},GetBucketStorageInfo:{httpMethod:"GET",urlPath:"storageinfo",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStorageInfoOutput:{data:{type:"xml",xmlRoot:"GetBucketStorageInfoResult"},parameters:{Size:{location:"xml",sentAs:"Size"},ObjectNumber:{location:"xml",sentAs:"ObjectNumber"},StandardSize:{location:"xml",sentAs:"StandardSize"},StandardObjectNumber:{location:"xml",sentAs:"StandardObjectNumber"},WarmSize:{location:"xml",sentAs:"WarmSize"},WarmObjectNumber:{location:"xml",sentAs:"WarmObjectNumber"},ColdSize:{location:"xml",sentAs:"ColdSize"},ColdObjectNumber:{location:"xml",sentAs:"ColdObjectNumber"},DeepArchiveSize:{location:"xml",sentAs:"DeepArchiveSize"},DeepArchiveObjectNumber:{location:"xml",sentAs:"DeepArchiveObjectNumber"},HighPerformanceSize:{location:"xml",sentAs:"HighPerformanceSize"},HighPerformanceObjectNumber:{location:"xml",sentAs:"HighPerformanceObjectNumber"}}},SetBucketQuota:{httpMethod:"PUT",urlPath:"quota",data:{xmlRoot:"Quota"},parameters:{Bucket:{required:!0,location:"uri"},StorageQuota:{required:!0,location:"xml",sentAs:"StorageQuota"},NumberQuota:{required:!1,location:"xml",sentAs:"NumberQuota"}}},GetBucketQuota:{httpMethod:"GET",urlPath:"quota",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketQuotaOutput:{data:{type:"xml",xmlRoot:"Quota"},parameters:{StorageQuota:{location:"xml",sentAs:"StorageQuota"},NumberQuota:{location:"xml",sentAs:"NumberQuota"}}},SetBucketAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:qe,Grants:Ue}},GetBucketInventory:{httpMethod:"GET",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"ListInventoryConfiguration"},parameters:{Rules:{type:"array",location:"xml",sentAs:"InventoryConfiguration",items:{type:"object",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}},LastExportTime:{sentAs:"LastExportTime"}}}}}},SetBucketInventory:{httpMethod:"PUT",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"},InventoryConfiguration:Ye}},SetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:Ye}},DeleteBucketInventory:{httpMethod:"DELETE",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"}}},DeleteBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:Ye}},GetBucketAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{Owner:qe,Grants:Ue}},SetBucketLoggingConfiguration:{httpMethod:"PUT",urlPath:"logging",data:{xmlRoot:"BucketLoggingStatus",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},LoggingEnabled:Ge}},GetBucketLoggingConfiguration:{httpMethod:"GET",urlPath:"logging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLoggingConfigurationOutput:{data:{type:"xml",xmlRoot:"BucketLoggingStatus"},parameters:{Agency:{location:"xml",sentAs:"Agency"},LoggingEnabled:Ge}},SetSFSAcl:{httpMethod:"PUT",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetSFSAcl:{httpMethod:"GET",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},GetSFSAclOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteSFSAcl:{httpMethod:"DELETE",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketPolicy:{httpMethod:"PUT",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetBucketPolicy:{httpMethod:"GET",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicyOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteBucketPolicy:{httpMethod:"DELETE",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketDisPolicy:{httpMethod:"PUT",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},GetBucketDisPolicy:{httpMethod:"GET",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},ContentType:{location:"header",sentAs:"Content-Type"}}},GetBucketDisPolicyOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteBucketDisPolicy:{httpMethod:"DELETE",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},SetBucketLifecycleConfiguration:{httpMethod:"PUT",urlPath:"lifecycle",data:{xmlRoot:"LifecycleConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Rules:xe}},GetBucketLifecycleConfiguration:{httpMethod:"GET",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLifecycleConfigurationOutput:{data:{type:"xml",xmlRoot:"LifecycleConfiguration"},parameters:{Rules:xe}},DeleteBucketLifecycleConfiguration:{httpMethod:"DELETE",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketWebsiteConfiguration:{httpMethod:"PUT",urlPath:"website",data:{xmlRoot:"WebsiteConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},RedirectAllRequestsTo:He,IndexDocument:Ke,ErrorDocument:We,RoutingRules:ze}},GetBucketWebsiteConfiguration:{httpMethod:"GET",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketWebsiteConfigurationOutput:{data:{type:"xml",xmlRoot:"WebsiteConfiguration"},parameters:{RedirectAllRequestsTo:He,IndexDocument:Ke,ErrorDocument:We,RoutingRules:ze}},DeleteBucketWebsiteConfiguration:{httpMethod:"DELETE",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketVersioningConfiguration:{httpMethod:"PUT",urlPath:"versioning",data:{xmlRoot:"VersioningConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},VersionStatus:{required:!0,location:"xml",sentAs:"Status"}}},GetBucketVersioningConfiguration:{httpMethod:"GET",urlPath:"versioning",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketVersioningConfigurationOutput:{data:{type:"xml",xmlRoot:"VersioningConfiguration"},parameters:{VersionStatus:{location:"xml",sentAs:"Status"}}},SetBucketCors:{httpMethod:"PUT",urlPath:"cors",data:{xmlRoot:"CORSConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},CorsRules:Ve}},GetBucketCors:{httpMethod:"GET",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCorsOutput:{data:{type:"xml",xmlRoot:"CORSConfiguration"},parameters:{CorsRules:Ve}},DeleteBucketCors:{httpMethod:"DELETE",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketNotification:{httpMethod:"PUT",urlPath:"notification",data:{xmlRoot:"NotificationConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},TopicConfigurations:ke,FunctionGraphConfigurations:Pe,FunctionStageConfigurations:Oe,EventGridConfigurations:Ce}},GetBucketNotification:{httpMethod:"GET",urlPath:"notification",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketNotificationOutput:{data:{type:"xml",xmlRoot:"NotificationConfiguration"},parameters:{RequestId:{location:"header",sentAs:"x-obs-request-id"},TopicConfigurations:ke,FunctionGraphConfigurations:Pe,FunctionStageConfigurations:Oe,EventGridConfigurations:Ce}},GetBucketObjectLockConfiguration:{httpMethod:"GET",urlPath:"object-lock",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketObjectLockConfigurationOutput:{data:{type:"xml",xmlRoot:"ObjectLockConfiguration"},parameters:{Rule:we}},SetBucketObjectLockConfig:{httpMethod:"PUT",urlPath:"object-lock",data:{type:"xml",xmlAllowEmpty:!0,xmlRoot:"ObjectLockConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:we}},SetBucketTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Tags:Qe}},DeleteBucketTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:Qe}},SetBucketStoragePolicy:{httpMethod:"PUT",urlPath:"storageClass",parameters:{Bucket:{required:!0,location:"uri"},StorageClass:{required:!0,location:"xml",type:"adapter",sentAs:"StorageClass"}}},GetBucketStoragePolicy:{httpMethod:"GET",urlPath:"storageClass",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStoragePolicyOutput:{data:{type:"xml"},parameters:{StorageClass:{location:"xml",type:"string",sentAs:"StorageClass"}}},SetBucketReplication:{httpMethod:"PUT",urlPath:"replication",data:{xmlRoot:"ReplicationConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},Rules:Xe}},GetBucketReplication:{httpMethod:"GET",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketReplicationOutput:{data:{type:"xml",xmlRoot:"ReplicationConfiguration"},parameters:{Agency:{location:"xml",sentAs:"Agency"},Rules:Xe}},DeleteBucketReplication:{httpMethod:"DELETE",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},PutObject:{httpMethod:"PUT",parameters:De(De({ObjectLockMode:{location:"header",sentAs:"object-lock-mode",withPrefix:!0},ObjectLockRetainUntailDate:{location:"header",sentAs:"object-lock-retain-until-date",withPrefix:!0},Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentSHA256:{location:"header",sentAs:"content-sha256",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},SuccessActionRedirect:{location:"header",sentAs:"success-action-redirect"}},le),{},{Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}})},PutObjectOutput:{parameters:De({ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0}},le)},AppendObject:{httpMethod:"POST",urlPath:"append",parameters:De(De({Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Position:{location:"urlPath",sentAs:"position",type:"number"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0}},le),{},{Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}})},AppendObjectOutput:{parameters:De({ETag:{location:"header",sentAs:"etag"},NextPosition:{location:"header",sentAs:"next-append-position",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0}},le)},GetObject:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ResponseCacheControl:{location:"urlPath",sentAs:"response-cache-control"},ResponseContentDisposition:{location:"urlPath",sentAs:"response-content-disposition"},ResponseContentEncoding:{location:"urlPath",sentAs:"response-content-encoding"},ResponseContentLanguage:{location:"urlPath",sentAs:"response-content-language"},ResponseContentType:{location:"urlPath",sentAs:"response-content-type"},ResponseExpires:{location:"urlPath",sentAs:"response-expires"},VersionId:{location:"urlPath",sentAs:"versionId"},ImageProcess:{location:"urlPath",sentAs:"x-image-process"},IfMatch:{location:"header",sentAs:"If-Match"},IfModifiedSince:{location:"header",sentAs:"If-Modified-Since"},IfNoneMatch:{location:"header",sentAs:"If-None-Match"},IfUnmodifiedSince:{location:"header",sentAs:"If-Unmodified-Since"},Range:{location:"header",sentAs:"Range"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SaveByType:{type:"dstFile"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},GetObjectOutput:{data:{type:"body"},parameters:De(De({Content:{location:"body"},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},ContentLength:{location:"header",sentAs:"Content-Length"},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}},le),{},{Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}})},CopyObject:{httpMethod:"PUT",parameters:De(De({Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},CopySource:{required:!0,location:"header",sentAs:"copy-source",withPrefix:!0,skipEncoding:!0},CopySourceIfMatch:{location:"header",sentAs:"copy-source-if-match",withPrefix:!0},CopySourceIfModifiedSince:{location:"header",sentAs:"copy-source-if-modified-since",withPrefix:!0},CopySourceIfNoneMatch:{location:"header",sentAs:"copy-source-if-none-match",withPrefix:!0},CopySourceIfUnmodifiedSince:{location:"header",sentAs:"copy-source-if-unmodified-since",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},ContentEncoding:{location:"header",sentAs:"content-encoding"},ContentLanguage:{location:"header",sentAs:"content-language"},ContentDisposition:{location:"header",sentAs:"content-disposition"},CacheControl:{location:"header",sentAs:"cache-control"},TagDirective:{location:"header",sentAs:"tagging-directive",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"expires"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},SuccessActionRedirect:{location:"header",sentAs:"success-action-redirect"}},le),{},{CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}})},CopyObjectOutput:{data:{type:"xml",xmlRoot:"CopyObjectResult"},parameters:De({VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},CopySourceVersionId:{location:"header",sentAs:"copy-source-version-id",withPrefix:!0},ETag:{location:"xml",sentAs:"ETag"},LastModified:{location:"xml",sentAs:"LastModified"}},le)},RestoreObject:{httpMethod:"POST",urlPath:"restore",data:{xmlRoot:"RestoreRequest",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Days:{location:"xml",sentAs:"Days"},Tier:{wrapper:"RestoreJob",location:"xml",sentAs:"Tier"}}},GetObjectMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},GetObjectMetadataOutput:{parameters:De(De({ObjectLockMode:{location:"header",sentAs:"object-lock-mode",withPrefix:!0},ObjectLockRetainUntilDate:{location:"header",sentAs:"object-lock-retain-until-date",withPrefix:!0},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},TaggingCount:{location:"header",sentAs:"tagging-count",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},ObjectType:{location:"header",sentAs:"x-obs-object-type"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}},le),{},{Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},Expires:{location:"header",sentAs:"Expires"},ReplicationStatus:{location:"header",sentAs:"replication-status",withPrefix:!0}})},SetObjectMetadata:{httpMethod:"PUT",urlPath:"metadata",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentType:{location:"header",sentAs:"Content-Type"},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"Expires"},Metadata:{shape:"Sy",location:"header",type:"object",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}}},SetObjectMetadataOutput:{parameters:{Expires:{location:"header",sentAs:"Expires"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},SetObjectAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Delivered:{location:"xml",sentAs:"Delivered"},Owner:qe,Grants:Ue}},SetObjectAclOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0}}},SetObjectObjectLock:{httpMethod:"PUT",urlPath:"retention",data:{xmlRoot:"Retention"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Mode:{sentAs:"Mode",location:"xml"},RetainUntilDate:{sentAs:"RetainUntilDate",location:"xml"}}},GetObjectAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Delivered:{location:"xml",sentAs:"Delivered"},Owner:qe,Grants:Ue}},GetObjectTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:Qe}},SetObjectTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Tags:Qe}},DeleteObjectTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObject:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObjectOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0}}},DeleteObjects:{httpMethod:"POST",urlPath:"delete",data:{xmlRoot:"Delete",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Quiet:{location:"xml",sentAs:"Quiet"},EncodingType:{location:"xml",sentAs:"EncodingType"},Objects:{required:!0,type:"array",location:"xml",sentAs:"Object",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"}}}}}},DeleteObjectsOutput:{data:{type:"xml",xmlRoot:"DeleteResult"},EncodingType:{location:"xml",sentAs:"EncodingType"},parameters:{Deleteds:{type:"array",location:"xml",sentAs:"Deleted",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},DeleteMarker:{sentAs:"DeleteMarker"},DeleteMarkerVersionId:{sentAs:"DeleteMarkerVersionId"}}}},Errors:{type:"array",location:"xml",sentAs:"Error",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"},Code:{sentAs:"Code"},Message:{sentAs:"Message"}}}}}},InitiateMultipartUpload:{httpMethod:"POST",urlPath:"uploads",parameters:De({EncodingType:{location:"urlPath",sentAs:"encoding-type"},ObjectLockMode:{location:"header",sentAs:"object-lock-mode",withPrefix:!0},ObjectLockRetainUntailDate:{location:"header",sentAs:"object-lock-retain-until-date",withPrefix:!0},Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"expires",type:"number",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"}},le)},InitiateMultipartUploadOutput:{data:{type:"xml",xmlRoot:"InitiateMultipartUploadResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},ObjectEncryptionRule:le}},ListMultipartUploads:{httpMethod:"GET",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Delimiter:{location:"urlPath",sentAs:"delimiter"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxUploads:{type:"number",location:"urlPath",sentAs:"max-uploads"},Prefix:{location:"urlPath",sentAs:"prefix"},UploadIdMarker:{location:"urlPath",sentAs:"upload-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListMultipartUploadsOutput:{data:{type:"xml",xmlRoot:"ListMultipartUploadsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},UploadIdMarker:{location:"xml",sentAs:"UploadIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},NextUploadIdMarker:{location:"xml",sentAs:"NextUploadIdMarker"},MaxUploads:{location:"xml",sentAs:"MaxUploads"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},EncodingType:{location:"xml",sentAs:"EncodingType"},Uploads:{type:"array",location:"xml",sentAs:"Upload",items:{type:"object",parameters:{UploadId:{sentAs:"UploadId"},Key:{decode:!0,sentAs:"Key"},Initiated:{sentAs:"Initiated"},StorageClass:{sentAs:"StorageClass"},Owner:qe,Initiator:Fe}}},CommonPrefixes:{type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{decode:!0,sentAs:"Prefix"},MTime:{sentAs:"MTime"},InodeNo:{sentAs:"InodeNo"}}}}}},UploadPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,type:"number",location:"urlPath",sentAs:"partNumber"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentSHA256:{location:"header",sentAs:"content-sha256",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},Offset:{type:"plain"},PartSize:{type:"plain"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},UploadPartOutput:{parameters:De({ETag:{location:"header",sentAs:"etag"}},le)},ListParts:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},MaxParts:{type:"number",location:"urlPath",sentAs:"max-parts"},PartNumberMarker:{type:"number",location:"urlPath",sentAs:"part-number-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListPartsOutput:{data:{type:"xml",xmlRoot:"ListPartsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},PartNumberMarker:{location:"xml",sentAs:"PartNumberMarker"},NextPartNumberMarker:{location:"xml",sentAs:"NextPartNumberMarker"},MaxParts:{location:"xml",sentAs:"MaxParts"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},StorageClass:{location:"xml",sentAs:"StorageClass"},EncodingType:{location:"urlPath",sentAs:"EncodingType"},Initiator:Fe,Owner:qe,Parts:{type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"}}}}}},CopyPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,location:"urlPath",sentAs:"partNumber",type:"number"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},CopySource:{required:!0,location:"header",sentAs:"copy-source",skipEncoding:!0,withPrefix:!0},CopySourceRange:{location:"header",sentAs:"copy-source-range",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyPartOutput:{data:{type:"xml",xmlRoot:"CopyPartResult"},parameters:De({LastModified:{location:"xml",sentAs:"LastModified"},ETag:{location:"xml",sentAs:"ETag"}},le)},AbortMultipartUpload:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"}}},CompleteMultipartUpload:{httpMethod:"POST",data:{xmlRoot:"CompleteMultipartUpload"},parameters:De(De({Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}},le),{},{Parts:{required:!0,type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},ETag:{sentAs:"ETag"}}}},Callback:{location:"header",sentAs:"callback",withPrefix:!0,type:"callback",parameters:{CallbackUrl:{required:!0},CallbackBody:{required:!0},CallbackHost:{},CallbackBodyType:{}}}})},CompleteMultipartUploadOutput:{data:{type:"xml",xmlRoot:"CompleteMultipartUploadResult"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Location:{location:"xml",sentAs:"Location"},EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},ETag:{location:"xml",sentAs:"ETag"},SseKms:{location:"header",sentAs:"server-side-encryption",withPrefix:!0},SseKmsKey:{location:"header",sentAs:"server-side-encryption-kms-key-id",withPrefix:!0},SseKmsProjectId:{location:"header",sentAs:"sse-kms-key-project-id",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKeyMd5:{location:"header",sentAs:"server-side-encryption-customer-key-MD5",withPrefix:!0}},CallbackResponse:{location:"body",sentAs:"CallbackResponseBody"}},OptionsBucket:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsBucketOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},OptionsObject:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsObjectOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},GetBucketEncryption:{httpMethod:"GET",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:$e}},SetBucketEncryption:{httpMethod:"PUT",urlPath:"encryption",data:{xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:$e}},SetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:$e}},DeleteBucketEncryption:{httpMethod:"DELETE",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:$e}},GetBucketRequesterPay:{httpMethod:"GET",urlPath:"requestPayment",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPay:{httpMethod:"PUT",urlPath:"requestPayment",data:{xmlRoot:"RequestPaymentConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},SetMirrorBackToSource:{httpMethod:"PUT",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},SetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},GetMirrorBackToSource:{httpMethod:"GET",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteMirrorBackToSource:{httpMethod:"DELETE",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetBucketDirectColdAccess:{httpMethod:"GET",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccess:{httpMethod:"PUT",urlPath:"directcoldaccess",data:{xmlRoot:"DirectColdAccessConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Status:{required:!0,location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},DeleteBucketDirectColdAccess:{httpMethod:"DELETE",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},GetBucketCustomDomain:{httpMethod:"GET",urlPath:"customdomain",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCustomDomainOutput:{data:{type:"xml",xmlRoot:"ListBucketCustomDomainsResult"},parameters:{Domains:{location:"xml",type:"array",sentAs:"Domains",items:{type:"object",parameters:{DomainName:{sentAs:"DomainName"},Value:{sentAs:"CreateTime"},CertificateId:{sentAs:"CertificateId"}}}}}},SetBucketCustomDomain:{httpMethod:"PUT",data:{xmlRoot:"CustomDomainConfiguration",md5:!0},parameters:De({Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}},Se)},DeleteBucketCustomDomain:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}}},GetCDNNotifyConfiguration:{httpMethod:"GET",urlPath:"CDNNotifyConfiguration",parameters:{Bucket:{required:!0,location:"uri"},NotForwardTag:{type:"string",sentAs:"x-obs-not-forward-tag",location:"header"}}},GetCDNNotifyConfigurationOutput:{data:{type:"xml",xmlRoot:"CDNNotifyConfiguration"},parameters:{Domain:ue}},SetCdnNotifyConfiguration:{httpMethod:"PUT",urlPath:"CDNNotifyConfiguration",data:{xmlRoot:"CDNNotifyConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},NotForwardTag:{type:"string",sentAs:"x-obs-not-forward-tag",location:"header"},Domain:ue}},GetQuota:{httpMethod:"GET",urlPath:"quota"},GetQuotaOutput:{data:{type:"xml",xmlRoot:"MaxBucketNumber"},parameters:{Size:{location:"xml",sentAs:"Number"}}},GetWorkflowTrigger:{httpMethod:"GET",urlPath:"obsworkflowtriggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetWorkflowTriggerOutput:{data:{type:"body"},parameters:{rules:{location:"body"}}},DeleteWorkflowTrigger:{httpMethod:"DELETE",urlPath:"obsworkflowtriggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},CreateWorkflowTrigger:{httpMethod:"PUT",urlPath:"obsworkflowtriggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rule:{required:!0,location:"body"}}},RestoreFailedWorkflowExecution:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"execution_name"},GraphName:{required:!0,location:"urlPath",sentAs:"x-workflow-graph-name"}}},CreateTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Template:{required:!0,location:"body"}}},CreateWorkflow:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"graph_name"},Workflow_create:{location:"urlPath",sentAs:"x-workflow-create"},Workflow:{required:!0,location:"body"}}},CreateAuditPolicy:fe,CreateAuditPolicyOutput:de,GetAuditPolicy:he,GetAuditPolicyOutput:me,PutAuditPolicy:ye,PutAuditPolicyOutPut:ge,DeleteAuditPolicy:ve,GetAuditResult:be,GetAuditResultOutput:Ae,DeleteWorkflow:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"}}},UpdateWorkflow:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"},Graph_name:{required:!0,location:"body"}}},GetWorkflowList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name_prefix"},XObsLimit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsStart:{type:"number",location:"urlPath",sentAs:"x-workflow-start"}}},GetWorkflowListOutput:{data:{type:"body"},parameters:{workflows:{location:"body"}}},GetWorkflowTemplateList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowTemplateListOutput:{data:{type:"body"},parameters:{templates:{location:"body"}}},GetWorkflowInstanceList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"execution_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},Graph_name:{location:"urlPath",sentAs:"x-workflow-graph-name"},State:{location:"urlPath",sentAs:"x-workflow-execution-state"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowInstanceListOutput:{data:{type:"body"},parameters:{instances:{location:"body"}}},DeleteTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name"}}},GetActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsCategory:{type:"String",location:"urlPath",sentAs:"x-workflow-category"}}},GetActionTemplatesOutput:{data:{type:"body"},parameters:{actions:{location:"body"}}},GetWorkflowAuthorization:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},GetWorkflowAuthorizationOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAuthorization:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"}}},CreateOnlineDecom:{httpMethod:"PUT",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"},Decom:{required:!0,location:"body"}}},GetOnlineDecom:{httpMethod:"GET",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetOnlineDecomOutput:{data:{type:"body"},parameters:{Decom:{location:"body"}}},DeleteOnlineDecom:{httpMethod:"DELETE",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetPublicationTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetPublicationTemplatesOutput:{data:{type:"body"},parameters:{PublishedTemplates:{location:"body"}}},GetPublicationTemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetPublicationTemplateDetailOutput:{data:{type:"body"},parameters:{PublishTemplate:{location:"body"}}},GetWorkflowAgreements:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},GetWorkflowAgreementsOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAgreements:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},CreateMyActionTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},CreateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},GetMyActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetMyActionTemplatesOutput:{data:{type:"body"},parameters:{ActionTemplates:{location:"body"}}},GetMyactiontemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetMyactiontemplateDetailOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},UpdateMyActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},UpdateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},DeleteMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},ForbidMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},XObsForbid:{location:"urlPath",sentAs:"x-workflow-forbid"}}},UpdatePublicActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},PublicAction:{required:!0,location:"body"}}},GetOmPublicActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetOmPublicActionTemplatesOutput:{data:{type:"body"},parameters:{Templates:{location:"body"}}},SetBucketAlias:{httpMethod:"PUT",urlPath:"obsbucketalias",data:{xmlRoot:"CreateBucketAlias"},parameters:{Bucket:{required:!0,location:"uri"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}},GetBucketAlias:{httpMethod:"GET",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAliasOutput:{data:{type:"xml",xmlRoot:"AliasList"},parameters:{BucketAlias:{location:"xml",type:"object",sentAs:"BucketAlias",parameters:{Alias:{sentAs:"Alias"},BucketList:{sentAs:"Bucket",location:"xml",type:"array",wrapper:"BucketList",items:{type:"string"}}}}}},DeleteBucketAlias:{httpMethod:"DELETE",urlPath:"obsbucketalias",parameters:{Bucket:{required:!0,location:"uri"}}},BindBucketAlias:{httpMethod:"PUT",urlPath:"obsalias",data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},BindBucketAliasOutput:{data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},UnbindBucketAlias:{httpMethod:"DELETE",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},ListBucketsAlias:{httpMethod:"GET",urlPath:"obsbucketalias"},ListBucketsAliasOutput:{data:{type:"xml",xmlRoot:"ListBucketAliasResult"},parameters:{BucketAliasList:{location:"xml",sentAs:"BucketAliasList",type:"object",parameters:{BucketAlias:{location:"xml",type:"array",sentAs:"BucketAlias",items:{type:"object",parameters:{Alias:{sentAs:"Alias"},CreationDate:{sentAs:"CreationDate"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}}}}},Owner:{location:"xml",sentAs:"Owner",type:"object",parameters:{ID:{sentAs:"ID"}}}}},getSFSPermissionAcl:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},getSFSPermissionAclOutput:{data:{type:"body"},parameters:{PermissionGroup:{location:"body"}}},updateSFSPermissionAcl:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Params:{location:"body"}}},deleteSFSPermissionAcl:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"}}},getSFSPermissionGroupList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Limit:{location:"urlPath",sentAs:"limit"},Offset:{location:"urlPath",sentAs:"offset"}}},getSFSPermissionGroupListOutput:{data:{type:"body"},parameters:{PermissionGroups:{location:"body"}}},setSFSPermissionGroup:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Params:{location:"body"}}},updateSFSPermissionGroup:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Params:{location:"body"}}},getSFSPermissionGroup:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},getSFSPermissionGroupOutput:{data:{type:"body"},parameters:{PermissionGroup:{location:"body"}}},deleteSFSPermissionGroup:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"}}}};function Je(t){return Je="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Je(t)}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}function er(t,e,r){return e=function(t){var e=function(t,e){if("object"!=Je(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!=Je(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==Je(e)?e:e+""}(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var rr={httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach((function(e){er(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"x-default-storage-class"},IESLocation:{location:"header",sentAs:"ies-location",withPrefix:!0},ObjectLockEnabeld:{location:"header",sentAs:"bucket-object-lock-enabled",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},Redundancy:{location:"header",sentAs:"bucket-redundancy",withPrefix:!0},IsFusionAllowUpgrade:{location:"header",sentAs:"fusion-allow-upgrade",withPrefix:!0},IsFusionAllowAlternative:{location:"header",sentAs:"fusion-allow-alternative",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantFullControlDelivered:{location:"header",sentAs:"grant-full-control-delivered",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadDelivered:{location:"header",sentAs:"grant-read-delivered",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"LocationConstraint"}},pe)},or={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},nr={type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{decode:!0,sentAs:"Prefix"},MTime:{sentAs:"MTime"},InodeNo:{sentAs:"InodeNo"}}}};function ir(t){return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ir(t)}function ar(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}function sr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ar(Object(r),!0).forEach((function(e){cr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ar(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function cr(t,e,r){return e=function(t){var e=function(t,e){if("object"!=ir(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!=ir(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ir(e)?e:e+""}(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ur={type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},lr={type:"object",location:"xml",sentAs:"Rule",parameters:{ApplyServerSideEncryptionByDefault:{type:"object",sentAs:"ApplyServerSideEncryptionByDefault",parameters:{SSEAlgorithm:{sentAs:"SSEAlgorithm"},KMSMasterKeyID:{sentAs:"KMSMasterKeyID"},ProjectID:{sentAs:"ProjectID"},KMSDataEncryption:{sentAs:"KMSDataEncryption"}}}}},pr={type:"object",location:"xml",sentAs:"InventoryConfiguration",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"},Encryption:{type:"object",sentAs:"Encryption",parameters:{"SSE-KMS":{type:"object",sentAs:"SSE-KMS",parameters:{KeyId:{sentAs:"KeyId",type:"adapter"}}}}}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}}}},fr={type:"object",location:"xml",sentAs:"Initiator",parameters:{ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"}}},dr={sentAs:"BackToSourceRule",required:!0,location:"xml",type:"array",items:{type:"object",parameters:{ID:{sentAs:"ID"},Condition:{sentAs:"Condition",type:"object",parameters:{ObjectKeyPrefixEquals:{sentAs:"ObjectKeyPrefixEquals"},HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"}}},Redirect:{sentAs:"Redirect",type:"object",parameters:{HttpRedirectCode:{sentAs:"HttpRedirectCode"},SourceEndpoint:{sentAs:"SourceEndpoint"},SourceBucketName:{sentAs:"SourceBucketName"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"},StaticUri:{sentAs:"StaticUri"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},MigrationConfiguration:{sentAs:"MigrationConfiguration",type:"object",parameters:{Agency:{sentAs:"Agency"},LogBucketName:{sentAs:"LogBucketName"},PrivateBucketConfiguration:{sentAs:"PrivateBucketConfiguration",type:"object",parameters:{SourceStorageProvider:{sentAs:"SourceStorageProvider"},SourceBucketAK:{sentAs:"SourceBucketAK"},SourceBucketSK:{sentAs:"SourceBucketSK"},SourceBucketZone:{sentAs:"SourceBucketZone"}}}}}}}}}},hr={type:"array",location:"xml",wrapper:"AccessControlList",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{data:{xsiNamespace:"http://www.w3.org/2001/XMLSchema-instance",xsiType:"Type"},type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID",notAllowEmpty:!0},Name:{sentAs:"DisplayName"},URI:{sentAs:"URI",type:"adapter",notAllowEmpty:!0}}},Permission:{sentAs:"Permission"}}}},mr={type:"object",location:"xml",sentAs:"LoggingEnabled",parameters:{TargetBucket:{sentAs:"TargetBucket"},TargetPrefix:{sentAs:"TargetPrefix"},TargetGrants:{type:"array",wrapper:"TargetGrants",sentAs:"Grant",items:{type:"object",parameters:{Grantee:{data:{xsiNamespace:"http://www.w3.org/2001/XMLSchema-instance",xsiType:"Type"},type:"object",sentAs:"Grantee",parameters:{Type:{type:"ignore"},ID:{sentAs:"ID"},Name:{sentAs:"DisplayName"},URI:{sentAs:"URI",type:"adapter"}}},Permission:{sentAs:"Permission"}}}}}},yr={type:"object",location:"xml",sentAs:"RedirectAllRequestsTo",parameters:{HostName:{sentAs:"HostName"},Protocol:{sentAs:"Protocol"}}},gr={type:"array",wrapper:"RoutingRules",location:"xml",sentAs:"RoutingRule",items:{type:"object",parameters:{Condition:{type:"object",sentAs:"Condition",parameters:{HttpErrorCodeReturnedEquals:{sentAs:"HttpErrorCodeReturnedEquals"},KeyPrefixEquals:{sentAs:"KeyPrefixEquals"}}},Redirect:{type:"object",sentAs:"Redirect",parameters:{HostName:{sentAs:"HostName"},HttpRedirectCode:{sentAs:"HttpRedirectCode"},Protocol:{sentAs:"Protocol"},ReplaceKeyPrefixWith:{sentAs:"ReplaceKeyPrefixWith"},ReplaceKeyWith:{sentAs:"ReplaceKeyWith"}}}}}},vr={type:"object",location:"xml",sentAs:"IndexDocument",parameters:{Suffix:{sentAs:"Suffix"}}},br={type:"object",location:"xml",sentAs:"ErrorDocument",parameters:{Key:{sentAs:"Key"}}},Ar={required:!0,type:"array",location:"xml",sentAs:"CORSRule",items:{type:"object",parameters:{ID:{sentAs:"ID"},AllowedMethod:{type:"array",sentAs:"AllowedMethod",items:{type:"string"}},AllowedOrigin:{type:"array",sentAs:"AllowedOrigin",items:{type:"string"}},AllowedHeader:{type:"array",sentAs:"AllowedHeader",items:{type:"string"}},MaxAgeSeconds:{type:"number",sentAs:"MaxAgeSeconds"},ExposeHeader:{type:"array",sentAs:"ExposeHeader",items:{type:"string"}}}}},xr={required:!0,type:"array",location:"xml",wrapper:"TagSet",sentAs:"Tag",items:{type:"object",parameters:{Key:{sentAs:"Key"},Value:{sentAs:"Value"}}}},wr={required:!0,type:"array",location:"xml",sentAs:"Rule",items:{type:"object",parameters:{ID:{sentAs:"ID"},Prefix:{sentAs:"Prefix"},Status:{sentAs:"Status"},Destination:{type:"object",sentAs:"Destination",parameters:{Bucket:{sentAs:"Bucket",type:"adapter"},StorageClass:{sentAs:"StorageClass",type:"adapter"},DeleteData:{sentAs:"DeleteData",type:"string"}}},HistoricalObjectReplication:{sentAs:"HistoricalObjectReplication"}}}},Sr={HeadBucket:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"}}},HeadApiVersion:{httpMethod:"HEAD",urlPath:"apiversion",parameters:{Bucket:{location:"uri"}}},HeadApiVersionOutput:{parameters:{ApiVersion:{location:"header",sentAs:"x-obs-api"}}},CreateBucket:rr,CreateSfsBucket:{httpMethod:"PUT",data:{xmlRoot:"CreateBucketConfiguration"},parameters:{ApiPath:{location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0},StorageType:{location:"header",sentAs:"x-default-storage-class"},IESLocation:{location:"header",sentAs:"ies-location",withPrefix:!0},ObjectLockEnabeld:{location:"header",sentAs:"bucket-object-lock-enabled",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},Redundancy:{location:"header",sentAs:"bucket-redundancy",withPrefix:!0},IsFusionAllowUpgrade:{location:"header",sentAs:"fusion-allow-upgrade",withPrefix:!0},IsFusionAllowAlternative:{location:"header",sentAs:"fusion-allow-alternative",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadACP:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWrite:{location:"header",sentAs:"grant-write",withPrefix:!0},GrantWriteACP:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},Location:{location:"xml",sentAs:"LocationConstraint"}}},DeleteSfsBucket:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"}}},ListSfsBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},ApiPath:{location:"uri"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListSfsBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"},IESLocation:{sentAs:"IESLocation"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},SetSFSAcl:{httpMethod:"PUT",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetSFSAcl:{httpMethod:"GET",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},GetSFSAclOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteSFSAcl:{httpMethod:"DELETE",urlPath:"sfsacl",parameters:{Bucket:{required:!0,location:"uri"}}},getSFSPermissionAcl:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},getSFSPermissionAclOutput:{data:{type:"body"},parameters:{PermissionGroup:{location:"body"}}},updateSFSPermissionAcl:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Params:{location:"body"}}},deleteSFSPermissionAcl:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"}}},getSFSPermissionGroupList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Limit:{location:"urlPath",sentAs:"limit"},Offset:{location:"urlPath",sentAs:"offset"}}},getSFSPermissionGroupListOutput:{data:{type:"body"},parameters:{PermissionGroups:{location:"body"}}},setSFSPermissionGroup:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Params:{location:"body"}}},updateSFSPermissionGroup:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Params:{location:"body"}}},getSFSPermissionGroup:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},getSFSPermissionGroupOutput:{data:{type:"body"},parameters:{PermissionGroup:{location:"body"}}},deleteSFSPermissionGroup:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"}}},GetBucketMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"}}},GetBucketMetadataOutput:{parameters:{StorageClass:{location:"header",sentAs:"x-default-storage-class"},ObsVersion:{location:"header",sentAs:"x-obs-version"},Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},FileInterface:{location:"header",sentAs:"fs-file-interface",withPrefix:!0},Type:{location:"header",sentAs:"bucket-type",withPrefix:!0},MultiAz:{location:"header",sentAs:"x-obs-az-redundancy"},Redundancy:{location:"header",sentAs:"bucket-redundancy",withPrefix:!0},MultiEnterprise:{location:"header",sentAs:"epid",withPrefix:!0},ClusterType:{location:"header",sentAs:"cluster-type",withPrefix:!0},Cluster:{location:"header",sentAs:"location-clustergroup-id",withPrefix:!0},IESLocation:{location:"header",sentAs:"ies-location",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}}},DeleteBucket:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"}}},ListBuckets:{httpMethod:"GET",parameters:{Type:{sentAs:"x-obs-bucket-type",location:"header"},Location:{sentAs:"location",location:"header",withPrefix:!0}}},ListBucketsOutput:{data:{type:"xml",xmlRoot:"ListAllMyBucketsResult"},parameters:{Buckets:{location:"xml",sentAs:"Bucket",type:"array",wrapper:"Buckets",items:{type:"object",location:"xml",sentAs:"Bucket",parameters:{Name:{sentAs:"Name"},CreationDate:{sentAs:"CreationDate"},Location:{sentAs:"Location"},ClusterType:{sentAs:"ClusterType"},IESLocation:{sentAs:"IESLocation"},BucketType:{sentAs:"BucketType"}}}},Owner:{type:"object",location:"xml",sentAs:"Owner",parameters:{ID:{sentAs:"ID"},DisplayName:{sentAs:"DisplayName"}}}}},GetBucketLocation:{httpMethod:"GET",urlPath:"location",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLocationOutput:{data:{type:"xml",xmlRoot:"CreateBucketConfiguration"},parameters:{Location:{location:"xml",sentAs:"LocationConstraint"}}},ListObjects:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},Marker:{location:"urlPath",sentAs:"marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListObjectsOutput:{data:{type:"xml",xmlRoot:"ListBucketResult"},parameters:{Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Marker:{decode:!0,location:"xml",sentAs:"Marker"},NextMarker:{decode:!0,location:"xml",sentAs:"NextMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},EncodingType:{location:"xml",sentAs:"EncodingType"},Contents:{type:"array",location:"xml",sentAs:"Contents",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},StorageClass:{sentAs:"StorageClass"},Owner:or}}},CommonPrefixes:nr}},ListVersions:{httpMethod:"GET",urlPath:"versions",parameters:{Bucket:{required:!0,location:"uri"},Prefix:{location:"urlPath",sentAs:"prefix"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxKeys:{type:"number",location:"urlPath",sentAs:"max-keys"},Delimiter:{location:"urlPath",sentAs:"delimiter"},VersionIdMarker:{location:"urlPath",sentAs:"version-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListVersionsOutput:{data:{type:"xml",xmlRoot:"ListVersionsResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Location:{location:"header",sentAs:"bucket-region",withPrefix:!0},Bucket:{location:"xml",sentAs:"Name"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},VersionIdMarker:{location:"xml",sentAs:"VersionIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},NextVersionIdMarker:{location:"xml",sentAs:"NextVersionIdMarker"},MaxKeys:{location:"xml",sentAs:"MaxKeys"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},Versions:{type:"array",location:"xml",sentAs:"Version",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"},Type:{sentAs:"Type"},Owner:or,StorageClass:{sentAs:"StorageClass"}}}},DeleteMarkers:{type:"array",location:"xml",sentAs:"DeleteMarker",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},IsLatest:{sentAs:"IsLatest"},LastModified:{sentAs:"LastModified"},Owner:or}}},CommonPrefixes:nr}},PutBackToSource:{httpMethod:"PUT",data:{xmlRoot:"BackToSourceConfiguration"},urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"},BackToSourceRules:dr,ContentMD5:{location:"header",sentAs:"Content-MD5"}}},DeleteBackToSource:{httpMethod:"DELETE",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSource:{httpMethod:"GET",urlPath:"backtosource",parameters:{Bucket:{required:!0,location:"uri"}}},GetBackToSourceOutput:{data:{type:"xml",xmlRoot:"BackToSourceConfiguration"},parameters:{BackToSourceRules:dr}},GetBucketStorageInfo:{httpMethod:"GET",urlPath:"storageinfo",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStorageInfoOutput:{data:{type:"xml",xmlRoot:"GetBucketStorageInfoResult"},parameters:{Size:{location:"xml",sentAs:"Size"},ObjectNumber:{location:"xml",sentAs:"ObjectNumber"},StandardSize:{location:"xml",sentAs:"StandardSize"},StandardObjectNumber:{location:"xml",sentAs:"StandardObjectNumber"},Standard_IASize:{location:"xml",sentAs:"Standard_IASize"},Standard_IAObjectNumber:{location:"xml",sentAs:"Standard_IAObjectNumber"},GlacierSize:{location:"xml",sentAs:"GlacierSize"},GlacierObjectNumber:{location:"xml",sentAs:"GlacierObjectNumber"},DeepArchiveSize:{location:"xml",sentAs:"DeepArchiveSize"},DeepArchiveObjectNumber:{location:"xml",sentAs:"DeepArchiveObjectNumber"},HighPerformanceSize:{location:"xml",sentAs:"HighPerformanceSize"},HighPerformanceObjectNumber:{location:"xml",sentAs:"HighPerformanceObjectNumber"}}},SetBucketQuota:{httpMethod:"PUT",urlPath:"quota",data:{xmlRoot:"Quota"},parameters:{Bucket:{required:!0,location:"uri"},StorageQuota:{required:!0,location:"xml",sentAs:"StorageQuota"},NumberQuota:{required:!1,location:"xml",sentAs:"NumberQuota"}}},GetBucketQuota:{httpMethod:"GET",urlPath:"quota",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketQuotaOutput:{data:{type:"xml",xmlRoot:"Quota"},parameters:{StorageQuota:{location:"xml",sentAs:"StorageQuota"},NumberQuota:{location:"xml",sentAs:"NumberQuota"}}},SetBucketAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:ur,Grants:hr}},GetBucketAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{Owner:ur,Grants:hr}},SetBucketLoggingConfiguration:{httpMethod:"PUT",urlPath:"logging",data:{xmlRoot:"BucketLoggingStatus",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},LoggingEnabled:mr}},GetBucketLoggingConfiguration:{httpMethod:"GET",urlPath:"logging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLoggingConfigurationOutput:{data:{type:"xml",xmlRoot:"BucketLoggingStatus"},parameters:{LoggingEnabled:mr}},SetBucketPolicy:{httpMethod:"PUT",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"},Policy:{required:!0,location:"body"}}},GetBucketPolicy:{httpMethod:"GET",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketPolicyOutput:{data:{type:"body"},parameters:{Policy:{location:"body"}}},DeleteBucketPolicy:{httpMethod:"DELETE",urlPath:"policy",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketDisPolicy:{httpMethod:"PUT",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},GetBucketDisPolicy:{httpMethod:"GET",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},ContentType:{location:"header",sentAs:"Content-Type"}}},GetBucketDisPolicyOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},DeleteBucketDisPolicy:{httpMethod:"DELETE",urlPath:"disPolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},SetBucketLifecycleConfiguration:{httpMethod:"PUT",urlPath:"lifecycle",data:{xmlRoot:"LifecycleConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Rules:xe}},GetBucketLifecycleConfiguration:{httpMethod:"GET",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketLifecycleConfigurationOutput:{data:{type:"xml",xmlRoot:"LifecycleConfiguration"},parameters:{Rules:xe}},DeleteBucketLifecycleConfiguration:{httpMethod:"DELETE",urlPath:"lifecycle",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketWebsiteConfiguration:{httpMethod:"PUT",urlPath:"website",data:{xmlRoot:"WebsiteConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},RedirectAllRequestsTo:yr,IndexDocument:vr,ErrorDocument:br,RoutingRules:gr}},GetBucketWebsiteConfiguration:{httpMethod:"GET",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketWebsiteConfigurationOutput:{data:{type:"xml",xmlRoot:"WebsiteConfiguration"},parameters:{RedirectAllRequestsTo:yr,IndexDocument:vr,ErrorDocument:br,RoutingRules:gr}},DeleteBucketWebsiteConfiguration:{httpMethod:"DELETE",urlPath:"website",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketVersioningConfiguration:{httpMethod:"PUT",urlPath:"versioning",data:{xmlRoot:"VersioningConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},VersionStatus:{required:!0,location:"xml",sentAs:"Status"}}},GetBucketVersioningConfiguration:{httpMethod:"GET",urlPath:"versioning",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketVersioningConfigurationOutput:{data:{type:"xml",xmlRoot:"VersioningConfiguration"},parameters:{VersionStatus:{location:"xml",sentAs:"Status"}}},SetBucketCors:{httpMethod:"PUT",urlPath:"cors",data:{xmlRoot:"CORSConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},CorsRules:Ar}},GetBucketCors:{httpMethod:"GET",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCorsOutput:{data:{type:"xml",xmlRoot:"CORSConfiguration"},parameters:{CorsRules:Ar}},DeleteBucketCors:{httpMethod:"DELETE",urlPath:"cors",parameters:{Bucket:{required:!0,location:"uri"}}},SetBucketNotification:{httpMethod:"PUT",urlPath:"notification",data:{xmlRoot:"NotificationConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},TopicConfigurations:ke,FunctionGraphConfigurations:Pe,FunctionStageConfigurations:Oe,EventGridConfigurations:Ce}},GetBucketNotification:{httpMethod:"GET",urlPath:"notification",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketNotificationOutput:{data:{type:"xml",xmlRoot:"NotificationConfiguration"},parameters:{TopicConfigurations:ke,FunctionGraphConfigurations:Pe,FunctionStageConfigurations:Oe,EventGridConfigurations:Ce}},GetBucketObjectLockConfiguration:{httpMethod:"GET",urlPath:"object-lock",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketObjectLockConfigurationOutput:{data:{type:"xml",xmlRoot:"ObjectLockConfiguration"},parameters:{Rule:we}},SetBucketObjectLockConfig:{httpMethod:"PUT",urlPath:"object-lock",data:{type:"xml",xmlAllowEmpty:!0,xmlRoot:"ObjectLockConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:we}},SetBucketTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Tags:xr}},DeleteBucketTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:xr}},SetBucketStoragePolicy:{httpMethod:"PUT",urlPath:"storagePolicy",data:{xmlRoot:"StoragePolicy"},parameters:{Bucket:{required:!0,location:"uri"},StorageClass:{required:!0,location:"xml",type:"adapter",sentAs:"DefaultStorageClass"}}},GetBucketStoragePolicy:{httpMethod:"GET",urlPath:"storagePolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketStoragePolicyOutput:{data:{type:"xml",xmlRoot:"StoragePolicy"},parameters:{StorageClass:{location:"xml",type:"string",sentAs:"DefaultStorageClass"}}},SetBucketReplication:{httpMethod:"PUT",urlPath:"replication",data:{xmlRoot:"ReplicationConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Agency:{location:"xml",sentAs:"Agency"},Rules:wr}},GetBucketReplication:{httpMethod:"GET",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketReplicationOutput:{data:{type:"xml",xmlRoot:"ReplicationConfiguration"},parameters:{Agency:{location:"xml",sentAs:"Agency"},Rules:wr}},DeleteBucketReplication:{httpMethod:"DELETE",urlPath:"replication",parameters:{Bucket:{required:!0,location:"uri"}}},PutObject:{httpMethod:"PUT",parameters:sr(sr({Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentSHA256:{location:"header",sentAs:"content-sha256",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},Offset:{type:"plain"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"}},le),{},{Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}})},PutObjectOutput:{parameters:sr({ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0}},le)},AppendObject:{httpMethod:"POST",urlPath:"append",parameters:sr(sr({Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Position:{location:"urlPath",sentAs:"position",type:"number"},ContentMD5:{location:"header",sentAs:"Content-MD5"},Offset:{type:"plain"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLength:{location:"header",sentAs:"Content-Length",type:"plain"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"}},le),{},{Body:{location:"body"},SourceFile:{type:"srcFile"},ProgressCallback:{type:"plain"}})},AppendObjectOutput:{parameters:sr({ETag:{location:"header",sentAs:"etag"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0}},le)},GetObject:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ResponseCacheControl:{location:"urlPath",sentAs:"response-cache-control"},ResponseContentDisposition:{location:"urlPath",sentAs:"response-content-disposition"},ResponseContentEncoding:{location:"urlPath",sentAs:"response-content-encoding"},ResponseContentLanguage:{location:"urlPath",sentAs:"response-content-language"},ResponseContentType:{location:"urlPath",sentAs:"response-content-type"},ResponseExpires:{location:"urlPath",sentAs:"response-expires"},VersionId:{location:"urlPath",sentAs:"versionId"},ImageProcess:{location:"urlPath",sentAs:"x-image-process"},IfMatch:{location:"header",sentAs:"If-Match"},IfModifiedSince:{location:"header",sentAs:"If-Modified-Since"},IfNoneMatch:{location:"header",sentAs:"If-None-Match"},IfUnmodifiedSince:{location:"header",sentAs:"If-Unmodified-Since"},Range:{location:"header",sentAs:"Range"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SaveByType:{type:"dstFile"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},GetObjectOutput:{data:{type:"body"},parameters:sr(sr({Content:{location:"body"},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},ETag:{location:"header",sentAs:"etag"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentType:{location:"header",sentAs:"Content-Type"},Expires:{location:"header",sentAs:"Expires"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},ContentLength:{location:"header",sentAs:"Content-Length"},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}},le),{},{Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}})},CopyObject:{httpMethod:"PUT",parameters:sr(sr({Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},CopySource:{required:!0,location:"header",sentAs:"copy-source",withPrefix:!0,skipEncoding:!0},CopySourceIfMatch:{location:"header",sentAs:"copy-source-if-match",withPrefix:!0},CopySourceIfModifiedSince:{location:"header",sentAs:"copy-source-if-modified-since",withPrefix:!0},CopySourceIfNoneMatch:{location:"header",sentAs:"copy-source-if-none-match",withPrefix:!0},CopySourceIfUnmodifiedSince:{location:"header",sentAs:"copy-source-if-unmodified-since",withPrefix:!0},ContentType:{location:"header",sentAs:"Content-Type"},ContentEncoding:{location:"header",sentAs:"content-encoding"},ContentLanguage:{location:"header",sentAs:"content-language"},ContentDisposition:{location:"header",sentAs:"content-disposition"},CacheControl:{location:"header",sentAs:"cache-control"},TagDirective:{location:"header",sentAs:"tagging-directive",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"expires"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}},le),{},{CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}})},CopyObjectOutput:{data:{type:"xml",xmlRoot:"CopyObjectResult"},parameters:sr({VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},CopySourceVersionId:{location:"header",sentAs:"copy-source-version-id",withPrefix:!0},ETag:{location:"xml",sentAs:"ETag"},LastModified:{location:"xml",sentAs:"LastModified"}},le)},RestoreObject:{httpMethod:"POST",urlPath:"restore",data:{xmlRoot:"RestoreRequest",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Days:{location:"xml",sentAs:"Days"},Tier:{wrapper:"GlacierJobParameters",location:"xml",sentAs:"Tier"}}},GetObjectMetadata:{httpMethod:"HEAD",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0}}},GetObjectMetadataOutput:{parameters:sr(sr({ObjectLockMode:{location:"header",sentAs:"object-lock-mode",withPrefix:!0},ObjectLockRetainUntilDate:{location:"header",sentAs:"object-lock-retain-until-date",withPrefix:!0},Expiration:{location:"header",sentAs:"expiration",withPrefix:!0},LastModified:{location:"header",sentAs:"Last-Modified"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ETag:{location:"header",sentAs:"etag"},VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},Restore:{location:"header",sentAs:"restore",withPrefix:!0},ObjectType:{location:"header",sentAs:"x-obs-object-type"},NextPosition:{location:"header",sentAs:"x-obs-next-append-position"},AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"}},le),{},{Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},TaggingCount:{location:"header",sentAs:"tagging-count",withPrefix:!0},Expires:{location:"header",sentAs:"Expires"},ReplicationStatus:{location:"header",sentAs:"replication-status",withPrefix:!0}})},SetObjectMetadata:{httpMethod:"PUT",urlPath:"metadata",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Origin:{location:"header",sentAs:"Origin"},RequestHeader:{location:"header",sentAs:"Access-Control-Request-Headers"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"},ContentType:{location:"header",sentAs:"Content-Type"},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"Expires"},Metadata:{shape:"Sy",location:"header",type:"object",sentAs:"meta-",withPrefix:!0},MetadataDirective:{location:"header",sentAs:"metadata-directive",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0}}},SetObjectMetadataOutput:{parameters:{Expires:{location:"header",sentAs:"Expires"},ContentLength:{location:"header",sentAs:"Content-Length"},ContentType:{location:"header",sentAs:"Content-Type"},ContentLanguage:{location:"header",sentAs:"Content-Language"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition"},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{location:"header",type:"object",sentAs:"meta-",withPrefix:!0}}},SetObjectAcl:{httpMethod:"PUT",urlPath:"acl",data:{xmlRoot:"AccessControlPolicy"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},Owner:ur,Grants:hr}},SetObjectAclOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0}}},SetObjectObjectLock:{httpMethod:"PUT",urlPath:"retention",data:{xmlRoot:"Retention"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Mode:{sentAs:"Mode",location:"xml"},RetainUntilDate:{sentAs:"RetainUntilDate",location:"xml"}}},GetObjectAcl:{httpMethod:"GET",urlPath:"acl",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectAclOutput:{data:{type:"xml",xmlRoot:"AccessControlPolicy"},parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Owner:ur,Grants:hr}},GetObjectTagging:{httpMethod:"GET",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},GetObjectTaggingOutput:{data:{type:"xml",xmlRoot:"Tagging"},parameters:{Tags:xr}},SetObjectTagging:{httpMethod:"PUT",urlPath:"tagging",data:{xmlRoot:"Tagging",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"},Tags:xr}},DeleteObjectTagging:{httpMethod:"DELETE",urlPath:"tagging",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObject:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},VersionId:{location:"urlPath",sentAs:"versionId"}}},DeleteObjectOutput:{parameters:{VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},DeleteMarker:{location:"header",sentAs:"delete-marker",withPrefix:!0}}},DeleteObjects:{httpMethod:"POST",urlPath:"delete",data:{xmlRoot:"Delete",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Quiet:{location:"xml",sentAs:"Quiet"},EncodingType:{location:"xml",sentAs:"EncodingType"},Objects:{required:!0,type:"array",location:"xml",sentAs:"Object",items:{type:"object",parameters:{Key:{sentAs:"Key"},VersionId:{sentAs:"VersionId"}}}}}},DeleteObjectsOutput:{data:{type:"xml",xmlRoot:"DeleteResult"},parameters:{EncodingType:{location:"xml",sentAs:"EncodingType"},Deleteds:{type:"array",location:"xml",sentAs:"Deleted",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},DeleteMarker:{sentAs:"DeleteMarker"},DeleteMarkerVersionId:{sentAs:"DeleteMarkerVersionId"}}}},Errors:{type:"array",location:"xml",sentAs:"Error",items:{type:"object",parameters:{Key:{decode:!0,sentAs:"Key"},VersionId:{sentAs:"VersionId"},Code:{sentAs:"Code"},Message:{sentAs:"Message"}}}}}},InitiateMultipartUpload:{httpMethod:"POST",urlPath:"uploads",parameters:sr({EncodingType:{location:"urlPath",sentAs:"encoding-type"},ObjectLockMode:{location:"header",sentAs:"object-lock-mode",withPrefix:!0},ObjectLockRetainUntailDate:{location:"header",sentAs:"object-lock-retain-until-date",withPrefix:!0},Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},ACL:{location:"header",sentAs:"acl",withPrefix:!0,type:"adapter"},GrantRead:{location:"header",sentAs:"grant-read",withPrefix:!0},GrantReadAcp:{location:"header",sentAs:"grant-read-acp",withPrefix:!0},GrantWriteAcp:{location:"header",sentAs:"grant-write-acp",withPrefix:!0},GrantFullControl:{location:"header",sentAs:"grant-full-control",withPrefix:!0},StorageClass:{location:"header",sentAs:"storage-class",withPrefix:!0,type:"adapter"},Metadata:{type:"object",location:"header",sentAs:"meta-",withPrefix:!0},WebsiteRedirectLocation:{location:"header",sentAs:"website-redirect-location",withPrefix:!0},Tags:{location:"header",sentAs:"tagging",withPrefix:!0},Expires:{location:"header",sentAs:"x-obs-expires",type:"number"},ContentType:{location:"header",sentAs:"Content-Type"},CacheControl:{location:"header",sentAs:"Cache-Control"},ContentDisposition:{location:"header",sentAs:"Content-Disposition",encodingSafe:' ;/?:@&=+$,"'},ContentLanguage:{location:"header",sentAs:"Content-Language"},ContentEncoding:{location:"header",sentAs:"Content-Encoding"}},le)},InitiateMultipartUploadOutput:{data:{type:"xml",xmlRoot:"InitiateMultipartUploadResult"},parameters:sr({EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"}},le)},ListMultipartUploads:{httpMethod:"GET",urlPath:"uploads",parameters:{Bucket:{required:!0,location:"uri"},Delimiter:{location:"urlPath",sentAs:"delimiter"},KeyMarker:{location:"urlPath",sentAs:"key-marker"},MaxUploads:{type:"number",location:"urlPath",sentAs:"max-uploads"},Prefix:{location:"urlPath",sentAs:"prefix"},UploadIdMarker:{location:"urlPath",sentAs:"upload-id-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListMultipartUploadsOutput:{data:{type:"xml",xmlRoot:"ListMultipartUploadsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},KeyMarker:{decode:!0,location:"xml",sentAs:"KeyMarker"},UploadIdMarker:{location:"xml",sentAs:"UploadIdMarker"},NextKeyMarker:{decode:!0,location:"xml",sentAs:"NextKeyMarker"},Prefix:{decode:!0,location:"xml",sentAs:"Prefix"},Delimiter:{decode:!0,location:"xml",sentAs:"Delimiter"},NextUploadIdMarker:{location:"xml",sentAs:"NextUploadIdMarker"},MaxUploads:{location:"xml",sentAs:"MaxUploads"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},EncodingType:{location:"xml",sentAs:"EncodingType"},Uploads:{type:"array",location:"xml",sentAs:"Upload",items:{type:"object",parameters:{UploadId:{sentAs:"UploadId"},Key:{decode:!0,sentAs:"Key"},Initiated:{sentAs:"Initiated"},StorageClass:{sentAs:"StorageClass"},Owner:ur,Initiator:fr}}},CommonPrefixes:{type:"array",location:"xml",sentAs:"CommonPrefixes",items:{type:"object",parameters:{Prefix:{decode:!0,sentAs:"Prefix"},MTime:{sentAs:"MTime"},InodeNo:{sentAs:"InodeNo"}}}}}},UploadPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,type:"number",location:"urlPath",sentAs:"partNumber"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},ContentMD5:{location:"header",sentAs:"Content-MD5"},ContentSHA256:{location:"header",sentAs:"content-sha256",withPrefix:!0},Body:{location:"body"},SourceFile:{type:"srcFile"},Offset:{type:"plain"},PartSize:{type:"plain"},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},ProgressCallback:{type:"plain"}}},UploadPartOutput:{parameters:sr({ETag:{location:"header",sentAs:"etag"}},le)},ListParts:{httpMethod:"GET",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},MaxParts:{type:"number",location:"urlPath",sentAs:"max-parts"},PartNumberMarker:{type:"number",location:"urlPath",sentAs:"part-number-marker"},EncodingType:{location:"urlPath",sentAs:"encoding-type"}}},ListPartsOutput:{data:{type:"xml",xmlRoot:"ListPartsResult"},parameters:{Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},UploadId:{location:"xml",sentAs:"UploadId"},PartNumberMarker:{location:"xml",sentAs:"PartNumberMarker"},NextPartNumberMarker:{location:"xml",sentAs:"NextPartNumberMarker"},MaxParts:{location:"xml",sentAs:"MaxParts"},IsTruncated:{location:"xml",sentAs:"IsTruncated"},StorageClass:{location:"xml",sentAs:"StorageClass"},EncodingType:{location:"urlPath",sentAs:"EncodingType"},Initiator:fr,Owner:ur,Parts:{type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},LastModified:{sentAs:"LastModified"},ETag:{sentAs:"ETag"},Size:{sentAs:"Size"}}}}}},CopyPart:{httpMethod:"PUT",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},PartNumber:{required:!0,location:"urlPath",sentAs:"partNumber",type:"number"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},CopySource:{required:!0,location:"header",sentAs:"copy-source",skipEncoding:!0,withPrefix:!0},CopySourceRange:{location:"header",sentAs:"copy-source-range",withPrefix:!0},SseC:{location:"header",sentAs:"server-side-encryption-customer-algorithm",withPrefix:!0},SseCKey:{location:"header",sentAs:"server-side-encryption-customer-key",type:"password",withPrefix:!0},CopySourceSseC:{location:"header",sentAs:"copy-source-server-side-encryption-customer-algorithm",withPrefix:!0},CopySourceSseCKey:{location:"header",sentAs:"copy-source-server-side-encryption-customer-key",type:"password",withPrefix:!0}}},CopyPartOutput:{data:{type:"xml",xmlRoot:"CopyPartResult"},parameters:sr({LastModified:{location:"xml",sentAs:"LastModified"},ETag:{location:"xml",sentAs:"ETag"}},le)},AbortMultipartUpload:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"}}},CompleteMultipartUpload:{httpMethod:"POST",data:{xmlRoot:"CompleteMultipartUpload"},parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},UploadId:{required:!0,location:"urlPath",sentAs:"uploadId"},EncodingType:{location:"urlPath",sentAs:"encoding-type"},Parts:{required:!0,type:"array",location:"xml",sentAs:"Part",items:{type:"object",parameters:{PartNumber:{sentAs:"PartNumber"},ETag:{sentAs:"ETag"}}}},Callback:{location:"header",sentAs:"callback",withPrefix:!0,type:"callback",parameters:{CallbackUrl:{required:!0},CallbackBody:{required:!0},CallbackHost:{},CallbackBodyType:{}}}}},CompleteMultipartUploadOutput:{data:{type:"xml",xmlRoot:"CompleteMultipartUploadResult"},parameters:sr({VersionId:{location:"header",sentAs:"version-id",withPrefix:!0},Location:{location:"xml",sentAs:"Location"},EncodingType:{location:"xml",sentAs:"EncodingType"},Bucket:{location:"xml",sentAs:"Bucket"},Key:{decode:!0,location:"xml",sentAs:"Key"},ETag:{location:"xml",sentAs:"ETag"}},le),CallbackResponse:{location:"body",sentAs:"CallbackResponseBody"}},GetBucketInventory:{httpMethod:"GET",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"ListInventoryConfiguration"},parameters:{Rules:{type:"array",location:"xml",sentAs:"InventoryConfiguration",items:{type:"object",parameters:{Id:{sentAs:"Id"},IsEnabled:{sentAs:"IsEnabled"},Filter:{type:"object",sentAs:"Filter",parameters:{Prefix:{sentAs:"Prefix"}}},Destination:{type:"object",sentAs:"Destination",parameters:{Format:{sentAs:"Format"},Bucket:{sentAs:"Bucket"},Prefix:{sentAs:"Prefix"}}},Schedule:{type:"object",sentAs:"Schedule",parameters:{Frequency:{sentAs:"Frequency"}}},IncludedObjectVersions:{sentAs:"IncludedObjectVersions"},OptionalFields:{type:"object",location:"xml",sentAs:"OptionalFields",parameters:{Field:{type:"array",sentAs:"Field",items:{type:"string"}}}},LastExportTime:{sentAs:"LastExportTime"}}}}}},SetBucketInventory:{httpMethod:"PUT",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"},InventoryConfiguration:pr}},SetBucketInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:pr}},DeleteInventory:{httpMethod:"DELETE",urlPath:"inventory",parameters:{Bucket:{required:!0,location:"uri"},Id:{location:"urlPath",sentAs:"id"}}},DeleteInventoryOutput:{data:{type:"xml",xmlRoot:"InventoryConfiguration"},parameters:{InventoryConfiguration:pr}},GetBucketEncryption:{httpMethod:"GET",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:lr}},SetBucketEncryption:{httpMethod:"PUT",urlPath:"encryption",data:{xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Rule:lr}},SetBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:lr}},DeleteBucketEncryption:{httpMethod:"DELETE",urlPath:"encryption",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketEncryptionOutput:{data:{type:"xml",xmlRoot:"ServerSideEncryptionConfiguration"},parameters:{Rule:lr}},GetBucketRequesterPay:{httpMethod:"GET",urlPath:"requestPayment",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPay:{httpMethod:"PUT",urlPath:"requestPayment",data:{xmlRoot:"RequestPaymentConfiguration"},parameters:{Bucket:{required:!0,location:"uri"},Payer:{location:"xml",sentAs:"Payer"}}},SetBucketRequesterPayOutput:{data:{type:"xml",xmlRoot:"RequestPaymentConfiguration"},parameters:{Payer:{location:"xml",sentAs:"Payer"}}},OptionsBucket:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsBucketOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},OptionsObject:{httpMethod:"OPTIONS",parameters:{Bucket:{required:!0,location:"uri"},Key:{required:!0,location:"uri"},Origin:{required:!0,location:"header",sentAs:"Origin"},AccessControlRequestMethods:{required:!0,type:"array",location:"header",sentAs:"Access-Control-Request-Method",items:{type:"string"}},AccessControlRequestHeaders:{type:"array",location:"header",sentAs:"Access-Control-Request-Headers",items:{type:"string"}}}},OptionsObjectOutput:{parameters:{AllowOrigin:{location:"header",sentAs:"access-control-allow-origin"},AllowHeader:{location:"header",sentAs:"access-control-allow-headers"},AllowMethod:{location:"header",sentAs:"access-control-allow-methods"},ExposeHeader:{location:"header",sentAs:"access-control-expose-headers"},MaxAgeSeconds:{location:"header",sentAs:"access-control-max-age"}}},SetMirrorBackToSource:{httpMethod:"PUT",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rules:{required:!0,location:"body"}}},SetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},GetMirrorBackToSource:{httpMethod:"GET",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetMirrorBackToSourceOutput:{data:{type:"body"},parameters:{Rules:{location:"body"}}},deleteMirrorBackToSource:{httpMethod:"DELETE",urlPath:"mirrorBackToSource",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetBucketDirectColdAccess:{httpMethod:"GET",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccess:{httpMethod:"PUT",urlPath:"directcoldaccess",data:{xmlRoot:"DirectColdAccessConfiguration",md5:!0},parameters:{Bucket:{required:!0,location:"uri"},Status:{required:!0,location:"xml",sentAs:"Status"}}},SetBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},DeleteBucketDirectColdAccess:{httpMethod:"DELETE",urlPath:"directcoldaccess",parameters:{Bucket:{required:!0,location:"uri"}}},DeleteBucketDirectColdAccessOutput:{data:{type:"xml",xmlRoot:"DirectColdAccessConfiguration"},parameters:{Status:{location:"xml",sentAs:"Status"}}},GetBucketCustomDomain:{httpMethod:"GET",urlPath:"customdomain",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketCustomDomainOutput:{data:{type:"xml",xmlRoot:"ListBucketCustomDomainsResult"},parameters:{Domains:{location:"xml",type:"array",sentAs:"Domains",items:{type:"object",parameters:{DomainName:{sentAs:"DomainName"},Value:{sentAs:"CreateTime"},CertificateId:{sentAs:"CertificateId"}}}}}},SetBucketCustomDomain:{httpMethod:"PUT",data:{xmlRoot:"CustomDomainConfiguration",md5:!0},parameters:sr({Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}},Se)},DeleteBucketCustomDomain:{httpMethod:"DELETE",parameters:{Bucket:{required:!0,location:"uri"},DomainName:{location:"urlPath",sentAs:"customdomain"}}},GetCDNNotifyConfiguration:{httpMethod:"GET",urlPath:"CDNNotifyConfiguration",parameters:{Bucket:{required:!0,location:"uri"},NotForwardTag:{type:"string",sentAs:"x-obs-not-forward-tag",location:"header"}}},GetCDNNotifyConfigurationOutput:{data:{type:"xml",xmlRoot:"CDNNotifyConfiguration"},parameters:{Domain:ue}},SetCdnNotifyConfiguration:{httpMethod:"PUT",urlPath:"CDNNotifyConfiguration",data:{xmlRoot:"CDNNotifyConfiguration",xmlAllowEmpty:!0},parameters:{Bucket:{required:!0,location:"uri"},NotForwardTag:{type:"string",sentAs:"x-obs-not-forward-tag",location:"header"},Domain:ue}},GetQuota:{httpMethod:"GET",urlPath:"quota"},GetQuotaOutput:{data:{type:"xml",xmlRoot:"MaxBucketNumber"},parameters:{Size:{location:"xml",sentAs:"Number"}}},GetWorkflowTrigger:{httpMethod:"GET",urlPath:"triggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},GetWorkflowTriggerOutput:{data:{type:"body"},parameters:{rules:{location:"body"}}},DeleteWorkflowTrigger:{httpMethod:"DELETE",urlPath:"triggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"}}},CreateWorkflowTrigger:{httpMethod:"PUT",urlPath:"triggerpolicy",parameters:{Bucket:{required:!0,location:"uri"},ApiPath:{location:"uri"},Rule:{required:!0,location:"body"}}},RestoreFailedWorkflowExecution:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"execution_name"},GraphName:{required:!0,location:"urlPath",sentAs:"x-workflow-graph-name"}}},CreateTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Template:{required:!0,location:"body"}}},CreateWorkflow:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},Other_parameter:{required:!0,location:"uri",sentAs:"graph_name"},Workflow_create:{location:"urlPath",sentAs:"x-workflow-create"},Workflow:{required:!0,location:"body"}}},CreateAuditPolicy:fe,CreateAuditPolicyOutput:de,GetAuditPolicy:he,GetAuditPolicyOutput:me,PutAuditPolicy:ye,PutAuditPolicyOutPut:ge,DeleteAuditPolicy:ve,GetAuditResult:be,GetAuditResultOutput:Ae,DeleteWorkflow:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"}}},UpdateWorkflow:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name"},Graph_name:{required:!0,location:"body"}}},GetWorkflowList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"graph_name_prefix"},XObsLimit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsStart:{type:"number",location:"urlPath",sentAs:"x-workflow-start"}}},GetWorkflowListOutput:{data:{type:"body"},parameters:{workflows:{location:"body"}}},GetWorkflowTemplateList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowTemplateListOutput:{data:{type:"body"},parameters:{templates:{location:"body"}}},GetWorkflowInstanceList:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"execution_name_prefix"},Start:{type:"number",location:"urlPath",sentAs:"x-workflow-start"},Limit:{type:"number",location:"urlPath",sentAs:"x-workflow-limit"},Graph_name:{location:"urlPath",sentAs:"x-workflow-graph-name"},State:{location:"urlPath",sentAs:"x-workflow-execution-state"},"X-workflow-prefix":{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetWorkflowInstanceListOutput:{data:{type:"body"},parameters:{instances:{location:"body"}}},DeleteTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name"}}},GetActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},Other_parameter:{location:"uri",sentAs:"template_name_prefix"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"},XObsCategory:{type:"String",location:"urlPath",sentAs:"x-workflow-category"}}},GetActionTemplatesOutput:{data:{type:"body"},parameters:{templates:{location:"body"}}},GetWorkflowAuthorization:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"}}},GetWorkflowAuthorizationOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAuthorization:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"}}},CreateOnlineDecom:{httpMethod:"PUT",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"},Decom:{required:!0,location:"body"}}},GetOnlineDecom:{httpMethod:"GET",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetOnlineDecomOutput:{data:{type:"body"},parameters:{Decom:{location:"body"}}},DeleteOnlineDecom:{httpMethod:"DELETE",urlPath:"obscompresspolicy",parameters:{Bucket:{required:!0,location:"uri"}}},GetPublicationTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetPublicationTemplatesOutput:{data:{type:"body"},parameters:{PublishedTemplates:{location:"body"}}},GetPublicationTemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetPublicationTemplateDetailOutput:{data:{type:"body"},parameters:{PublishTemplate:{location:"body"}}},GetWorkflowAgreements:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},GetWorkflowAgreementsOutput:{data:{type:"body"},parameters:{authorization:{location:"body"}}},OpenWorkflowAgreements:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},XWorkflowType:{required:!0,location:"urlPath",sentAs:"x-workflow-type"}}},CreateMyActionTemplate:{httpMethod:"POST",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},CreateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},GetMyActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetMyActionTemplatesOutput:{data:{type:"body"},parameters:{ActionTemplates:{location:"body"}}},GetMyactiontemplateDetail:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},GetMyactiontemplateDetailOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},UpdateMyActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},ActionTemplate:{required:!0,location:"body"}}},UpdateMyActionTemplateOutput:{data:{type:"body"},parameters:{ActionTemplate:{location:"body"}}},DeleteMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"}}},ForbidMyActionTemplate:{httpMethod:"DELETE",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},XObsForbid:{location:"urlPath",sentAs:"x-workflow-forbid"}}},UpdatePublicActionTemplate:{httpMethod:"PUT",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name"},PublicAction:{required:!0,location:"body"}}},GetOmPublicActionTemplates:{httpMethod:"GET",parameters:{ApiPath:{location:"uri"},OtherParameter:{location:"uri",sentAs:"template_name_prefix"},XObsCategory:{location:"urlPath",sentAs:"x-workflow-category"},XObsOtatus:{location:"urlPath",sentAs:"x-workflow-status"},XObsPrefix:{location:"urlPath",sentAs:"x-workflow-prefix"}}},GetOmPublicActionTemplatesOutput:{data:{type:"body"},parameters:{Templates:{location:"body"}}},SetBucketAlias:{httpMethod:"PUT",urlPath:"obsbucketalias",data:{xmlRoot:"CreateBucketAlias"},parameters:{Bucket:{required:!0,location:"uri"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}},GetBucketAlias:{httpMethod:"GET",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},GetBucketAliasOutput:{data:{type:"xml",xmlRoot:"AliasList"},parameters:{BucketAlias:{location:"xml",type:"object",sentAs:"BucketAlias",parameters:{Alias:{sentAs:"Alias"},BucketList:{sentAs:"Bucket",location:"xml",type:"array",wrapper:"BucketList",items:{type:"string"}}}}}},DeleteBucketAlias:{httpMethod:"DELETE",urlPath:"obsbucketalias",parameters:{Bucket:{required:!0,location:"uri"}}},BindBucketAlias:{httpMethod:"PUT",urlPath:"obsalias",data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},BindBucketAliasOutput:{data:{xmlRoot:"AliasList"},parameters:{Bucket:{required:!0,location:"uri"},Alias:{location:"xml",type:"string",sentAs:"Alias"}}},UnbindBucketAlias:{httpMethod:"DELETE",urlPath:"obsalias",parameters:{Bucket:{required:!0,location:"uri"}}},ListBucketsAlias:{httpMethod:"GET",urlPath:"obsbucketalias"},ListBucketsAliasOutput:{data:{type:"xml",xmlRoot:"ListBucketAliasResult"},parameters:{BucketAliasList:{location:"xml",sentAs:"BucketAliasList",type:"object",parameters:{BucketAlias:{location:"xml",type:"array",sentAs:"BucketAlias",items:{type:"object",parameters:{Alias:{sentAs:"Alias"},CreationDate:{sentAs:"CreationDate"},BucketList:{location:"xml",type:"object",sentAs:"BucketList",parameters:{Bucket:{location:"xml",type:"array",items:{parameters:{sentAs:"Bucket"}}}}}}}}}},Owner:{location:"xml",sentAs:"Owner",type:"object",parameters:{ID:{sentAs:"ID"}}}}}},Pr=n(3784),kr=n.t(Pr,2);function Cr(t){return Cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(t)}function Or(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,o=new Array(e);r<e;r++)o[r]=t[r];return o}var Er={createHmac:function(t,e){var r=new St("sha1"===t?"SHA-1":"sha512"===t?"SHA-512":"SHA-256","TEXT");return r.setHMACKey(e,e instanceof ArrayBuffer?"ARRAYBUFFER":"TEXT"),{update:function(t){return r.update(t),this},digest:function(t){return"hex"===t?r.getHMAC("HEX"):"base64"===t?r.getHMAC("B64"):r.getHMAC("ARRAYBUFFER")}}},createHash:function(t){if("md5"===t)return{update:function(t){return this.message?this.message+=t:this.message=t,this},digest:function(t){return"hex"===t?se()(this.message):"base64"===t||"rawbase64"===t?(window.btoa?window.btoa:ie.encode)(se()(this.message,!1,!0)):se()(this.message,!1,!0)}};var e=new St("sha1"===t?"SHA-1":"sha512"===t?"SHA-512":"SHA-256","TEXT");return{update:function(t){return e.update(t),this},digest:function(t){return"hex"===t?e.getHash("HEX"):"base64"===t?e.getHash("B64"):e.getHash("ARRAYBUFFER")}}}},Tr="e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",Br=["inventory","acl","backtosource","policy","torrent","logging","location","storageinfo","quota","storageclass","storagepolicy","mirrorbacktosource","requestpayment","versions","versioning","versionid","uploads","uploadid","partnumber","website","notification","replication","lifecycle","deletebucket","delete","cors","restore","tagging","append","position","response-content-type","response-content-language","response-expires","response-cache-control","response-content-disposition","response-content-encoding","x-image-process","x-image-save-object","x-image-save-bucket","x-oss-process","encryption","obsworkflowtriggerpolicy","x-workflow-limit","x-workflow-prefix","x-workflow-start","x-workflow-template-name","x-workflow-graph-name","x-workflow-execution-state","x-workflow-category","x-workflow-prefix","x-workflow-create","directcoldaccess","customdomain","cdnnotifyconfiguration","metadata","dispolicy","obscompresspolicy","template_name","template_name_prefix","x-workflow-status","x-workflow-type","x-workflow-forbid","sfsacl","obsbucketalias","obsalias","rename","name","modify","attname","inventory","truncate","object-lock","retention","x-obs-security-token"],Mr=["content-type","content-md5","content-length","content-language","expires","origin","cache-control","content-disposition","content-encoding","x-default-storage-class","location","date","etag","host","last-modified","content-range","x-reserved","access-control-allow-origin","access-control-allow-headers","access-control-max-age","access-control-allow-methods","access-control-expose-headers","connection","x-obs-location-clustergroup-id"],Rr={"content-length":"ContentLength",date:"Date","x-reserved":"Reserved"},_r=["STANDARD","WARM","COLD","DEEP_ARCHIVE","INTELLIGENT_TIERING"],Ir=["STANDARD","STANDARD_IA","GLACIER","DEEP_ARCHIVE","INTELLIGENT_TIERING"],jr=["private","public-read","public-read-write","public-read-delivered","public-read-write-delivered"],Dr=["private","public-read","public-read-write","authenticated-read","bucket-owner-read","bucket-owner-full-control","log-delivery-write"],Lr=["Everyone","LogDelivery"],qr=["http://acs.amazonaws.com/groups/global/AllUsers","http://acs.amazonaws.com/groups/global/AuthenticatedUsers","http://acs.amazonaws.com/groups/s3/LogDelivery"],Fr=["ObjectCreated","ObjectCreated:*","ObjectCreated:Put","ObjectCreated:Post","ObjectCreated:Copy","ObjectCreated:CompleteMultipartUpload","ObjectRemoved","ObjectRemoved:*","ObjectRemoved:Delete","ObjectRemoved:DeleteMarkerCreated","ObjectChanged:*","ObjectChanged:Rename","ObjectChanged:Truncate","ObjectChanged:Modify"],Nr=["ObjectCreated","s3:ObjectCreated:*","s3:ObjectCreated:Put","s3:ObjectCreated:Post","s3:ObjectCreated:Copy","s3:ObjectCreated:CompleteMultipartUpload","ObjectRemoved","s3:ObjectRemoved:*","s3:ObjectRemoved:Delete","s3:ObjectRemoved:DeleteMarkerCreated","ObjectRemoved:*","ObjectRemoved:Delete","ObjectRemoved:DeleteMarkerCreated","ObjectChanged:*","ObjectChanged:Rename","ObjectChanged:Truncate","ObjectChanged:Modify"],Ur=["CreateBucket","SetBucketAlias","BindBucketAlias","UnbindBucketAlias","DeleteBucketAlias","GetBucketAlias"],Gr="HeadApiVersion",Hr={signature:"obs",headerPrefix:"x-obs-",headerMetaPrefix:"x-obs-meta-",authPrefix:"OBS"},zr={signature:"v2",headerPrefix:"x-amz-",headerMetaPrefix:"x-amz-meta-",authPrefix:"AWS"};function Kr(t,e,r){if(0===(t=String(t)).length)return"";if(r)return t;var o;if(e){o=[];var n,i=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return Or(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Or(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var o=0,n=function(){};return{s:n,n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){s=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}(t);try{for(i.s();!(n=i.n()).done;){var a=n.value;o.push(e.indexOf(a)>=0?a:encodeURIComponent(a))}}catch(t){i.e(t)}finally{i.f()}o=o.join("")}else o=encodeURIComponent(t);return o.replace(/!/g,"%21").replace(/\*/g,"%2A").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29")}function Wr(t){return JSON?JSON.stringify(t):""}function Vr(t,e){var r={};for(var o in e)if({}.hasOwnProperty.call(e,o)){var n=String(o).toLowerCase();0===n.indexOf(t)&&(r[n.slice(t.length)]=e[o])}return r}function Qr(t){return"[object Function]"===Object.prototype.toString.call(t)}function Xr(t){return"[object Object]"===Object.prototype.toString.call(t)}function $r(t,e){if("object"===Cr(t))return e(null,t);try{return e(null,ce.parseString(t))}catch(t){return e(t,null)}}function Yr(t){var e=new Date(Date.parse(t)),r=e.getUTCHours(),o=e.getUTCMinutes(),n=e.getUTCSeconds(),i=e.getUTCDate(),a=e.getUTCMonth()+1,s="";return s+=e.getUTCFullYear()+"-",a<10&&(s+="0"),s+=a+"-",i<10&&(s+="0"),s+=i+"T",r<10&&(s+="0"),s+=r+":",o<10&&(s+="0"),s+=o+":",n<10&&(s+="0"),s+(n+"Z")}function Zr(t){var e=new Date(Date.parse(t)),r=e.getUTCHours(),o=e.getUTCMinutes(),n=e.getUTCSeconds(),i=e.getUTCDate(),a=e.getUTCMonth()+1,s="",c="";return s+=e.getUTCFullYear(),a<10&&(s+="0"),s+=a,i<10&&(s+="0"),c+=(s+=i)+"T",r<10&&(c+="0"),c+=r,o<10&&(c+="0"),c+=o,n<10&&(c+="0"),[s,c+=n+"Z"]}function Jr(t){var e=[],r={};for(var o in t)({}).hasOwnProperty.call(t,o)&&(e.push(o.toLowerCase()),r[o.toLowerCase()]=t[o]);e=e.sort();for(var n="",i="",a=0;a<e.length;a++)0!==a&&(n+=";"),n+=e[a],i+=e[a]+":"+r[e[a]]+"\n";return[n,i]}function to(t,e,r,o){var n=Er.createHmac("sha256","AWS4"+e).update(t).digest(),i=Er.createHmac("sha256",n).update(r).digest(),a=Er.createHmac("sha256",i).update("s3").digest(),s=Er.createHmac("sha256",a).update("aws4_request").digest();return Er.createHmac("sha256",s).update(o).digest("hex")}function eo(t,e,r,o,n){var i="AWS4-HMAC-SHA256\n";return i+=e+"\n",i+=t+"/"+o+"/s3/aws4_request\n",to(t,r,o,i+=Er.createHash("sha256").update(n).digest("hex"))}function ro(t){this.log=t,this.ak=null,this.sk=null,this.securityToken=null,this.isSecure=!0,this.server=null,this.pathStyle=!1,this.signatureContext=null,this.isSignatureNegotiation=!0,this.bucketSignatureCache={},this.region="region",this.port=null,this.timeout=300,this.obsSdkVersion="3.22.3",this.isCname=!1,this.bucketEventEmitters={},this.useRawXhr=!1}ro.prototype.encodeURIWithSafe=Kr,ro.prototype.mimeTypes={"7z":"application/x-7z-compressed",aac:"audio/x-aac",ai:"application/postscript",aif:"audio/x-aiff",asc:"text/plain",asf:"video/x-ms-asf",atom:"application/atom+xml",avi:"video/x-msvideo",bmp:"image/bmp",bz2:"application/x-bzip2",cer:"application/pkix-cert",crl:"application/pkix-crl",crt:"application/x-x509-ca-cert",css:"text/css",csv:"text/csv",cu:"application/cu-seeme",deb:"application/x-debian-package",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dvi:"application/x-dvi",eot:"application/vnd.ms-fontobject",eps:"application/postscript",epub:"application/epub+zip",etx:"text/x-setext",flac:"audio/flac",flv:"video/x-flv",gif:"image/gif",gz:"application/gzip",htm:"text/html",html:"text/html",ico:"image/x-icon",ics:"text/calendar",ini:"text/plain",iso:"application/x-iso9660-image",jar:"application/java-archive",jpe:"image/jpeg",jpeg:"image/jpeg",jpg:"image/jpeg",js:"text/javascript",json:"application/json",latex:"application/x-latex",log:"text/plain",m4a:"audio/mp4",m4v:"video/mp4",mid:"audio/midi",midi:"audio/midi",mov:"video/quicktime",mp3:"audio/mpeg",mp4:"video/mp4",mp4a:"audio/mp4",mp4v:"video/mp4",mpe:"video/mpeg",mpeg:"video/mpeg",mpg:"video/mpeg",mpg4:"video/mp4",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",pbm:"image/x-portable-bitmap",pdf:"application/pdf",pgm:"image/x-portable-graymap",png:"image/png",pnm:"image/x-portable-anymap",ppm:"image/x-portable-pixmap",ppt:"application/vnd.ms-powerpoint",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",ps:"application/postscript",qt:"video/quicktime",rar:"application/x-rar-compressed",ras:"image/x-cmu-raster",rss:"application/rss+xml",rtf:"application/rtf",sgm:"text/sgml",sgml:"text/sgml",svg:"image/svg+xml",swf:"application/x-shockwave-flash",tar:"application/x-tar",tif:"image/tiff",tiff:"image/tiff",torrent:"application/x-bittorrent",ttf:"application/x-font-ttf",txt:"text/plain",wav:"audio/x-wav",webm:"video/webm",wma:"audio/x-ms-wma",wmv:"video/x-ms-wmv",woff:"application/x-font-woff",wsdl:"application/wsdl+xml",xbm:"image/x-xbitmap",xls:"application/vnd.ms-excel",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xml:"application/xml",xpm:"image/x-xpixmap",xwd:"image/x-xwindowdump",yaml:"text/yaml",yml:"text/yaml",zip:"application/zip"},ro.prototype.refresh=function(t,e,r){this.ak=t?String(t).trim():null,this.sk=e?String(e).trim():null,this.securityToken=r?String(r).trim():null},ro.prototype.initFactory=function(t,e,r,o,n,i,a,s,c,u,l,p,f,d,h,m,y){if(this.refresh(t,e,u),this.urlPrefix=f||"",this.regionDomains=d||null,this.setRequestHeaderHook=h||null,this.checkAlgorithm=Pr.ContentMD5,"string"==typeof y&&"sha256"===y.toLowerCase()&&(this.checkAlgorithm=Pr.ContentSHA256),!o)throw new Error("Server is not set");0===(o=String(o).trim()).indexOf("https://")?(o=o.slice(8),r=!0):0===o.indexOf("http://")&&(o=o.slice(7),r=!1);for(var g=o.lastIndexOf("/");g>=0;)g=(o=o.slice(0,g)).lastIndexOf("/");(g=o.indexOf(":"))>=0&&(s=o.slice(g+1),o=o.slice(0,g)),this.server=o,/^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$/.test(this.server)&&(n=!0),void 0!==r&&(this.isSecure=r),void 0!==n&&(this.pathStyle=n),i=void 0!==i?String(i).trim().toLowerCase():"obs",void 0!==l&&(this.isSignatureNegotiation=l),this.isCname=p,(this.pathStyle||this.isCname)&&(this.isSignatureNegotiation=!1,"obs"===i&&(i="v2")),this.signatureContext="obs"===i?Hr:zr,void 0!==a&&(this.region=String(a)),this.port=s?parseInt(s,10):this.isSecure?443:80,void 0!==c&&(this.timeout=parseInt(c,10)),void 0!==m&&(this.useRawXhr=m)},ro.prototype.SseKmsAdapter=function(t,e){t=t||"";var r=(t=String(t)).indexOf("aws:");return"obs"===e.signature?0===r?t.slice(4):t:0===r?t:"aws:"+t},ro.prototype.SseModeAdapter=function(t,e){t=t||"";var r=(t=String(t)).indexOf("aws:");return"obs"===e.signature?0===r?t.slice(4):t:0===r?t:"aws:"+t},ro.prototype.BucketAdapter=function(t,e){t=t||"";var r=(t=String(t)).indexOf("arn:aws:s3:::");return"obs"===e.signature?0===r?t.slice(13):t:0===r?t:"arn:aws:s3:::"+t},ro.prototype.EventAdapter=function(t,e){return t=t||"",t=String(t),"obs"===e.signature?Fr.indexOf(t)>=0?t:Nr.indexOf(t)>=0?t.substring(3):"":Nr.indexOf(t)>=0?t:Fr.indexOf(t)>=0?"s3:"+t:""},ro.prototype.EventsAdapter=function(t,e){return this.EventAdapter(t,e)},ro.prototype.URIAdapter=function(t,e){return t=t||"",t=String(t),"obs"===e.signature?Lr.indexOf(t)>=0?t:"AllUsers"===t||"http://acs.amazonaws.com/groups/global/AllUsers"===t?"Everyone":"":qr.indexOf(t)>=0?t:"Everyone"===t||"AllUsers"===t?"http://acs.amazonaws.com/groups/global/AllUsers":"AuthenticatedUsers"===t?"http://acs.amazonaws.com/groups/global/AuthenticatedUsers":"LogDelivery"===t?"http://acs.amazonaws.com/groups/s3/LogDelivery":""},ro.prototype.StorageClassAdapter=function(t,e){return t=t||"",t=String(t).toUpperCase(),"obs"===e.signature?_r.indexOf(t)>=0?t:"STANDARD_IA"===t?"WARM":"GLACIER"===t?"COLD":"":Ir.indexOf(t)>=0?t:"WARM"===t?"STANDARD_IA":"COLD"===t?"GLACIER":""},ro.prototype.ACLAdapter=function(t,e){return t=t||"",t=String(t).toLowerCase(),"obs"===e.signature?jr.indexOf(t)>=0?t:"":("public-read-delivered"===t?t="public-read":"public-read-write-delivered"===t&&(t="public-read-write"),Dr.indexOf(t)>=0?t:"")},ro.prototype.doExec=function(t,e,r){var o=this.makeParam(t,e);if("err"in o)return r(o.err,null);this.sendRequest(t,o,r)},ro.prototype.doNegotiation=function(t,e,r,o,n,i){var a=null,s=this;if(n&&e.Bucket){var c=this.bucketSignatureCache[e.Bucket];if(c&&c.signatureContext&&c.expireTime>(new Date).getTime()){e.signatureContext=c.signatureContext;var u=this.makeParam(t,e);return"err"in u?r(u.err,null):(u.signatureContext=c.signatureContext,this.sendRequest(t,u,r))}if((a=this.bucketEventEmitters[e.Bucket])||(a={s:0,n:function(){for(;this.e&&this.e.length>0;)this.e.shift()()}},this.bucketEventEmitters[e.Bucket]=a),a.s)return void a.e.push((function(){s.doNegotiation(t,e,r,o,n,i)}));a.e=[],a.s=1}this.doExec(Gr,o?{Bucket:e.Bucket,hasRegion:e.hasRegion}:{},(function(n,c){if(n)return r(n,null),void(a&&(a.s=0,a.n()));if(o&&404===c.CommonMsg.Status||c.CommonMsg.Status>=500)return r(n,c),void(a&&(a.s=0,a.n()));var u=zr;c.CommonMsg.Status<300&&c.InterfaceResult&&c.InterfaceResult.ApiVersion>="3.0"&&(u=Hr),i&&(s.bucketSignatureCache[e.Bucket]={signatureContext:u,expireTime:(new Date).getTime()+15+60*Math.ceil(5*Math.random())*1e3}),a&&(a.s=0,a.n()),e.signatureContext=u;var l=s.makeParam(t,e);if("err"in l)return r(l.err,null);l.signatureContext=u,s.sendRequest(t,l,r)}))},ro.prototype.exec=function(t,e,r){var o=this;o.isSignatureNegotiation&&t!==Gr?"ListBuckets"===t?o.doNegotiation(t,e,r,!1,!1,!1):Ur.indexOf(t)>-1?o.doNegotiation(t,e,(function(n,i){if(!n&&400===i.CommonMsg.Status&&"Unsupported Authorization Type"===i.CommonMsg.Message&&e.signatureContext&&"v2"===e.signatureContext.signature){e.signatureContext=zr;var a=o.makeParam(t,e);return"err"in a?r(a.err,null):(a.signatureContext=e.signatureContext,void o.sendRequest(t,a,r))}r(n,i)}),!1,!0,!1):o.doNegotiation(t,e,r,!0,!0,!0):o.doExec(t,e,r)},ro.prototype.sliceBlob=function(t,e,r,o){return o=o||t.type,t.mozSlice?t.mozSlice(e,r,o):t.webkitSlice?t.webkitSlice(e,r,o):t.slice(e,r,o)},ro.prototype.toXml=function(t,e,r,o,n){var i="";if(null!==r)return i+this.buildXml(t,e,r,o,n);for(var a in e)if(a in t){var s=e[a].sentAs||a;i+=this.buildXml(t,e[a],a,s,n)}return i},ro.prototype.buildXml=function(t,e,r,o,n){var i="",a=e.type;if("array"===a)for(var s=0;s<t[r].length;s++)if("object"===e.items.type){if(!t[r][s])return i;var c=this.toXml(t[r][s],e.items.parameters,null,null,n);""!==c&&(i+="<"+o+">"+c+"</"+o+">")}else"adapter"===e.items.type?i+="<"+o+">"+String(this[r+"Adapter"](t[r][s],n)).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+o+">":"array"!==e.items.type&&(i+="<"+o+">"+String(t[r][s]).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+o+">");else if("object"===a){if(!t[r])return i;var u=this.toXml(t[r],e.parameters,null,null,n);""!==u&&(i+="<"+o,"data"in e&&("xsiNamespace"in e.data&&(i+=' xmlns:xsi="'+e.data.xsiNamespace+'"'),"xsiType"in e.data&&(i+=' xsi:type="'+t[r][e.data.xsiType]+'"')),i+=">",i+=u+"</"+o+">")}else"adapter"===a?i+="<"+o+">"+String(this[r+"Adapter"](t[r],n)).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+o+">":"ignore"!==a&&(i+="<"+o+">"+String(t[r]).replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;")+"</"+o+">");if(i&&e.wrapper){var l=e.wrapper;i="<"+l+">"+i+"</"+l+">"}return i},ro.prototype.jsonToObject=function(t,e,r,o){var n={};if(null!==r)this.buildObject(t,e,r,n,o);else for(var i in t)({}).hasOwnProperty.call(t,i)&&this.buildObject(t,e,i,n,o);return n},ro.prototype.buildObject=function(t,e,r,o,n){var i=function(e){return void 0===e?"":e&&t[r].decode&&n?decodeURIComponent(e.replace(/\+/g,"%20")):e};if(Xr(e)){var a=!0,s=t[r].wrapper;if(s&&s in e&&(a=Xr(e=e[s])),a){var c=t[r].sentAs||r;if(c in e)if("object"===t[r].type)o[r]=this.jsonToObject(t[r].parameters,e[c],null,n);else if("array"===t[r].type){var u=[];if(function(t){return"[object Array]"===Object.prototype.toString.call(t)}(e[c]))for(var l=0;l<e[c].length;l++)u[l]="object"===t[r].items.type?this.jsonToObject(t[r].items.parameters,e[c][l],null,n):i(e[c][l]["#text"]);else u[0]="object"===t[r].items.type?this.jsonToObject(t[r].items.parameters,e[c],null,n):i(e[c]["#text"]||"");o[r]=u}else o[r]=i(e[c]["#text"])}}void 0===o[r]&&("object"===t[r].type?o[r]=t[r].parameters?this.jsonToObject(t[r].parameters,null,null,n):{}:"array"===t[r].type&&(o[r]=[]))},ro.prototype.makeParam=function(t,e){var r=e.signatureContext||this.signatureContext,o="obs"===r.signature?Ze[t]:Sr[t],n=o.httpMethod,i="/",a="",s="",c={},u={};for(var l in u.$requestParam=e,"urlPath"in o&&(a+="?",a+=o.urlPath),o.parameters)if({}.hasOwnProperty.call(o.parameters,l)){var p=o.parameters[l];if("Bucket"===l&&this.isCname)continue;var f=e[l];if("callback"===p.type&&void 0===f&&p.parameters&&(void 0!==e.CallbackUrl||void 0!==e.CallbackBody)){f={};for(var d=0,h=Object.keys(p.parameters);d<h.length;d++){var m=h[d],y=p.parameters[m],g=e[m];if(y.required&&(null==g||"[object String]"===Object.prototype.toString.call(g)&&""===g))return u.err=m+" is a required element!",this.log.runLog("error",t,u.err),u;f[m.slice(0,1).toLowerCase()+m.slice(1)]=g}}if(p.required&&(null==f||"[object String]"===Object.prototype.toString.call(f)&&""===f))return u.err=l+" is a required element!",this.log.runLog("error",t,u.err),u;if(null!=f){if("srcFile"===p.type||"dstFile"===p.type){u[p.type]=f;continue}"plain"===p.type&&(u[l]=f);var v=p.sentAs||l;if(p.withPrefix&&(v=r.headerPrefix+v),"uri"===p.location)"/"!==i&&(i+="/"),i+=f;else if("header"===p.location){var b=p.encodingSafe||" ;/?:@&=+$,";if("object"===p.type){if(r.headerMetaPrefix===v)for(var A in f)if({}.hasOwnProperty.call(f,A)){var x=f[A];c[0===(A=String(A).trim().toLowerCase()).indexOf(v)?A:v+A]=Kr(x,b)}}else if("array"===p.type){var w=[];for(var S in f)({}).hasOwnProperty.call(f,S)&&(w[S]=Kr(f[S],b));c[v]=w}else if("password"===p.type){var P=window.btoa?window.btoa:ie.encode;c[v]=P(f),c[p.pwdSentAs||v+"-MD5"]=this.rawBufMD5(f)}else if("number"===p.type&&Number(f))c[v]=Kr(String(f),b);else if("boolean"===p.type)c[v]=Kr(f?"true":"false",b);else if("callback"===p.type)c[v]=ie.encode(JSON.stringify(f));else if("adapter"===p.type){var k=this[l+"Adapter"](f,r);k&&(c[v]=Kr(String(k),b))}else c[v]=Kr(String(f),b,p.skipEncoding)}else if("urlPath"===p.location){var C=""===a?"?":"&",O=f;("number"!==p.type||"number"===p.type&&Number(O)>=0)&&(a+=C+Kr(v,"/")+"="+Kr(String(O),"/"))}else if("xml"===p.location){var E=this.toXml(e,p,l,v,r);E&&(s+=E)}else"body"===p.location&&(s=f)}}var T="file"===u.dstFile;if("Content-Type"in c||T||(c["Content-Type"]="binary/octet-stream"),"data"in o&&"xmlRoot"in o.data&&(s||o.data.xmlAllowEmpty)){var B=o.data.xmlRoot;s="<"+B+">"+s+"</"+B+">",c["Content-Type"]="application/xml"}if(T&&(u.rawUri=i),c.Host=this.server+(80===this.port||443===this.port?"":":"+this.port),!this.pathStyle&&!this.isCname){var M=i.split("/");if(M.length>=2&&M[1]){c.Host=M[1]+"."+c.Host;var R=i.replace(M[1],"");0===R.indexOf("//")&&(R=R.slice(1)),"v4"===r.signature?i=R:"/"===R&&(i+="/"),u.requestUri=Kr(R,"/")}}if(u.method=n,u.uri=Kr(i,"/"),u.urlPath=a,s&&(o.data&&o.data.md5&&(this.checkAlgorithm===Pr.ContentSHA256?c["".concat(r.headerPrefix).concat(Pr.ContentSHA256.toLowerCase())]=this.bufSHA256(s,"hex"):c[Pr.ContentMD5]=this.bufMD5(s),c["Content-Length"]=0===s.length?"0":String(s.length)),u.xml=s,this.log.runLog("debug",t,"request content:"+s)),u.headers=c,"srcFile"in u&&(u.srcFile instanceof window.File||u.srcFile instanceof window.Blob)){var _=u.srcFile.size;if("Content-Length"in u.headers||"PartSize"in u||"Offset"in u){var I,j=u.Offset;j=j&&j>=0&&j<_?j:0,I=(I="PartSize"in u?u.PartSize:"Content-Length"in u.headers?parseInt(u.headers["Content-Length"],10):_)&&I>0&&I<=_-j?I:_-j,u.PartSize=I,u.Offset=j,u.headers["Content-Length"]=String(u.PartSize)}}return u},ro.prototype.parseCommonHeaders=function(t,e,r){for(var o in Rr)({}).hasOwnProperty.call(Rr,o)&&(t.InterfaceResult[Rr[o]]=e[o]);t.InterfaceResult.RequestId=e[r.headerPrefix+"request-id"],t.InterfaceResult.Id2=e[r.headerPrefix+"id-2"],t.CommonMsg.RequestId=t.InterfaceResult.RequestId,t.CommonMsg.Id2=t.InterfaceResult.Id2},ro.prototype.contrustCommonMsg=function(t,e,r,o){for(var n in t.InterfaceResult={},this.parseCommonHeaders(t,r,o),e)if("header"===e[n].location){var i=e[n].sentAs||n;if(e[n].withPrefix&&(i=o.headerPrefix+i),"object"===e[n].type)t.InterfaceResult[n]=Vr(i,r);else{var a=null;i in r?a=r[i]:i.toLowerCase()in r&&(a=r[i.toLowerCase()]),null!==a&&(t.InterfaceResult[n]=a)}}},ro.prototype.getRequest=function(t,e,r,o,n,i){var a=this.regionDomains,s={},c=this.log,u="obs"===r.signature?Ze[t+"Output"]:Sr[t+"Output"],l=(u=u||{}).parameters||{};s.CommonMsg={Status:e.status,Code:"",Message:"",HostId:"",RequestId:"",InterfaceResult:null};var p=e.headers,f=Wr(p);c.runLog("info",t,"get response start, statusCode:"+e.status),c.runLog("debug",t,"response msg :statusCode:"+e.status+", headers:"+f);var d=function(){var e="Status:"+s.CommonMsg.Status+", Code:"+s.CommonMsg.Code+", Message:"+s.CommonMsg.Message;c.runLog("debug",t,"exec interface "+t+" finish, "+e),i(null,s)};if(e.status>=300&&e.status<400&&304!==e.status&&o<=5){var h=p.location||p.Location;if(h){var m="http code is 3xx, need to redirect to "+h;c.runLog("warn",t,m);var y=new Error("redirect");return y.location=h,y.bucketLocation=p["x-amz-bucket-region"]||p["x-obs-bucket-region"],i(y)}var g=p["x-amz-bucket-region"]||p["x-obs-bucket-location"];if(g&&a[g]){var v=(this.isSecure?"https://":"http://")+a[g];Qr(this.setRequestHeaderHook)&&this.setRequestHeaderHook(p,n,t,a[g]);var b="get redirect code 3xx, need to redirect to"+v;c.runLog("error",t,b);var A=new Error("redirect");return A.location=v,i(A)}c.runLog("error",t,"get redirect code 3xx, but no location in headers")}if(e.status<300){var x=e.data;this.contrustCommonMsg(s,l,p,r);var w="Status: "+s.CommonMsg.Status+", headers: "+f;if(x&&(w+="body length: "+x.length,c.runLog("debug",t,"response body length:"+x.length)),c.runLog("debug",t,w),x&&"data"in u){if(n.CallbackUrl&&u.CallbackResponse)return s.InterfaceResult[u.CallbackResponse.sentAs]=x,void d();if("xml"===u.data.type){var S=this;return $r(x,(function(e,r){if(e)return c.runLog("error",t,"change xml to json err ["+Wr(e)+"]"),i(e,null);var o=r;u.data.xmlRoot&&u.data.xmlRoot in o&&(o=r[u.data.xmlRoot]);var n=!!o.EncodingType;if(Xr(o))for(var a in l)"xml"===l[a].location&&(s.InterfaceResult[a]=S.jsonToObject(l,o,a,n)[a]);d()}))}if("body"===u.data.type)for(var P in l)if("body"===l[P].location){s.InterfaceResult[P]=x;break}}return d()}var k=e.data,C="Status: "+s.CommonMsg.Status+", headers: "+f;return""!==k&&(C+="body: "+k,c.runLog("debug",t,"response body :"+k)),s.CommonMsg.RequestId=p[r.headerPrefix+"request-id"],s.CommonMsg.Id2=p[r.headerPrefix+"id2"],s.CommonMsg.Indicator=p["x-reserved-indicator"],c.runLog("info",t,"request finished with request id:"+s.CommonMsg.RequestId),c.runLog("debug",t,C),k?$r(k,(function(e,r){if(e)c.runLog("error",t,"change xml to json err ["+Wr(e)+"]"),s.CommonMsg.Message=e.message;else if(r){if("Error"in r){var o=r.Error;for(var n in o)o[n]&&o[n]["#text"]&&(s.CommonMsg[n]=o[n]["#text"])}else{var i=r;"code"in i&&(s.CommonMsg.Code=i.code),"message"in i&&(s.CommonMsg.Message=i.message),"hostId"in i&&(s.CommonMsg.HostId=i.hostId),"request_id"in i&&i.request_id&&(s.CommonMsg.RequestId=i.request_id)}c.runLog("error",t,"request error with error code:"+s.CommonMsg.Code+", error message:"+s.CommonMsg.Message+", request id:"+s.CommonMsg.RequestId)}d()})):d()},ro.prototype.makeRequest=function(t,e,r,n){var i=this.log,a=e.xml||null,s=e.signatureContext||this.signatureContext;if(delete e.headers.Authorization,"file"===e.dstFile){var c={};if(e.urlPath)for(var u=e.urlPath.slice(1).split("&"),l=0;l<u.length;l++)if(-1===u[l].indexOf("="))c[u[l]]="";else{var p=u[l].split("=");c[p[0]]=p[1]}var f=e.rawUri.split("/")[1],d=e.rawUri.slice(("/"+f+"/").length);this.isCname&&(d=e.rawUri.slice(1),f="");var h={CommonMsg:{Status:0,Code:"",Message:"",HostId:""},InterfaceResult:{}},m=("obs"===s.signature?Ze[t+"Output"]:Sr[t+"Output"]).parameters;for(var y in m)if("body"===m[y].location){h.InterfaceResult[y]=this.createSignedUrlSync({Method:e.method,Bucket:f,Key:d,Expires:3600,Headers:e.headers,QueryParams:c,signatureContext:s});break}return n(null,h)}var g,v=e.$requestParam.RequestDate,b=Object.prototype.toString.call(v);if("[object Date]"===b)g=v;else if("[object String]"===b)try{(g=new Date).setTime(Date.parse(v))}catch(t){}g||(g=new Date);var A=g.toUTCString(),x="v4"===s.signature.toLowerCase();e.headers[s.headerPrefix+"date"]=x?Zr(A)[1]:A;var w=(e.requestUri?e.requestUri:e.uri)+e.urlPath;this.ak&&this.sk&&t!==Gr&&(this.securityToken&&(e.headers[s.headerPrefix+"security-token"]=this.securityToken),x?this.v4Auth(e,t,s):this.doAuth(e,t,s));var S=e.headers;Qr(this.setRequestHeaderHook)&&this.setRequestHeaderHook(S,e.$requestParam,t);var P=S.Host,k=e.method,C={};for(var O in S)({}).hasOwnProperty.call(S,O)&&(C[O]=S[O]);C.Authorization="****";var E="method:"+k+", path:"+w+"headers:"+Wr(C);a&&(E+="body:"+a),i.runLog("info",t,"prepare request parameters ok,then Send request to service start"),i.runLog("debug",t,"request msg:"+E);var T=e.protocol?0===e.protocol.toLowerCase().indexOf("https"):this.isSecure,B=e.port||this.port;delete S.Host,delete S["Content-Length"];var M="text";!e.dstFile||"file"===e.dstFile||"arraybuffer"!==e.dstFile&&"blob"!==e.dstFile||(M=String(e.dstFile));var R=g.getTime(),_=this,I=function(e){try{var r=Wr(e);i.runLog("error",t,"Send request to service error ["+r+"]")}catch(r){e.toString&&i.runLog("error",t,"Send request to service error ["+e.toString()+"]")}i.runLog("info",t,"http cost "+((new Date).getTime()-R)+" ms"),n(e,null)};if(this.useRawXhr){var j=null;try{j=new XMLHttpRequest}catch(t){try{j=new ActiveXObject("Msxml2.XMLHTTP")}catch(t){try{j=new ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}}if(null===j)return n(new Error("XHR is not available"),null);if(e.srcFile){if(!(e.srcFile instanceof window.File||e.srcFile instanceof window.Blob))return n(new Error("source file must be an instance of window.File or window.Blob"),null);try{var D=e.srcFile;if(e.Offset>=0&&e.PartSize>0)D=this.sliceBlob(D,e.Offset,e.Offset+e.PartSize);else if("ContentLength"in e){var L=parseInt(e.ContentLength,10);L>0&&(D=this.sliceBlob(D,0,L))}a=D}catch(t){return n(t)}}for(var q in j.open(k,(T?"https://"+this.urlPrefix+P:"http://"+this.urlPrefix+P)+w),j.withCredentials=!1,S)({}).hasOwnProperty.call(S,q)&&j.setRequestHeader(q,S[q]);j.timeout=1e3*_.timeout,j.responseType=M,e.$requestParam.cancelHook=function(){j.abort()},j.onreadystatechange=function(){if(4===j.readyState&&j.status>=200){i.runLog("info",t,"http cost "+((new Date).getTime()-R)+" ms");for(var o=j.getAllResponseHeaders().trim().split(/[\r\n]+/),a={},c=0;c<o.length;c++){var u=o[c].split(": "),l=u.shift(),p=u.join(": ");a[l.toLowerCase()]=p}var f=j.response;f||""!==M&&"text"!==M||(f=j.responseText);var d={status:j.status,headers:a,data:f};_.getRequest(t,d,s,r,e.$requestParam,n)}};var F=!1,N=function(t){F||(F=!0,I(t))};j.ontimeout=function(){N(new Error("timeout of "+j.timeout+"ms exceed"))},j.onerror=function(){N(new Error("Network Error"))},j.onabort=function(){N(new Error("Cancel"))},j.upload&&(j.upload.ontimeout=function(){N(new Error("timeout of "+j.timeout+"ms exceed"))},j.upload.onerror=function(){N(new Error("Network Error"))},j.upload.onabort=function(t){N(new Error("Cancel"))}),Qr(e.ProgressCallback)&&("GET"!==k&&j.upload?"PUT"!==k&&"POST"!==k||(j.upload.onprogress=function(t){t.lengthComputable&&e.ProgressCallback(t.loaded,t.total,((new Date).getTime()-R)/1e3)}):j.onprogress=function(t){t.lengthComputable&&e.ProgressCallback(t.loaded,t.total,((new Date).getTime()-R)/1e3)}),j.send(a)}else{var U=null,G=null;if(Qr(e.ProgressCallback)){var H=function(t){t.lengthComputable&&e.ProgressCallback(t.loaded,t.total,((new Date).getTime()-R)/1e3)};"GET"===k?G=H:"PUT"!==k&&"POST"!==k||(U=H)}var z=":"+B;P.indexOf(":")>=0&&(z="");var K="",W=T?"https://":"http://";if(this.urlPrefix&&Qr(this.setRequestHeaderHook)&&"UploadPart"!==t){var V=!0;(e.$requestParam.hasRegion||e.$requestParam.redirectRegion)&&(V=!1);var Q="";5443===B&&(Q="-5443"),V?e.$requestParam.Bucket?(-1!==e.$requestParam.Bucket.indexOf(".")&&(K=W+this.urlPrefix+"/bucket"+Q),K=W+this.urlPrefix+"/bucket"+Q):K="/"===w.split("?")[0]?W+this.urlPrefix+Q:W+this.urlPrefix+"/place"+Q:K=e.$requestParam.Bucket?W+this.urlPrefix+"/region-bucket"+Q:W+this.urlPrefix+"/region"+Q}else K=W+P+z;var X={method:k,url:K+w,withCredentials:!1,headers:S,validateStatus:function(t){return t>=200},maxRedirects:0,responseType:M,data:a,timeout:1e3*this.timeout,onUploadProgress:U,onDownloadProgress:G,cancelToken:new(o().CancelToken)((function(t){e.$requestParam.cancelHook=t}))};if(e.srcFile){if(!(e.srcFile instanceof window.File||e.srcFile instanceof window.Blob))return n(new Error("source file must be an instance of window.File or window.Blob"),null);var $=e.srcFile;try{if(e.Offset>=0&&e.PartSize>0)$=this.sliceBlob($,e.Offset,e.Offset+e.PartSize);else if("ContentLength"in e){var Y=parseInt(e.ContentLength,10);Y>0&&($=this.sliceBlob($,0,Y))}}catch(t){return n(t)}X.data=$}o().request(X).then((function(o){i.runLog("info",t,"http cost "+((new Date).getTime()-R)+" ms"),_.getRequest(t,o,s,r,e.$requestParam,n)})).catch((function(t){I(t)}))}},ro.prototype.sendRequest=function(t,r,o,n){void 0===n&&(n=1);var i=!1;n<=r.$requestParam.maxPartRetryCount&&(i=!0);var a=r.headers.Host,s=this;s.makeRequest(t,r,n,(function(c,u){if(c&&"redirect"===c.message){var l=function(t){var r=e().parse(t);return{hostname:r.hostname,port:r.port,host:r.hostname,protocol:r.protocol?r.protocol+":":"",query:r.query,path:r.path+(r.query?"?"+r.query:""),pathname:r.path,search:r.query?"?"+r.query:""}}(c.location);c.bucketLocation&&-1!==l.hostname.indexOf(r.$requestParam.Bucket)&&(r.$requestParam.redirectRegion=c.bucketLocation),r.headers.Host=l.hostname,r.protocol=l.protocol,r.port=l.port||(r.protocol&&0===r.protocol.toLowerCase().indexOf("https")?443:80),s.sendRequest(t,r,o,n+1)}else"UploadPart"===t&&i&&(c||u.CommonMsg.Status>300)?(r.headers.Host=a,s.sendRequest(t,r,o,n+1)):o(c,u)}))},ro.prototype.doAuth=function(t,e,r){for(var o=["Content-MD5","Content-Type"],n=t.method+"\n",i=0;i<o.length;i++)o[i]in t.headers&&(n+=t.headers[o[i]]),n+="\n";r.headerPrefix+"date"in t.headers||(n+=t.headers.Date),n+="\n";var a=[];for(var s in t.headers)if({}.hasOwnProperty.call(t.headers,s)){var c=s.toLowerCase();0===c.indexOf(r.headerPrefix)&&a.push({key:c,value:t.headers[s]})}a=a.sort((function(t,e){return t.key<e.key?-1:t.key>e.key?1:0}));for(var u=0;u<a.length;u++){var l=a[u].key,p=0===l.indexOf(r.headerMetaPrefix)?a[u].value.trim():a[u].value;n+=l+":"+p+"\n"}var f=t.uri;if(this.isCname&&("/"===f?f+=t.headers.Host+"/":0===f.indexOf("/")&&(f="/"+t.headers.Host+f)),t.urlPath){for(var d=t.urlPath.slice(1).split("&").sort(),h="",m=0;m<d.length;m++){var y=d[m].split("="),g=decodeURIComponent(y[0]);Br.indexOf(g.toLowerCase())>=0&&(h+=""===h?"?":"&",h+=g,2===y.length&&y[1]&&(h+="="+decodeURIComponent(y[1])))}f+=h}n+=f,this.log.runLog("debug",e,"stringToSign:"+n),t.headers.Authorization=r.authPrefix+" "+this.ak+":"+Er.createHmac("sha1",this.sk).update(n).digest("base64")},ro.prototype.v4Auth=function(t,e,r){t.headers[r.headerPrefix+"content-sha256"]=Tr;var o=t.headers,n=this.log,i=null,a=null;if(r.headerPrefix+"date"in o)i=(a=o[r.headerPrefix+"date"]).slice(0,a.indexOf("T"));else{var s=Zr(o.Date);i=s[0],a=s[1]}var c=this.ak+"/"+i+"/"+this.region+"/s3/aws4_request",u=Jr(o),l=u[0],p=u[1],f="";if(t.urlPath){var d=t.urlPath.slice(1).split("&");d=d.sort();for(var h=0;h<d.length;h++)f+=d[h],-1===d[h].indexOf("=")&&(f+="="),h!==d.length-1&&(f+="&")}var m=t.method+"\n";m+=t.uri+"\n",m+=f+"\n",m+=p+"\n",m+=l+"\n",m+=Tr,n.runLog("debug",e,"canonicalRequest:"+m);var y=eo(i,a,this.sk,this.region,m);t.headers.Authorization="AWS4-HMAC-SHA256 Credential="+c+",SignedHeaders="+l+",Signature="+y},ro.prototype.bufMD5=function(t){return Er.createHash("md5").update(t).digest("base64")},ro.prototype.bufSHA256=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"base64";return Er.createHash("sha256").update(t).digest(e)},ro.prototype.rawBufMD5=function(t){return Er.createHash("md5").update(t).digest("rawbase64")},ro.prototype.createSignedUrl=function(t){return"v4"===(t.signatureContext||this.signatureContext).signature.toLowerCase()?this.createV4SignedUrlSync(t):this.createV2SignedUrl(t)},ro.prototype.createSignedUrlSync=function(t){return"v4"===(t.signatureContext||this.signatureContext).signature.toLowerCase()?this.createV4SignedUrlSync(t):this.createV2SignedUrlSync(t)},ro.prototype.getStringToSign=function(t,e){var r=t.isShareFolder,o=t.queryParams,n=t.queryParamsKeys,i=e.signatureContext||this.signatureContext,a=e.Method?String(e.Method):"GET",s=e.Bucket?String(e.Bucket):null,c=e.Key?String(e.Key):null,u=e.Policy?String(e.Policy):null,l=e.Expires?parseInt(e.Expires,10):300;l<0&&(l=300),l=parseInt((new Date).getTime()/1e3,10)+l;var p={};if(e.Headers&&e.Headers instanceof Object&&!(e.Headers instanceof Array))for(var f in e.Headers)({}).hasOwnProperty.call(e.Headers,f)&&(p[f]=e.Headers[f]);var d={};for(var h in p)if({}.hasOwnProperty.call(p,h)){var m=String(h).toLowerCase();("content-type"===m||"content-md5"===m||m.length>i.headerPrefix.length&&m.slice(0,i.headerPrefix.length)===i.headerPrefix)&&(d[m]=p[h])}var y="",g=this.server;this.isCname?y+="/"+g+"/":s&&(y+="/"+s,this.pathStyle||(g=s+"."+g,y+="/")),c&&(y.lastIndexOf("/")!==y.length-1&&(y+="/"),y+=c=Kr(c,"/")),""===y&&(y="/"),n.sort();for(var v=!1,b=[],A=r?"":"/",x=0;x<n.length;x++){var w=n[x],S=o[w];if(w=Kr(w,A),S=Kr(S,A),(!r||"policy"!==w.toLowerCase())&&(Br.indexOf(w.toLowerCase())>=0||0===w.toLowerCase().indexOf(i.headerPrefix))){v=!0;var P=S?w+"="+decodeURIComponent(S):w;b.push(P)}}b=b.join("&"),v&&(b="?"+b),y+=b;var k=[a];if(k.push("\n"),"content-md5"in d&&k.push(d["content-md5"]),k.push("\n"),"content-type"in d&&k.push(d["content-type"]),k.push("\n"),r?k.push(u):k.push(String(l)),k.push("\n"),r)k.push(b);else{var C=[],O=0;for(var E in d)E.length>i.headerPrefix.length&&E.slice(0,i.headerPrefix.length)===i.headerPrefix&&(C[O++]=E);C=C.sort();for(var T=0;T<C.length;T++)k.push(C[T]),k.push(":"),k.push(d[C[T]]),k.push("\n");k.push(y)}return{stringToSign:k.join(""),headers:p,host:g}},ro.prototype.getQueryParams=function(t){var e=t.Policy?String(t.Policy):null,r=t.Prefix?String(t.Prefix):null,o=t.Expires?parseInt(t.Expires,10):300,n={};if(t.QueryParams&&t.QueryParams instanceof Object&&!(t.QueryParams instanceof Array))for(var i=0,a=Object.keys(t.QueryParams);i<a.length;i++){var s=a[i];n[s]=t.QueryParams[s]}var c=t.SpecialParam?String(t.SpecialParam):null,u=t.signatureContext||this.signatureContext,l=function(t,e){if(["storageClass","storagePolicy"].includes(t))return"obs"===e?"storageClass":"v2"===e?"storagePolicy":void 0}(c,u.signature.toLowerCase());l&&(c=l),c&&(n[c]=""),t.AfterStringToSign?n[u.headerPrefix+"security-token"]="${hws:security-token}":this.securityToken&&!n[u.headerPrefix+"security-token"]&&(n[u.headerPrefix+"security-token"]=this.securityToken),o<0&&(o=300),o=parseInt((new Date).getTime()/1e3,10)+o;var p=e&&r;p?(n.Policy=e,n.prefix=r):n.Expires=String(o);var f=[];return Object.keys(n).forEach((function(t){f.push(t)})),f.sort(),{isShareFolder:p,queryParams:n,queryParamsKeys:f}},ro.prototype.getSignResult=function(t,e,r){var o=t.bucketName,n=t.objectKey,i=t.signatureContext,a=t.isShareFolder,s=t.queryParams,c=t.queryParamsKeys;r&&(s[i.headerPrefix+"security-token"]=r),"v2"===i.signature.toLowerCase()?(s.AWSAccessKeyId=e,c.push("AWSAccessKeyId")):(s.AccessKeyId=e,c.push("AccessKeyId"));var u="";o&&this.pathStyle&&(u+="/"+o),n&&(u+="/"+(n=Kr(n,"/"))),u+="?",c.sort();for(var l=a?"":"/",p=0;p<c.length;p++){var f=c[p],d=s[f];u+=f=Kr(f,l),(d=Kr(d,l))&&(u+="="+d),u+="&"}return u},ro.prototype.createV2SignedUrl=function(t){var e=this,r=this.isSecure,o=this.port,n=this.getQueryParams(t),i=n.isShareFolder,a=n.queryParams,s=n.queryParamsKeys,c=this.getStringToSign({isShareFolder:i,queryParams:a,queryParamsKeys:s},t),u=c.stringToSign,l=c.headers,p=c.host,f={bucketName:t.Bucket?String(t.Bucket):null,objectKey:t.Key?String(t.Key):null,signatureContext:t.signatureContext||this.signatureContext,isShareFolder:i,queryParams:a,queryParamsKeys:s};if(Qr(t.AfterStringToSign))return Promise.resolve(t.AfterStringToSign(u)).then((function(t){var n=t.signature,a=t.ak,s=t.stsToken;return function(t,e){return e+=i?"Signature="+Kr(t):"Signature="+Kr(t,"/"),{ActualSignedRequestHeaders:l,SignedUrl:(r?"https":"http")+"://"+p+":"+o+e}}(n,e.getSignResult(f,a,s))}))},ro.prototype.createV2SignedUrlSync=function(t){var e=this.getQueryParams(t),r=e.isShareFolder,o=e.queryParams,n=e.queryParamsKeys,i=this.getStringToSign({isShareFolder:r,queryParams:o,queryParamsKeys:n},t),a=i.stringToSign,s=i.headers,c=i.host,u={bucketName:t.Bucket?String(t.Bucket):null,objectKey:t.Key?String(t.Key):null,signatureContext:t.signatureContext||this.signatureContext,isShareFolder:r,queryParams:o,queryParamsKeys:n},l=this.getSignResult(u,this.ak,this.securityToken),p=Er.createHmac("sha1",this.sk);return p.update(a),l+=r?"Signature="+Kr(p.digest("base64")):"Signature="+Kr(p.digest("base64"),"/"),{ActualSignedRequestHeaders:s,SignedUrl:(this.isSecure?"https":"http")+"://"+c+":"+this.port+l}},ro.prototype.createV4SignedUrlSync=function(t){var e=(t=t||{}).signatureContext||this.signatureContext,r=t.Method?String(t.Method):"GET",o=t.Bucket?String(t.Bucket):null,n=t.Key?String(t.Key):null,i=t.SpecialParam?String(t.SpecialParam):null;"storageClass"===i&&(i="storagePolicy");var a=t.Expires?parseInt(t.Expires,10):300,s={};if(t.Headers&&t.Headers instanceof Object&&!(t.Headers instanceof Array))for(var c in t.Headers)({}).hasOwnProperty.call(t.Headers,c)&&(s[c]=t.Headers[c]);var u={};if(t.QueryParams&&t.QueryParams instanceof Object&&!(t.QueryParams instanceof Array))for(var l in t.QueryParams)({}).hasOwnProperty.call(t.QueryParams,l)&&(u[l]=t.QueryParams[l]);this.securityToken&&!u[e.headerPrefix+"security-token"]&&(u[e.headerPrefix+"security-token"]=this.securityToken);var p="",f="",d=this.server;o&&(this.pathStyle?(p+="/"+o,f+="/"+o):d=o+"."+d),n&&(p+="/"+(n=Kr(n,"/")),f+="/"+n),""===f&&(f="/"),p+="?",i&&(u[i]=""),a<0&&(a=300);var h=Zr(s.date||s.Date||(new Date).toUTCString()),m=h[0],y=h[1];s.Host=d+(80===this.port||443===this.port?"":":"+this.port),u["X-Amz-Algorithm"]="AWS4-HMAC-SHA256",u["X-Amz-Credential"]=this.ak+"/"+m+"/"+this.region+"/s3/aws4_request",u["X-Amz-Date"]=y,u["X-Amz-Expires"]=String(a);var g=Jr(s);u["X-Amz-SignedHeaders"]=g[0];var v={},b=[];for(var A in u)if({}.hasOwnProperty.call(u,A)){var x=u[A];A=Kr(A,"/"),x=Kr(x),v[A]=x,b.push(A),p+=A,x&&(p+="="+x),p+="&"}var w="";b.sort();for(var S=0;S<b.length;)w+=b[S]+"="+v[b[S]],++S!==b.length&&(w+="&");var P=r+"\n";return P+=f+"\n",P+=w+"\n",P+=g[1]+"\n",P+=g[0]+"\n",P+="UNSIGNED-PAYLOAD",p+="X-Amz-Signature="+Kr(eo(m,y,this.sk,this.region,P)),{ActualSignedRequestHeaders:s,SignedUrl:(this.isSecure?"https":"http")+"://"+d+":"+this.port+p}},ro.prototype.createPostSignatureSync=function(t){var e=t.signatureContext||this.signatureContext;if("v4"===e.signature)return this.createV4PostSignatureSync(t);var r=(t=t||{}).Bucket?String(t.Bucket):null,o=t.Key?String(t.Key):null,n=t.Expires?parseInt(t.Expires,10):300,i={};if(t.FormParams&&t.FormParams instanceof Object&&!(t.FormParams instanceof Array))for(var a in t.FormParams)({}).hasOwnProperty.call(t.FormParams,a)&&(i[a]=t.FormParams[a]);this.securityToken&&!i[e.headerPrefix+"security-token"]&&(i[e.headerPrefix+"security-token"]=this.securityToken);var s=new Date;s.setTime(parseInt((new Date).getTime(),10)+1e3*n),s=Yr(s.toUTCString()),r&&(i.bucket=r),o&&(i.key=o);var c=[];c.push('{"expiration":"'),c.push(s),c.push('", "conditions":[');var u=!0,l=!0,p=["acl","bucket","key","success_action_redirect","redirect","success_action_status"];for(var f in i)if(f){var d=i[f];"bucket"===(f=String(f).toLowerCase())?u=!1:"key"===f&&(l=!1),Mr.indexOf(f)<0&&p.indexOf(f)<0&&0!==f.indexOf(e.headerPrefix)||(c.push('{"'),c.push(f),c.push('":"'),c.push(null!==d?String(d):""),c.push('"},'))}u&&c.push('["starts-with", "$bucket", ""],'),l&&c.push('["starts-with", "$key", ""],'),c.push("]}");var h=c.join("");c=window.btoa?window.btoa(h):ie.encode(h);var m=Er.createHmac("sha1",this.sk).update(c).digest("base64");return{OriginPolicy:h,Policy:c,Signature:m,Token:this.ak+":"+m+":"+c}},ro.prototype.createV4PostSignatureSync=function(t){var e=(t=t||{}).signatureContext||this.signatureContext,r=t.Bucket?String(t.Bucket):null,o=t.Key?String(t.Key):null,n=t.Expires?parseInt(t.Expires,10):300,i={};if(t.FormParams&&t.FormParams instanceof Object&&!(t.FormParams instanceof Array))for(var a in t.FormParams)({}).hasOwnProperty.call(t.FormParams,a)&&(i[a]=t.FormParams[a]);this.securityToken&&!i[e.headerPrefix+"security-token"]&&(i[e.headerPrefix+"security-token"]=this.securityToken);var s=Zr((new Date).toUTCString()),c=s[0],u=s[1],l=this.ak+"/"+c+"/"+this.region+"/s3/aws4_request",p=new Date;p.setTime(parseInt((new Date).getTime(),10)+1e3*n),p=Yr(p.toUTCString()),i["X-Amz-Algorithm"]="AWS4-HMAC-SHA256",i["X-Amz-Date"]=u,i["X-Amz-Credential"]=l,r&&(i.bucket=r),o&&(i.key=o);var f=[];f.push('{"expiration":"'),f.push(p),f.push('", "conditions":[');var d=!0,h=!0,m=["acl","bucket","key","success_action_redirect","redirect","success_action_status"];for(var y in i)if(y){var g=i[y];"bucket"===(y=String(y).toLowerCase())?d=!1:"key"===y&&(h=!1),Mr.indexOf(y)<0&&m.indexOf(y)<0&&0!==y.indexOf(e.headerPrefix)||(f.push('{"'),f.push(y),f.push('":"'),f.push(null!==g?String(g):""),f.push('"},'))}d&&f.push('["starts-with", "$bucket", ""],'),h&&f.push('["starts-with", "$key", ""],'),f.push("]}");var v=f.join("");f=window.btoa?window.btoa(v):ie.encode(v);var b=to(c,this.sk,this.region,f);return{OriginPolicy:v,Policy:f,Algorithm:i["X-Amz-Algorithm"],Credential:i["X-Amz-Credential"],Date:i["X-Amz-Date"],Signature:b}};var oo=ro,no=Number.MAX_VALUE;function io(){this.consoleLog=window.console,this._level=no}io.prototype.setLevel=function(t){t&&(t="info"===(t=String(t).toLowerCase())?20:"warn"===t?30:"error"===t?40:"debug"===t?10:no,this._level=t)},io.prototype.runLog=function(t,e,r){if(t){var o=[(new Date).toLocaleString(),t.toLowerCase(),e,r].join("|");"debug"===t.toLowerCase()&&this._level<=10?this.consoleLog.debug(o):"info"===t.toLowerCase()&&this._level<=20?this.consoleLog.info(o):"warn"===t.toLowerCase()&&this._level<=30?this.consoleLog.warn(o):"error"===t.toLowerCase()&&this._level<=40&&this.consoleLog.error(o)}};var ao=io,so=function(t){return"[object String]"===Object.prototype.toString.call(t)&&t.lastIndexOf("/")!==t.length-1&&(t+="/"),t},co={extend:function(t){t.prototype.dropFile=function(t,e){this.deleteObject(t,e)},t.prototype.dropFolder=function(t,e){var r=this;t=t||{};var o=function(t,e,r){t=t||function(){};var o=(new Date).getTime();return function(r,n){return e.runLog("info","dropFolder","ObsClient cost "+((new Date).getTime()-o)+" ms"),"[object String]"===Object.prototype.toString.call(r)?t(new Error(r),n):t(r,n)}}(e,r.log),n=function(t){return t=t||function(){},function(e,r,o){return"[object Error]"===Object.prototype.toString.call(o)?t(e,r,o):"[object String]"===Object.prototype.toString.call(o)?t(e,r,new Error(o)):o?o.CommonMsg.Status>300?t(e,r,new Error("status:"+o.CommonMsg.Status+", code:"+o.CommonMsg.Code+", message:"+o.CommonMsg.Message)):void t(e,r,o):void 0}}(t.EventCallback),i=t.TaskNum||1,a=0,s=[],c=function(t,e,o,c){if(c)return e.finished++,void o(e);var u=function(){a++,r.dropFile({Bucket:e.bucket,Key:t},(function(r,c){a--,e.finished++,function(){for(;a<i&&s.length>0;)s.shift()()}(),r?(n("dropFileFailed",t,r),e.subDeleted=!1):c.CommonMsg.Status>=300?(n("dropFileFailed",t,c),e.subDeleted=!1):n("dropFileSucceed",t,c),o(e)}))};a<i?u():s.push(u)},u=function(t,e,r){return function(o){!o.isTruncated&&o.finished===o.total&&o.subDeleted&&c(t,e,r,!1)}},l=so(t.Prefix);!function t(e,n,l,p,f){a++,r.listObjects({Bucket:n,Prefix:l,Delimiter:"/",Marker:p},(function(r,p){if(a--,r)return o(r);if(p.CommonMsg.Status>=300)return o(null,p);if(e.total+=p.InterfaceResult.Contents.length,e.total+=p.InterfaceResult.CommonPrefixes.length,0!==e.total){e.isTruncated="true"===p.InterfaceResult.IsTruncated;for(var d=function(r){return function(){t({total:0,finished:0,isTruncated:!1,bucket:n,subDeleted:!0},n,r,null,u(r,e,f))}},h=0;h<p.InterfaceResult.CommonPrefixes.length;h++){var m=so(p.InterfaceResult.CommonPrefixes[h].Prefix);a<i?t({total:0,finished:0,isTruncated:!1,bucket:n,subDeleted:!0},n,m,null,u(m,e,f)):s.push(d(m))}for(var y=0;y<p.InterfaceResult.Contents.length;y++){var g=p.InterfaceResult.Contents[y].Key;c(g,e,f,g.lastIndexOf("/")===g.length-1)}e.isTruncated&&(a<i?t(e,n,l,p.InterfaceResult.NextMarker,f):s.push((function(){t(e,n,l,p.InterfaceResult.NextMarker,f)})))}else f(e)}))}({total:0,finished:0,isTruncated:!1,bucket:t.Bucket,subDeleted:!0},t.Bucket,l,null,(function(t){if(!t.isTruncated&&t.finished===t.total)if(t.subDeleted)r.dropFile({Bucket:t.bucket,Key:l},(function(t,e){return t?(n("dropFileFailed",l,t),o(t)):e.CommonMsg.Status>=300?(n("dropFileFailed",l,e),o(null,e)):(n("dropFileSucceed",l,e),o(null,e))}));else{var e="drop folder "+l+" failed due to child file deletion failed";n("dropFileFailed",l,new Error(e)),o(e)}}))}}},uo=co,lo=n(2062),po=n.n(lo),fo=5368709120,ho=function(t){return"[object Function]"===Object.prototype.toString.call(t)},mo=function(t){var e=[];if(e.push(t.bucket),e.push(t.key),e.push(t.sourceFile.name),e.push(String(t.partSize)),e.push(String(t.partCount)),e.push(String(t.fileStat.fileSize)),e.push(String(t.fileStat.lastModified)),t.uploadId&&e.push(t.uploadId),t.sseC&&e.push(t.sseC),t.sseCKey&&e.push(t.sseCKey),t.parts)for(var r=0;r<t.parts.length;r++){var o=t.parts[r];o&&(e.push(String(o.partNumber)),e.push(String(o.offset)),e.push(String(o.partSize)),e.push(String(o.isCompleted)),o.etag&&e.push(String(o.etag)))}return window.btoa(se()(e.join(""),!1,!0))},yo=function(t,e,r){t&&t.uploadId&&r.abortMultipartUpload({Bucket:t.bucket,Key:t.key,RequestDate:t.requestDate,UploadId:t.uploadId},(function(o,n){o?r.log.runLog("warn",e,"abort multipart upload failed, bucket:"+t.bucket+", key:"+t.key+", uploadId:"+t.uploadId+", err:"+o):n.CommonMsg.Status>=300?r.log.runLog("warn",e,"abort multipart upload failed, bucket:"+t.bucket+", key:"+t.key+", uploadId:"+t.uploadId+", status:"+n.CommonMsg.Status+", code:"+n.CommonMsg.Code+", message:"+n.CommonMsg.Message):(delete t.uploadId,r.log.runLog("warn",e,"abort multipart upload succeed, bucket:"+t.bucket+", key:"+t.key+", uploadId:"+t.uploadId))}))},go=function(t){if(!(t.finishedCount<t.uploadCheckpoint.partCount)){if(t.isAbort)return yo(t.uploadCheckpoint,t.funcName,t.that),t.callback("uploadFile failed the upload task is aborted");if(t.isSuspend)return t.callback("the process of uploadFile is suspened, you can retry with the uploadCheckpoint");if(t.hasError)return t.callback("uploadFile finished with error, you can retry with the uploadCheckpoint");for(var e=[],r=0;r<t.uploadCheckpoint.partCount;r++){var o=t.uploadCheckpoint.parts[r];e.push({PartNumber:o.partNumber,ETag:o.etag})}t.that.completeMultipartUpload({Bucket:t.uploadCheckpoint.bucket,Key:t.uploadCheckpoint.key,RequestDate:t.uploadCheckpoint.requestDate,UploadId:t.uploadCheckpoint.uploadId,Parts:e,CallbackUrl:t.callbackUrl,CallbackHost:t.callbackHost,CallbackBody:t.callbackBody,CallbackBodyType:t.callbackBodyType},(function(e,r){var o={bucket:t.uploadCheckpoint.bucket,key:t.uploadCheckpoint.key,uploadId:t.uploadCheckpoint.uploadId};return e?(t.eventCallback("completeMultipartUploadFailed",o,e),t.callback(e)):r.CommonMsg.Status>=500?(t.eventCallback("completeMultipartUploadFailed",o,r),t.callback(null,r)):r.CommonMsg.Status>=300&&r.CommonMsg.Status<500?(t.eventCallback("completeMultipartUploadAborted",o,r),yo(t.uploadCheckpoint,t.funcName,t.that),t.callback(null,r)):(t.eventCallback("completeMultipartUploadSucceed",o,r),void t.callback(null,r))}))}},vo=function(t){t.resumeCallback({cancel:function(){t.isSuspend=!0;for(var e=0;e<t.uploadPartParams.length;e++){var r=t.uploadPartParams[e].cancelHook;ho(r)&&r()}},abort:function(){t.isAbort=!0;for(var e=0;e<t.uploadPartParams.length;e++){var r=t.uploadPartParams[e].abortHook;ho(r)&&r()}}},t.uploadCheckpoint);var e=[],r=function(){if(0!==e.length)for(;t.runningTask<t.taskNum&&e.length>0;)e.shift()();else go(t)},o=(window.btoa&&window.btoa,function(o){return function(){if(t.runningTask++,t.isSuspend||t.isAbort)return t.runningTask--,t.finishedCount++,t.finishedCount+=e.length,e=[],r();var n,i,a,s,c=0,u=function(e){if(!c){c=1;var n,i={Bucket:t.uploadCheckpoint.bucket,Key:t.uploadCheckpoint.key,RequestDate:t.uploadCheckpoint.requestDate,PartNumber:o.partNumber,UploadId:t.uploadCheckpoint.uploadId,SourceFile:t.uploadCheckpoint.sourceFile,maxPartRetryCount:t.maxPartRetryCount,Offset:o.offset,PartSize:o.partSize,SseC:t.uploadCheckpoint.sseC,SseCKey:t.uploadCheckpoint.sseCKey,ProgressCallback:(n=o.partNumber,function(e,r,o){t.progressCallback(n,e)}),ContentMD5:e};t.uploadPartParams.push(i),t.that.uploadPart(i,(function(e,n){if(t.runningTask--,t.finishedCount++,t.isSuspend)return r();var i={partNumber:o.partNumber,bucket:t.uploadCheckpoint.bucket,key:t.uploadCheckpoint.key,uploadId:t.uploadCheckpoint.uploadId};e?(t.eventCallback("uploadPartFailed",i,e),t.hasError=!0):n.CommonMsg.Status>=500||400===n.CommonMsg.Status&&"BadDigest"===n.CommonMsg.Code?(t.eventCallback("uploadPartFailed",i,n),t.hasError=!0):n.CommonMsg.Status>=300&&n.CommonMsg.Status<500?(t.isAbort=!0,t.hasError=!0,t.eventCallback("uploadPartAborted",i,n)):(o.etag=n.InterfaceResult.ETag,o.isCompleted=!0,i.etag=o.etag,t.uploadCheckpoint.md5=mo(t.uploadCheckpoint),t.eventCallback("uploadPartSucceed",i,n),t.that.log.runLog("debug",t.funcName,"Part "+String(o.partNumber)+" is finished, uploadId "+t.uploadCheckpoint.uploadId)),r()}))}};if(t.verifyMd5&&window.FileReader&&(t.uploadCheckpoint.sourceFile instanceof window.File||t.uploadCheckpoint.sourceFile instanceof window.Blob)){var l=(n=t.uploadCheckpoint.sourceFile,i=o.offset,a=o.offset+o.partSize,s=s||n.type,n.mozSlice?n.mozSlice(i,a,s):n.webkitSlice?n.webkitSlice(i,a,s):n.slice(i,a,s)),p=new window.FileReader;return p.onload=function(t){var e=po().lib.WordArray.create(t.target.result),r=po().MD5(e),o=po().enc.Base64.stringify(r);e=null,u(o)},p.onerror=function(e){t.that.log.runLog("error",t.funcName,"Caculate md5 for part "+String(o.partNumber)+" failed"),u()},void p.readAsArrayBuffer(l)}u()}});if(!t.isSuspend){for(var n=0;n<t.uploadCheckpoint.partCount;n++){var i=t.uploadCheckpoint.parts[n];i.isCompleted?(t.finishedCount++,t.finishedBytes+=i.partSize):e.push(o(i))}return 0===e.length?go(t):r()}t.callback("the process of uploadFile is suspened, you can retry with the uploadCheckpoint")},bo={extend:function(t){t.prototype.uploadFile=function(t,e){var r=this;t=t||{};var o="uploadFile",n=function(t,e,r){t=t||function(){};var o=(new Date).getTime();return function(n,i){return e.runLog("info",r,"ObsClient cost "+((new Date).getTime()-o)+" ms"),"[object String]"===Object.prototype.toString.call(n)?t(new Error(n),i):t(n,i)}}(e,r.log,o),i=function(t){return t=t||function(){},function(e,r,o){return"[object Error]"===Object.prototype.toString.call(o)?t(e,r,o):"[object String]"===Object.prototype.toString.call(o)?t(e,r,new Error(o)):o?o.CommonMsg.Status>300?t(e,r,new Error("status:"+o.CommonMsg.Status+", code:"+o.CommonMsg.Code+", message:"+o.CommonMsg.Message)):void t(e,r,o):void 0}}(t.EventCallback),a=t.TaskNum||1,s=t.PartRetryNum||0,c=t.ProgressCallback||function(){},u=t.ResumeCallback||function(){},l=t.VerifyMd5||!1;r.log.runLog("info",o,"enter "+o+"...");var p=null;if(t.UploadCheckpoint&&t.UploadCheckpoint.sourceFile&&t.UploadCheckpoint.fileStat&&t.UploadCheckpoint.uploadId&&t.UploadCheckpoint.md5===mo(t.UploadCheckpoint)?p=t.UploadCheckpoint:yo(t.UploadCheckpoint,o,r),p){var f=p.sourceFile;if(!(f instanceof window.File||f instanceof window.Blob))return n("source file is not valid, must be an instanceof [File | Blob]");if(!f.mozSlice&&!f.webkitSlice&&!f.slice)return n("your browser cannot support the slice method for [File | Blob]")}else{var d=t.SourceFile;if(!(d instanceof window.File||d instanceof window.Blob))return n("source file is not valid, must be an instanceof [File | Blob]");if(!d.mozSlice&&!d.webkitSlice&&!d.slice)return n("your browser cannot support the slice method for [File | Blob]");r.log.runLog("debug",o,"Begin to uploadFile to OBS from file:"+d.name);var h=d.size,m=parseInt(t.PartSize,10),y=0,g=[];if(0===h)m=0,y=1,g.push({partNumber:1,offset:0,partSize:0,isCompleted:!1});else{if(m=isNaN(m)||m<102400?9437184:m>fo?fo:m,(y=Math.floor(h/m))>=1e4&&(m=Math.floor(h/1e4),h%1e4!=0&&(m+=1),y=Math.floor(h/m)),m>fo)return n("The source file "+d.name+" is too large");var v=h%m;0!==v&&y++;for(var b=0;b<y;b++)g.push({partNumber:b+1,offset:b*m,partSize:m,isCompleted:!1});0!==v&&(g[y-1].partSize=v)}r.log.runLog("debug",o,"Total parts count "+y),(p={bucket:t.Bucket,key:t.Key,sourceFile:d,partSize:m,partCount:y,parts:g}).fileStat={fileSize:h,lastModified:d.lastModified},t.SseC&&t.SseCKey&&(p.sseC=t.SseC,p.sseCKey=t.SseCKey),p.md5=mo(p)}p.requestDate=t.RequestDate;var A={start:(new Date).getTime(),uploadCheckpoint:p,funcName:o,maxPartRetryCount:s,taskNum:a,callback:n,that:r,runningTask:0,finishedCount:0,hasError:!1,finishedBytes:0,isAbort:!1,resumeCallback:u,isSuspend:!1,partsLoaded:{},requestDate:t.RequestDate,uploadPartParams:[],verifyMd5:l,callbackUrl:t.CallbackUrl,callbackHost:t.CallbackHost,callbackBody:t.CallbackBody,callbackBodyType:t.CallbackBodyType,eventCallback:function(t,e,r){A.isSuspend||i(t,e,r)},progressCallback:function(t,e){A.isSuspend||(A.finishedBytes+=e,A.partsLoaded[t]&&(A.finishedBytes-=A.partsLoaded[t]),A.partsLoaded[t]=e,c(A.finishedBytes,A.uploadCheckpoint.fileStat.fileSize,((new Date).getTime()-A.start)/1e3))}};if(!p.uploadId){var x=t.ContentType;return!x&&p.key&&(x=r.util.mimeTypes[p.key.substring(p.key.lastIndexOf(".")+1)]),!x&&p.sourceFile.name&&(x=r.util.mimeTypes[p.sourceFile.name.substring(p.sourceFile.name.lastIndexOf(".")+1)]),void r.initiateMultipartUpload({Bucket:t.Bucket,Key:t.Key,RequestDate:t.RequestDate,ACL:t.ACL,Metadata:t.Metadata,WebsiteRedirectLocation:t.WebsiteRedirectLocation,StorageClass:t.StorageClass,ContentType:x,Expires:t.Expires,SseKms:t.SseKms,SseKmsKey:t.SseKmsKey,SseC:t.SseC,SseCKey:t.SseCKey},(function(e,i){var a={bucket:t.Bucket,key:t.Key};if(e)return A.eventCallback("initiateMultipartUploadFailed",a,e),n(e);if(i.CommonMsg.Status>=300)return A.eventCallback("initiateMultipartUploadFailed",a,i),n(null,i);var s=i.InterfaceResult.UploadId;p.uploadId=s,p.md5=mo(p),A.uploadCheckpoint=p,a.uploadId=s,r.log.runLog("info",o,"Claim a new upload id "+s),A.eventCallback("initiateMultipartUploadSucceed",a,i),vo(A)}))}vo(A)}}},Ao=bo;function xo(t){return xo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xo(t)}function wo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}function So(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wo(Object(r),!0).forEach((function(e){Po(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Po(t,e,r){return e=function(t){var e=function(t,e){if("object"!=xo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,"string");if("object"!=xo(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==xo(e)?e:e+""}(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ko(t){this.factory(t)}function Co(t){return t.slice(0,1).toUpperCase()+t.slice(1)}var Oo=["createBucket","createSfsBucket","listBuckets","listSfsBuckets","getBucketMetadata","headBucket","deleteBucket","deleteSfsBucket","setBucketQuota","getBucketQuota","getBucketStorageInfo","setBucketPolicy","getBucketPolicy","deleteBucketPolicy","setBucketVersioningConfiguration","getBucketVersioningConfiguration","putBackToSource","deleteBackToSource","getBackToSource","getBucketLocation","listVersions","listObjects","setBucketLifecycleConfiguration","getBucketLifecycleConfiguration","deleteBucketLifecycleConfiguration","setBucketAcl","getBucketAcl","setBucketLoggingConfiguration","getBucketLoggingConfiguration","setBucketWebsiteConfiguration","getBucketWebsiteConfiguration","deleteBucketWebsiteConfiguration","setBucketNotification","getBucketNotification","getBucketObjectLockConfiguration","setBucketObjectLockConfig","setBucketTagging","getBucketTagging","deleteBucketTagging","getBucketCors","deleteBucketCors","setBucketStoragePolicy","getBucketStoragePolicy","getObject","getObjectMetadata","setObjectMetadata","setObjectAcl","getObjectAcl","setObjectObjectLock","deleteObject","deleteObjects","listMultipartUploads","listParts","abortMultipartUpload","completeMultipartUpload","getBucketInventory","setBucketInventory","deleteBucketInventory","getBucketEncryption","setBucketEncryption","deleteBucketEncryption","getBucketRequesterPay","setBucketRequesterPay","setMirrorBackToSource","getMirrorBackToSource","deleteMirrorBackToSource","getWorkflowTrigger","deleteWorkflowTrigger","createWorkflowTrigger","restoreFailedWorkflowExecution","createTemplate","createWorkflow","createAuditPolicy","getAuditPolicy","deleteAuditPolicy","getAuditResult","getWorkflowList","deleteWorkflow","getWorkflowTemplateList","getWorkflowInstanceList","deleteTemplate","updateWorkflow","getActionTemplates","getWorkflowAuthorization","openWorkflowAuthorization","getBucketDirectColdAccess","setBucketDirectColdAccess","deleteBucketDirectColdAccess","getBucketCustomDomain","setBucketCustomDomain","deleteBucketCustomDomain","setBucketCors","getBucketReplication","setBucketReplication","deleteBucketReplication","getCDNNotifyConfiguration","setCdnNotifyConfiguration","getQuota","getBucketDisPolicy","setBucketDisPolicy","deleteBucketDisPolicy","createOnlineDecom","getOnlineDecom","getWorkflowAgreements","openWorkflowAgreements","deleteOnlineDecom","getMyActionTemplates","createMyActionTemplate","getMyactiontemplateDetail","updateMyActionTemplate","deleteMyActionTemplate","forbidMyActionTemplate","updatePublicActionTemplate","getOmPublicActionTemplates","setSFSAcl","getSFSAcl","deleteSFSAcl","setBucketAlias","bindBucketAlias","unbindBucketAlias","deleteBucketAlias","listBucketsAlias","getBucketAlias","getSFSPermissionAcl","updateSFSPermissionAcl","deleteSFSPermissionAcl","getSFSPermissionGroupList","setSFSPermissionGroup","updateSFSPermissionGroup","getSFSPermissionGroup","deleteSFSPermissionGroup","setObjectTagging","getObjectTagging","deleteObjectTagging"];function Eo(t){return function(e,r){this.exec(Co(t),e,r)}}for(var To=0;To<Oo.length;To++){var Bo=Oo[To];ko.prototype[Bo]=Eo(Bo)}function Mo(t){return"[object Function]"===Object.prototype.toString.call(t)}function Ro(t){return function(e,r){if(Mo(e))t.call(this,null,e);else{if(!Mo(r)){var o=this;return new Promise((function(r,n){t.call(o,e,(function(t,e){if(t)return n(t);r(e)}))}))}t.call(this,e,r)}}}if(ko.prototype.createTemplate=function(t,e){t.ApiPath="v2/workflowtemplates",this.exec("CreateTemplate",t,e)},ko.prototype.createWorkflow=function(t,e){t.ApiPath="v2/workflows",this.exec("CreateWorkflow",t,e)},ko.prototype.createAuditPolicy=function(t,e){t.ApiPath="v2/audit/policy",this.exec("CreateAuditPolicy",t,e)},ko.prototype.getAuditPolicy=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/audit/policy",this.exec("GetAuditPolicy",t,e),this.util.pathStyle=!1},ko.prototype.putAuditPolicy=function(t,e){t.ApiPath="v2/audit/policy",this.exec("PutAuditPolicy",t,e)},ko.prototype.deleteAuditPolicy=function(t,e){t.ApiPath="v2/audit/policy",this.exec("DeleteAuditPolicy",t,e)},ko.prototype.restoreFailedWorkflowExecution=function(t,e){t.ApiPath="v2/workflowexecutions",this.exec("RestoreFailedWorkflowExecution",t,e)},ko.prototype.getWorkflowList=function(t,e){t.ApiPath="v2/workflows",this.exec("GetWorkflowList",t,e)},ko.prototype.getAuditResult=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/audit/result",this.exec("GetAuditResult",t,e),this.util.pathStyle=!1},ko.prototype.deleteWorkflow=function(t,e){t.ApiPath="v2/workflows",this.exec("DeleteWorkflow",t,e)},ko.prototype.deleteTemplate=function(t,e){t.ApiPath="v2/workflowtemplates",this.exec("DeleteTemplate",t,e)},ko.prototype.getWorkflowTemplateList=function(t,e){t.ApiPath="v2/workflowtemplates",this.exec("GetWorkflowTemplateList",t,e)},ko.prototype.getWorkflowInstanceList=function(t,e){t.ApiPath="v2/workflowexecutions",this.exec("GetWorkflowInstanceList",t,e)},ko.prototype.updateWorkflow=function(t,e){t.ApiPath="v2/workflows",this.exec("UpdateWorkflow",t,e)},ko.prototype.getActionTemplates=function(t,e){t.ApiPath="v2/actiontemplates",this.exec("GetActionTemplates",t,e)},ko.prototype.getWorkflowAuthorization=function(t,e){t.ApiPath="v2/workflow-authorization",this.exec("GetWorkflowAuthorization",t,e)},ko.prototype.openWorkflowAuthorization=function(t,e){t.ApiPath="v2/workflow-authorization",this.exec("OpenWorkflowAuthorization",t,e)},ko.prototype.getPublicationTemplates=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/publicactiontemplates",this.exec("GetPublicationTemplates",t,e),this.util.pathStyle=!1},ko.prototype.getPublicationTemplateDetail=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/publicactiontemplates",this.exec("GetPublicationTemplateDetail",t,e),this.util.pathStyle=!1},ko.prototype.getWorkflowAgreements=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/workflow-agreements",this.exec("GetWorkflowAgreements",t,e),this.util.pathStyle=!1},ko.prototype.openWorkflowAgreements=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/workflow-agreements",this.exec("OpenWorkflowAgreements",t,e),this.util.pathStyle=!1},ko.prototype.createMyActionTemplate=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/myactiontemplates",this.exec("CreateMyActionTemplate",t,e),this.util.pathStyle=!1},ko.prototype.getMyActionTemplates=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/myactiontemplates",this.exec("GetMyActionTemplates",t,e),this.util.pathStyle=!1},ko.prototype.getMyactiontemplateDetail=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/myactiontemplates",this.exec("GetMyactiontemplateDetail",t,e),this.util.pathStyle=!1},ko.prototype.updateMyActionTemplate=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/myactiontemplates",this.exec("UpdateMyActionTemplate",t,e),this.util.pathStyle=!1},ko.prototype.deleteMyActionTemplate=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/myactiontemplates",this.exec("DeleteMyActionTemplate",t,e),this.util.pathStyle=!1},ko.prototype.createSfsBucket=function(t,e){this.util.pathStyle=!0;var r=So({},t),o=r.Bucket;r.ApiPath="v1/sfs/".concat(o),delete r.Bucket,this.exec("CreateSfsBucket",r,e),this.util.pathStyle=!1},ko.prototype.listSfsBuckets=function(t,e){this.util.pathStyle=!0;var r=So({},t);r.ApiPath="v1/sfs",this.exec("ListSfsBuckets",r,e),this.util.pathStyle=!1},ko.prototype.deleteSfsBucket=function(t,e){this.util.pathStyle=!0;var r=So({},t),o=r.Bucket;r.ApiPath="v1/sfs/".concat(o),delete r.Bucket,this.exec("DeleteSfsBucket",r,e),this.util.pathStyle=!1},ko.prototype.forbidMyActionTemplate=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/myactiontemplates",this.exec("ForbidMyActionTemplate",t,e),this.util.pathStyle=!1},ko.prototype.updatePublicActionTemplate=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/ompublicactiontemplates",this.exec("UpdatePublicActionTemplate",t,e),this.util.pathStyle=!1},ko.prototype.getOmPublicActionTemplates=function(t,e){this.util.pathStyle=!0,t.ApiPath="v2/ompublicactiontemplates",this.exec("GetOmPublicActionTemplates",t,e),this.util.pathStyle=!1},ko.prototype.putObject=function(t,e){if("Body"in t&&"SourceFile"in t){var r="the input body and sourcefile exist at same time,please specify one of eigther a string or file to be send!";return this.log.runLog("error","PutObject",r),e(new Error(r),null)}if(!("ContentType"in t)&&("Key"in t&&(t.ContentType=this.util.mimeTypes[t.Key.substring(t.Key.lastIndexOf(".")+1)]),!t.ContentType&&"SourceFile"in t)){var o=t.SourceFile.name;t.ContentType=this.util.mimeTypes[o.substring(o.lastIndexOf(".")+1)]}this.exec("PutObject",t,e)},ko.prototype.appendObject=function(t,e){if("Body"in t&&"SourceFile"in t){var r="the input body and sourcefile exist at same time,please specify one of eigther a string or file to be send!";return this.log.isLevelEnabled("error")&&this.log.runLog("error","PutObject",r),e(new Error(r),null)}"ContentType"in t||("Key"in t&&(t.ContentType=this.util.mimeTypes[t.Key.substring(t.Key.lastIndexOf(".")+1)]),!t.ContentType&&"SourceFile"in t&&(t.ContentType=this.util.mimeTypes[t.SourceFile.substring(t.SourceFile.lastIndexOf(".")+1)])),this.exec("AppendObject",t,e)},ko.prototype.copyObject=function(t,e){var r="CopySource";if(r in t){var o=t[r],n=o.lastIndexOf("?versionId=");t[r]=n>0?this.util.encodeURIWithSafe(o.slice(0,n))+o.slice(n):this.util.encodeURIWithSafe(o)}this.exec("CopyObject",t,e)},ko.prototype.copyPart=function(t,e){var r="CopySource";if(r in t){var o=t[r],n=o.lastIndexOf("?versionId=");t[r]=n>0?this.util.encodeURIWithSafe(o.slice(0,n))+o.slice(n):this.util.encodeURIWithSafe(o)}this.exec("CopyPart",t,e)},ko.prototype.restoreObject=function(t,e){this.exec("RestoreObject",t,(function(t,r){!t&&r.InterfaceResult&&r.CommonMsg.Status<300&&(r.InterfaceResult.RestoreStatus=200===r.CommonMsg.Status?"AVALIABLE":"INPROGRESS"),e(t,r)}))},ko.prototype.initiateMultipartUpload=function(t,e){"ContentType"in t||"Key"in t&&(t.ContentType=this.util.mimeTypes[t.Key.substring(t.Key.lastIndexOf(".")+1)]),this.exec("InitiateMultipartUpload",t,e)},ko.prototype.uploadPart=function(t,e){if("Body"in t&&"SourceFile"in t){var r="the input body and sourcefile exist at same time, please specify one of eigther a string or file to be send!";return this.log.runLog("error","UploadPart",r),e(new Error(r),null)}this.exec("UploadPart",t,e)},ko.prototype.getSFSPermissionAcl=function(t,e){this.util.pathStyle=!0;var r=So({},t);r.ApiPath="v3/bucket/".concat(t.Bucket,"/sfsacl"),delete r.Bucket,this.exec("getSFSPermissionAcl",r,e),this.util.pathStyle=!1},ko.prototype.updateSFSPermissionAcl=function(t,e){this.util.pathStyle=!0;var r=So({},t);r.ApiPath="v3/bucket/".concat(t.Bucket,"/sfsacl"),delete r.Bucket,this.exec("updateSFSPermissionAcl",r,e),this.util.pathStyle=!1},ko.prototype.deleteSFSPermissionAcl=function(t,e){this.util.pathStyle=!0;var r=So({},t);r.ApiPath="v3/bucket/".concat(t.Bucket,"/sfsacl"),delete r.Bucket,this.exec("deleteSFSPermissionAcl",r,e),this.util.pathStyle=!1},ko.prototype.getSFSPermissionGroupList=function(t,e){t||(t={}),this.util.pathStyle=!0,t.ApiPath="v3/sfs/permission-group",this.exec("getSFSPermissionGroupList",t,e),this.util.pathStyle=!1},ko.prototype.setSFSPermissionGroup=function(t,e){this.util.pathStyle=!0,t.ApiPath="v3/sfs/permission-group",this.exec("setSFSPermissionGroup",t,e),this.util.pathStyle=!1},ko.prototype.updateSFSPermissionGroup=function(t,e){this.util.pathStyle=!0,t.ApiPath="v3/sfs/permission-group/".concat(t.id),this.exec("updateSFSPermissionGroup",t,e),this.util.pathStyle=!1},ko.prototype.getSFSPermissionGroup=function(t,e){this.util.pathStyle=!0,t.ApiPath="v3/sfs/permission-group/".concat(t.id),this.exec("getSFSPermissionGroup",t,e),this.util.pathStyle=!1},ko.prototype.deleteSFSPermissionGroup=function(t,e){this.util.pathStyle=!0,t.ApiPath="v3/sfs/permission-group/".concat(t.id),this.exec("deleteSFSPermissionGroup",t,e),this.util.pathStyle=!1},uo.extend(ko),Ao.extend(ko),Mo(Promise))for(var _o in ko.prototype)if({}.hasOwnProperty.call(ko.prototype,_o)){var Io=ko.prototype[_o];ko.prototype[_o]=Ro(Io)}for(var jo in ko.prototype.exec=function(t,e,r){var o=this.log;o.runLog("info",t,"enter "+t+"...");var n=(new Date).getTime();e=e||{},r=r||function(){},this.util.exec(t,e,(function e(i,a){e.$called||(e.$called=!0,!i||i instanceof Error||(i=new Error(i)),o.runLog("debug",t,"ObsClient cost "+((new Date).getTime()-n)+" ms"),r(i,a))}))},ko.prototype.initLog=function(t){t=t||{},this.log.setLevel(t.level);var e=["[OBS SDK Version="+this.util.obsSdkVersion];if(this.util.server){var r=this.util.port?":"+this.util.port:"";e.push("Endpoint="+(this.util.is_secure?"https":"http")+"://"+this.util.server+r)}e.push("Access Mode="+(this.util.path_style?"Path":"Virtual Hosting")+"]"),this.log.runLog("warn","init",e.join("];["))},ko.prototype.factory=function(t){this.log=new ao,this.util=new oo(this.log),t=t||{},this.util.initFactory(t.access_key_id,t.secret_access_key,t.is_secure,t.server,t.path_style,t.signature,t.region,t.port,t.timeout,t.security_token,t.is_signature_negotiation,t.is_cname,t.url_prefix,t.region_domains,t.setRequestHeaderHook,t.useRawXhr,t.checksum_algorithm)},ko.prototype.refresh=function(t,e,r){this.util.refresh(t,e,r)},ko.prototype.createSignedUrl=function(t){return this.util.createSignedUrl(t)},ko.prototype.createSignedUrlSync=function(t){return this.util.createSignedUrlSync(t)},ko.prototype.createV2SignedUrlSync=function(t){return this.util.createV2SignedUrlSync(t)},ko.prototype.createV4SignedUrlSync=function(t){return this.util.createV4SignedUrlSync(t)},ko.prototype.createPostSignatureSync=function(t){return this.util.createPostSignatureSync(t)},ko.prototype.createV4PostSignatureSync=function(t){return this.util.createV4PostSignatureSync(t)},ko.prototype.enums=kr,ko.prototype)({}).hasOwnProperty.call(ko.prototype,jo)&&(ko.prototype[Co(jo)]=ko.prototype[jo]);for(var Do in ko.prototype)if({}.hasOwnProperty.call(ko.prototype,Do)){var Lo=Do.indexOf("Configuration");Lo>0&&Lo+13===Do.length&&(ko.prototype[Do.slice(0,Lo)]=ko.prototype[Do])}var qo=ko}(),i.default}()}));