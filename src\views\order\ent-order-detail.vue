<template>
  <div class="ent-detail-container">
    <div class="order-description">
      <div class="description-block">
        <div class="form-title">订单状态：{{ formDetail.status }}</div>
        <div class="order-status">
          <div
            class="status-item"
            v-for="(item, index) in orderStatus"
            :key="index"
          >
            <div class="status-dot">
              <svg-icon
                :icon-class="'status-success'"
                v-if="item.value <= formDetail.status"
              />
              <svg-icon :icon-class="'status-loading'" v-else />
            </div>
            <div class="status-name">
              {{ item.label }}
            </div>
            <span class="status-time">{{ item.time }}</span>
          </div>
          <div class="line"></div>
        </div>
      </div>
      <div class="middle-line"></div>
      <div class="description-block">
        <div class="right-btn">
          <div class="btn primary-btn" @click="handleBack">返回</div>
        </div>
        <div class="description-form">
          <div class="form-item">
            <div class="form-label">企业名称：</div>
            <div class="form-value">
              {{ formDetail.name || "--" }}
            </div>
          </div>
          <div class="form-item">
            <div class="form-label">订单编号：</div>
            <div class="form-value">
              {{ formDetail.order_no || "--" }}
            </div>
          </div>
          <div class="form-item">
            <div class="form-label">订单价格：</div>
            <div class="form-value">
              {{ formDetail.price || "--" }}
            </div>
          </div>
          <div class="form-item">
            <div class="form-label">套餐明细：</div>
            <div class="form-value">
              <el-scrollbar class="package-list" :max-height="100">
                <div class="package-block">
                  <span class="name">我是套餐名字</span>
                  <span class="status">已取消</span>
                </div>
                <div class="package-block">
                  <span class="name">
                    我是套餐名字，多个套餐会行显示，套餐名
                    称最多两行啊，多了就显示省略号啦！啦…</span
                  >
                  <span class="status">￥10000</span>
                </div>
                <div class="package-block">
                  <span class="name">
                    我是套餐名字，多个套餐会行显示，套餐名
                    称最多两行啊，多了就显示省略号啦！啦…我是套餐名字，多个套餐会行显示，套餐名
                    称最多两行啊，多了就显示省略号啦！啦…
                    我是套餐名字，多个套餐会行显示，套餐名
                    称最多两行啊，多了就显示省略号啦！啦…</span
                  >
                  <span class="status">￥10000</span>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-content">
      <div class="container-header">
        <div class="left">
          <div class="filter-row">
            <el-input
              v-model="queryParams.search"
              placeholder="请输入关键字"
              clearable
              size="large"
            />
          </div>
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="right">
          <!--  -->
        </div>
      </div>
      <div class="content">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="courseData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="课程" align="center" min-width="140">
            <template #default="scope">
              <div class="cover-name">
                <img :src="scope.row.thumb" alt="" class="cover-img" />
                <span>{{ scope.row.title }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="课程账号数" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.accounts || "--" }}
            </template>
          </el-table-column>
          <el-table-column label="已分配账号数" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.assigned || "0" }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </div>
    </div>

    <el-dialog
      class="previewImgDialog"
      :width="'70%'"
      v-model="dialogPreview.visible"
    >
      <div class="preview-img">
        <el-scrollbar warp-style="overflow-x: hidden;">
          <img :src="previewImgUrl" alt="Preview Image" />
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { getEnterprisesDetail } from "@/api/enterprise";
import { parseTime, secondsToMinutes } from "@/utils";
import { isString } from "@/utils/validate";
import { checkUserPermission } from "@/utils/auth";

const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MaterialLibrary",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const formDetail = reactive<any>({
  status: 20,
});
const loading = ref(false);
const total = ref(0);
const courseData = ref([]);

const orderStatus = ref<any>([
  { label: "创建订单", value: 10, time: "2023-10-01 12:00:00" },
  { label: "审核订单", value: 20, time: "2023-10-01 12:00:00" },
  { label: "审核通过", value: 30, time: "2023-10-01 12:00:00" },
  // {label:"取消订单",value:40,time:'2023-10-01 12:00:00'},
  // {label:"审核不通过",value:50,time:'2023-10-01 12:00:00'},
]); // 订单状态--根据detail动态渲染TODO:

// 弹窗对象
const dialogPreview = reactive({
  visible: false,
  title: " 预览",
  width: "60%",
});
const previewImgUrl = ref<any>("");

const orderId: any = computed(() => {
  return route.query.id;
});

onMounted(() => {
  // getEntOrderDetail();
});

function getEntOrderDetail() {
  getEnterprisesDetail(orderId.value).then((res: any) => {
    Object.assign(formDetail, res.data, {
      created_at: parseTime(res.data.created_at, "{y}-{m}-{d} {h}:{i}:{s}"),
    });

    total.value = 1;
  });
}
function getData() {}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.ent-detail-container {
  // width: 50%;
  height: 95%;
  margin: 20px;

  .order-description {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 331px;
    // margin-bottom: 10px;

    .description-block {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 49.8%;
      height: 100%;
      background: linear-gradient(180deg, #fff 0%, #fafdfb 100%);
      border-radius: 8px 0 0;
      box-shadow: inset 0 3px 50px 1px rgb(132 237 181 / 31%);
    }

    .middle-line {
      flex: 1;
      height: 100%;
      background: #fff;
    }

    .description-block:last-child {
      border-radius: 0 8px 0 0;
    }

    .form-title {
      position: absolute;
      top: 25px;
      left: 40px;
      font-size: 22px;
      font-weight: 600;
      color: #3d4040;
    }

    .right-btn {
      position: absolute;
      top: 25px;
      right: 40px;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }

    .order-status {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 584px;
      width: 85%;

      .line {
        position: absolute;
        top: 29%;
        left: 10%;
        z-index: 1;
        width: 80%;
        height: 8px;
        background: #99bcad;
        transform: translateY(-50%);
      }

      .status-dot {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 94px;
        // font-size: 64px;
        font-size: 94px;
      }

      .status-item {
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .status-name {
          font-size: 20px;
          font-weight: 500;
          color: #3d4040;
        }

        .status-time {
          margin-top: 18px;
          font-size: 14px;
          font-weight: 400;
          color: #3b4664;
        }
      }
    }

    .description-form {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 90%;
      height: 100%;
      padding: 0 20px;

      .form-item {
        display: flex;
        margin: 10px 0;

        .form-label {
          min-width: 100px;
          font-size: 15px;
          font-weight: 400;
          color: #3b4664;
        }

        .form-value {
          font-size: 17px;
          font-weight: 500;
          color: #3b4664;

          .package-block {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            font-size: 17px;
            font-weight: 500;
            line-height: 21px;
            color: #3b4664;
            background: hsl(180deg 3% 74% / 43%);
            border-radius: 12px;

            .name {
              display: -webkit-box; /* 必须结合 line-clamp 使用 */
              max-height: 3em; /* 行高 * 行数 (1.5 * 2 = 3em) */
              overflow: hidden; /* 隐藏超出内容 */
              line-height: 1.5;
              text-overflow: ellipsis; /* 显示省略号 */
              -webkit-box-orient: vertical; /* 设置盒子垂直排列 */
              -webkit-line-clamp: 2; /* 限制显示两行 */
            }

            .status {
              padding-left: 20px;
            }
          }
        }
      }
    }
  }

  .table-content {
    width: 100%;
    height: calc(100% - 331px);
    background: #fff;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  }

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 240px);
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      display: flex;
      align-items: center;
      justify-content: center;

      .cover-img {
        // width: 150px !important;
        // height: 100px !important;
        width: 162px !important;
        height: 86px !important;
        object-fit: cover;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.preview-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  :deep(.el-scrollbar__wrap) {
    max-height: 75vh;
  }

  img {
    width: 100%;
  }
}
</style>
