<template>
  <div :style="{ position: 'relative' }" class="bar-chart">
    <div class="title">订单金额趋势</div>
    <div :id="id" :class="className" :style="{ height, width }"></div>
  </div>

  <!-- <el-card style="height: 100%">
    <template #header> 
       <el-tooltip effect="dark" content="点击试试下载" placement="bottom">
          <i-ep-download class="download" @click="downloadEchart" />
        </el-tooltip>
    </template>
    <div :id="id" :class="className" :style="{ height, width }"></div>
  </el-card> -->
</template>

<script setup lang="ts">
import * as echarts from "echarts";

const props = defineProps({
  id: {
    type: String,
    default: "barChart",
  },
  className: {
    type: String,
    default: "barChart",
  },
  width: {
    type: String,
    default: "200px",
    required: true,
  },
  height: {
    type: String,
    default: "90%",
    // required: true,
  },
  gridTop: {
    type: String,
    default: "17%",
    required: true,
  },
  chartData: {
    type: Array,
    default: () => [],
    required: true,
  },
});
const chart = ref<any>("");
const options = reactive<any>({});
const chartDataFilter = ref<any>([]);
watch(
  () => props.chartData,
  (val) => {
    if (val.length > 0) {
      chartDataFilter.value = val;
    }
  },
  { deep: true }
);
watch(
  () => chartDataFilter.value,
  (val) => {
    Object.assign(options, {
      grid: {
        left: "2%",
        right: "2%",
        top: props.gridTop,
        // bottom: "10%",
        bottom: "7%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
      },
      // legend: {
      //   x: "center",
      //   y: "bottom",
      //   data: ["金额", "毛利润", "收入增长率", "利润增长率"],
      //   textStyle: {
      //     color: "#999",
      //   },
      // },
      xAxis: [
        {
          type: "category",
          data: chartDataFilter.value.map((item: any) => item.date),
          axisPointer: {
            type: "shadow",
          },
        },
      ],
      yAxis: [
        {
          type: "value",
          name: "单位：元",
          min: 0,
          // max: 10000,
          // interval: 2000,
          axisLabel: {
            formatter: "{value} ",
          },
        },
      ],
      series: [
        {
          name: "金额",
          type: "bar",
          data: chartDataFilter.value.map((item: any) => item.amount),
          barWidth: "50%",
          itemStyle: {
            // background: linear-gradient( 180deg, #33CEC9 0%, #1C8D84 100%);
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#33CEC9" },
              { offset: 1, color: "#1C8D84" },
            ]),
          },
        },
      ],
    });
    chartInit();
  },
  {
    deep: true,
    // immediate: true
  }
);

onMounted(() => {
  // 图表初始化
  nextTick(() => {
    chartInit();
  });
  // chart.value = markRaw(
  //   echarts.init(document.getElementById(props.id) as HTMLDivElement)
  // );
  // chart.value.setOption(options);
  // // 大小自适应
  // window.addEventListener("resize", () => {
  //   chart.value.resize();
  // });
});

function chartInit() {
  chart.value = markRaw(
    echarts.init(document.getElementById(props.id) as HTMLDivElement)
  );

  chart.value.setOption(options);
  // 大小自适应
  window.addEventListener("resize", () => {
    chart.value.resize();
  });
}
const downloadEchart = () => {
  // 获取画布图表地址信息
  const img = new Image();
  img.src = chart.value.getDataURL({
    type: "png",
    pixelRatio: 1,
    backgroundColor: "#fff",
  });
  // 当图片加载完成后，生成 URL 并下载
  img.onload = () => {
    const canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext("2d");
    if (ctx) {
      ctx.drawImage(img, 0, 0, img.width, img.height);
      const link = document.createElement("a");
      link.download = `柱状图.png`;
      link.href = canvas.toDataURL("image/png", 0.9);
      document.body.appendChild(link);
      link.click();
      link.remove();
    }
  };
};
</script>
<style lang="scss" scoped>
.bar-chart {
  width: 100%;
  height: 100%;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.3);
  border-radius: 8px 8px 8px 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
}
.title {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 20px;
  left: 20px;
  font-weight: 700;
  font-size: 18px;
  color: #3b4664;
}
</style>
