<template>
  <div class="ent-detail-container">
    <div class="ent-description">
      <div class="form-title">基本信息</div>
      <div class="right-btn">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
      <div class="basic-form">
        <div class="col-item" style="width: 30%">
          <div class="item">
            <div class="label">企业名称：</div>
            <div class="text">
              <el-tooltip placement="top" :content="formDetail.name">
                {{ formDetail.name }}
              </el-tooltip>
            </div>
          </div>
          <div class="item">
            <div class="label">企业营业执照：</div>
            <div
              class="license-img"
              @click="showLicense"
              v-if="formDetail.license"
            >
              <img :src="formDetail.license" />
            </div>
            <div v-else class="text">暂无执照</div>
          </div>
        </div>
        <div class="col-item" style="width: 30%">
          <div :span="8" class="item">
            <div class="label">内训系统：</div>
            <div class="text">
              {{ formDetail.trainSysState ? "开启" : "未开启" }}
            </div>
          </div>

          <div class="item">
            <div class="label">企业规模：</div>
            <div class="text">
              {{ formDetail.ent_size || "--" }}
            </div>
          </div>
          <div class="item">
            <div class="label">企业账号数：</div>
            <div class="text">
              {{ formDetail.ent_size || "--" }}
            </div>
          </div>
          <div class="item">
            <div class="label">存储空间：</div>
            <div class="text">
              {{ formDetail?.storage || "--" }}
              {{ formDetail.storage ? "GB" : "" }}
            </div>
          </div>
          <div class="item">
            <div class="label">申请时间：</div>
            <div class="text">
              {{ formDetail.created_at }}
            </div>
          </div>
        </div>
        <div class="col-item" style="width: 40%">
          <div class="item">
            <div class="label">企业状态：</div>
            <div class="text">
              {{ entStatusMap[formDetail.status]?.label || "--" }}
            </div>
          </div>
          <div class="item">
            <div class="label">联系人：</div>
            <div class="text">{{ formDetail.contact }}</div>
          </div>
          <div class="item">
            <div class="label">联系电话：</div>
            <div class="text">{{ formDetail.contact_no }}</div>
          </div>

          <div class="item" style="align-items: flex-start">
            <div class="label">社会信用统一代码：</div>
            <div class="text">
              {{ formDetail.credit_code || "--" }}
            </div>
          </div>
          <div class="item">
            <div class="label">地址：</div>
            <div class="text">
              <el-tooltip
                placement="top"
                :content="
                  formDetail.province + formDetail.city + formDetail.address
                "
              >
                {{ formDetail.province }}{{ formDetail.city
                }}{{ formDetail.address || "--" }}
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-content">
      <div class="pre-header">
        <el-tabs
          v-model="activeTab"
          class="header-tabs"
          @tab-change="handleTab"
        >
          <el-tab-pane
            v-for="(item, index) in typeOptions"
            :key="index"
            :label="item.label"
            :name="item.value"
          />
        </el-tabs>
      </div>
      <div class="container-header">
        <div class="left">
          <div class="filter-row">
            <el-input
              v-model="queryParams.search"
              placeholder="请输入关键字"
              clearable
              size="large"
            />
          </div>
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>

          <!-- <div class="filter-row" v-if="activeTab === 'combo'">
            <el-select
              size="large"
              v-model="currentComboId"
              placeholder="请选择企业套餐"
              filterable
              :suffix-icon="`CaretBottom`"
              @change="currentComboChange"
            >
              <el-option
                v-for="item in packageOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div> -->
        </div>
        <div class="right">
          <!--  -->
        </div>
      </div>
      <div class="content" v-if="activeTab === 'course'">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="courseData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column
            label="名称"
            align="center"
            prop="name"
            min-width="140"
          >
            <template #default="scope">
              <div class="cover-name">
                <img :src="scope.row.thumb" alt="" class="cover-img" />
                <span>{{ scope.row.title }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="课程来源"
            align="center"
            prop="name"
            min-width="50"
          >
            <template #default="scope">
              <span>{{
                scope.row.course_src == 10 ? "原创" : "平台课程"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="状态"
            align="center"
            prop="createTime"
            min-width="50"
          >
            <template #default="scope">
              <el-tag :type="statusMap[scope.row.status]?.type">{{
                statusMap[scope.row.status]?.label
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="课程有效期" align="center" min-width="70">
            <template #default="scope">
              {{ scope.row.expired_at || "--" }}
            </template>
          </el-table-column>
          <el-table-column
            label="已用额度/课程账号额度"
            align="center"
            min-width="70"
          >
            <template #default="scope">
              <span
                class="assigned-tag"
                :style="{
                  background:
                    scope.row.course_src != 10 &&
                    assignedFilter(scope.row) == accountFilter(scope.row)
                      ? '#ff8c82'
                      : '#b1dd8b',
                }"
              >
                {{
                  scope.row.course_src == 10
                    ? scope.row.user_stat?.total || 0
                    : assignedFilter(scope.row)
                }}
                &ensp;/ &ensp;
                {{
                  scope.row.course_src == 10 ? "∞" : accountFilter(scope.row)
                }}
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="学习情况" align="center" min-width="100">
            <template #default="scope">
              {{ scope.row.data || "--" }}
            </template>
          </el-table-column>
          <el-table-column label="学习完成进度" align="center" min-width="50">
            <template #default="scope">
              {{ scope.row.data || "--" }}
            </template>
          </el-table-column> -->
        </el-table>
      </div>

      <div class="content" v-if="activeTab === 'training'">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="trainingData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="培训名称" align="center" min-width="100">
            <template #default="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="培训时间" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.from_at }} ~ {{ scope.row.till_at }}
            </template>
          </el-table-column>
          <el-table-column label="培训时长" align="center" min-width="60">
            <template #default="scope">
              {{ secondsToHoursAndMinutes(scope.row.time || 0) }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.created_at }}
            </template>
          </el-table-column>
          <el-table-column label="参与培训账号数" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.user_stat?.total }}
            </template>
          </el-table-column>
          <el-table-column label="培训完成进度 " align="center" min-width="60">
            <template #default="scope">
              {{ (scope.row.progress?.total || 0) + "%" || "--" }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="content" v-if="activeTab === 'staff'">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="staffData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="员工id" align="center" min-width="40">
            <template #default="scope">
              {{ scope.row.id }}
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center" min-width="100">
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column label="手机号码" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.mobile }}
            </template>
          </el-table-column>
          <el-table-column label="是否绑定微信" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.bind_wechat ? "已绑定" : "未绑定" }}
            </template>
          </el-table-column>
          <el-table-column label="部门" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.departments[0] || "--" }}
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" min-width="60">
            <template #default="scope">
              <el-tag :type="staffStatusMap[scope.row.status]?.type">
                {{ staffStatusMap[scope.row.status]?.label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="捆绑课程" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.ent_course_number }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </div>
    </div>

    <el-dialog
      class="previewImgDialog"
      :width="'70%'"
      v-model="dialogPreview.visible"
    >
      <div class="preview-img">
        <el-scrollbar warp-style="overflow-x: hidden;">
          <img :src="previewImgUrl" alt="Preview Image" />
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";

import { parseTime, secondsToHoursAndMinutes, secondsToMinutes } from "@/utils";
import { isString } from "@/utils/validate";
import { checkUserPermission } from "@/utils/auth";

import {
  getEntCourses,
  getEntUsers,
  getEntTrainingList,
  getEnterprisesDetail,
  getEntTrainSys,
} from "@/api/enterprise";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MaterialLibrary",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const typeOptions = ref<any>([
  {
    value: "course",
    label: "企业课程",
  },
  {
    value: "staff",
    label: "员工列表",
  },
  {
    value: "training",
    label: "培训计划",
  },
]);
const activeTab: any = ref("course");
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  tab_type: activeTab.value,
});

const entStatusMap = reactive<any>({
  10: { type: "primary", label: "待审核", color: "#409eff" },
  20: { type: "success", label: "正常", color: "#2ab7b0" },
  30: { type: "info", label: "冻结", color: "#909399" },
  40: { type: "danger", label: "注销", color: "#f56c6c" },
  50: { type: "warning", label: "咨询中", color: "#e6a23c" },
  60: { type: "error", label: "审核不通过", color: "#f56c6c" },
});
const staffStatusMap = reactive<any>({
  10: { type: "primary", label: "待加入" },
  20: { type: "success", label: "已加入" },
  30: { type: "info", label: "冻结" },
  40: { type: "danger", label: "移除" },
  // 50: { type: "danger", label: "已驳回" },
});
const statusMap = reactive<any>({
  10: { type: "primary", label: "编辑中", color: "#409eff" },
  20: { type: "success", label: "上架", color: "#2ab7b0" },
  30: { type: "info", label: "下架", color: "#909399" },
  40: { type: "danger", label: "删除", color: "#f56c6c" },
  50: { type: "warning", label: "过期", color: "#e6a23c" },
  // add more status mappings as needed
});

const formDetail = reactive<any>({});
const loading = ref(false);

const total = ref(0); // 数据总数
const courseData = ref([]);
const trainingData = ref([]);
const staffData = ref([]);

// 弹窗对象
const dialogPreview = reactive({
  visible: false,
  title: "企业营业执照",
  width: "60%",
});
const previewImgUrl = ref<any>("");

const entId: any = computed(() => {
  return route.query.id;
});

onMounted(() => {
  getEntDetail();
  getData();
});

function getEntDetail() {
  getEnterprisesDetail(entId.value).then((res: any) => {
    Object.assign(formDetail, res.data, {
      created_at: parseTime(res.data.created_at, "{y}-{m}-{d} {h}:{i}:{s}"),
    });
  });
  getEntTrainSys({ ent_id: entId.value }).then((res: any) => {
    Object.assign(formDetail, {
      storage: res.data[0]?.storage
        ? (res.data[0]?.storage / 1024 / 1024 / 1024).toFixed(3)
        : "",
      trainSysState: res.data[0] && res.data[0].status != 30,
    });
  });
}
function getData() {
  loading.value = true;
  if (activeTab.value == "course") {
    getEntCoursesData();
  } else if (activeTab.value == "staff") {
    getEntStaff();
  } else if (activeTab.value == "training") {
    getEntTrainingData();
  }
}
function getEntCoursesData() {
  const params: any = {
    ent_id: entId.value || undefined,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getEntCourses(params)
    .then((res: any) => {
      if (res.status == 200) {
        courseData.value = res.data.ent_courses.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          item.published_at = parseTime(
            item.published_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          item.expired_at = item.package?.expired_at
            ? parseTime(item.package?.expired_at, "{y}-{m}-{d}")
            : "";

          return item;
        });
        total.value = res?.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function getEntStaff() {
  const params: any = {
    ent_id: entId.value || undefined,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getEntUsers(params).then((res: any) => {
    if (res.status == 200) {
      staffData.value = res.data.ent_users;
      total.value = res.total;
      loading.value = false;
    }
  });
}

function getEntTrainingData() {
  const params: any = {
    ent_id: entId.value || undefined,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getEntTrainingList(params).then((res: any) => {
    if (res.status == 200) {
      trainingData.value = res.data.ent_trainings.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        const fromRaw = item.from_at;
        const tillRaw = item.till_at;
        item.from_at = fromRaw
          ? parseTime(fromRaw, "{y}-{m}-{d} {h}:{i}:{s}")
          : null;
        item.till_at = tillRaw
          ? parseTime(tillRaw, "{y}-{m}-{d} {h}:{i}:{s}")
          : null;
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
function handleTab() {
  queryParams.search = null;
  queryParams.pageNum = 1;
  getData();
}
function showLicense() {
  dialogPreview.visible = true;
  previewImgUrl.value = formDetail.license;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
function handleBack() {
  router.go(-1);
}

function accountFilter(value: any) {
  let res: any = 0;
  if (value.package?.account) {
    res = value.package.account;
  }
  return res;
}
function assignedFilter(value: any) {
  let res: any = 0;
  if (value.package?.assigned) {
    res = value.package.assigned;
  }
  return res;
}
</script>

<style scoped lang="scss">
.ent-detail-container {
  // width: 50%;
  height: 95%;
  margin: 20px;

  .ent-description {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 271px;
    padding: 20px 20px 0;
    margin-bottom: 10px;
    background: linear-gradient(180deg, #fff 0%, #fafdfb 100%);
    border-radius: 8px;
    box-shadow: inset 0 0 50px 1px rgb(177 239 239 / 31%);

    .form-title {
      position: absolute;
      top: 15px;
      left: 40px;
      font-size: 22px;
      font-weight: 600;
      color: #3d4040;
    }

    .right-btn {
      position: absolute;
      top: 15px;
      right: 40px;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }

    .basic-form {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      min-height: 7rem;
      padding: 10px;
      padding: 5px 20px;
      margin: 15px 0;

      .col-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .item {
        display: flex;
        align-items: center !important;
        // justify-content: center;
        margin: 10px 0;
        text-align: center;

        .label {
          font-size: 18px;
          font-weight: 600;
          color: #3b4664;
          white-space: nowrap;
        }

        .text {
          overflow: hidden;
          font-size: 18px;
          color: #152b62;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .text-green {
          font-size: 20px;
          color: #00918c;
        }
      }

      .license-img {
        width: 120px;
        height: 120px;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .table-content {
    width: 100%;
    height: calc(100% - 271px);
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  }

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 240px);
    // height: calc(100% - 511px);
    padding: 10px 20px;

    .cover-name {
      display: flex;
      align-items: center;
      justify-content: center;

      .cover-img {
        width: 162px !important;
        height: 86px !important;
        object-fit: cover;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }

    .assigned-tag {
      display: inline-block;
      align-items: center;
      justify-content: center;
      padding: 5px 20px;
      font-size: 14px;
      font-weight: 500;
      color: #000;
      background: #b1dd8b;
      border-radius: 10px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.preview-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  :deep(.el-scrollbar__wrap) {
    max-height: 75vh;
  }

  img {
    width: 100%;
  }
}
</style>
