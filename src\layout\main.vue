<!--
 * @Author: czf <EMAIL>
 * @Date: 2024-10-30 15:08:17
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-12-24 11:11:56
 * @FilePath: \management-platform\src\layout\main.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts" setup>
import { computed, watchEffect } from "vue";
import { useWindowSize } from "@vueuse/core";
import {
  AppMain,
  Navbar,
  //  Settings, TagsView
} from "./components/index";
// import RightPanel from "@/components/RightPanel/index.vue";

import { useAppStore } from "@/store/modules/app";
import { useSettingsStore } from "@/store/modules/settings";
const { width } = useWindowSize();

/**
 * 响应式布局容器固定宽度
 *
 * 大屏（>=1200px）
 * 中屏（>=992px）
 * 小屏（>=768px）
 */
const WIDTH = 992;
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const fixedHeader = computed(() => settingsStore.fixedHeader);
// const showTagsView = computed(() => settingsStore.tagsView);
// const showSettings = computed(() => settingsStore.showSettings);
const layout = computed(() => settingsStore.layout);

const isRouterAlive = ref<boolean>(true);
// function reload() {
//   isRouterAlive.value = false;
//   nextTick(() => {
//     isRouterAlive.value = true;
//   });
// }
// provide("reload", reload);
// watchEffect(() => {
//   if (width.value < WIDTH) {
//     appStore.toggleDevice("mobile");
//     appStore.closeSideBar(true);
//   } else {
//     appStore.toggleDevice("desktop");

//     if (width.value >= 1200) {
//       //大屏
//       appStore.openSideBar(true);
//     } else {
//       appStore.closeSideBar(true);
//     }
//   }
// });
</script>
<template>
  <div :class="{ hasTagsView: true }" class="main-container">
    <el-scrollbar
      wrap-style="overflow-x: hidden"
      class="app-scrollbar"
      ref="scrollbarRef"
    >
      <div :class="{ 'fixed-header': fixedHeader, device: true }">
        <navbar v-if="layout === 'left'" />
        <!-- <tags-view v-if="showTagsView" /> -->
      </div>
      <!--主页面-->
      <app-main v-if="isRouterAlive" />
      <!-- 设置面板 -->
      <!-- <RightPanel v-if="showSettings">
      <settings />
    </RightPanel> -->
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.hideSidebar.mobile .fixed-header {
  width: 100%;
}

.isTop .fixed-header {
  width: 100% !important;
}

.isMix,
.isTop {
  .fixed-header {
    top: 80px;
  }
}
</style>
