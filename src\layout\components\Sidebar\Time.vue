<template>
  <div class="sidebar-time-container" :class="{ collapse: false }">
    <div class="sidebar-time">
      <p>{{ date }}</p>
      <p>
        <span>{{ week }}</span> <span>{{ minute }}</span
        >:<span>{{ second }}</span>
      </p>
    </div>
    <!-- <div v-else class="sidebar-time">
      <span>{{ minute }}</span>
    </div> -->
  </div>
</template>

<script setup lang="ts">
const timer = ref<any>();
const date = ref<any>();
const week = ref<any>();
const minute = ref<any>();
const second = ref<any>();
onMounted(() => {
  time();
});
function time() {
  const weekArr: any = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ];
  timer.value = setInterval(() => {
    const time: any = new Date();
    date.value =
      time.getFullYear() +
      "年" +
      parseInt(time.getMonth() + 1) +
      "月" +
      time.getDate() +
      "日";
    week.value = weekArr[time.getDay()];
    const hour: any =
      time.getHours() < 10 ? "0" + time.getHours() : time.getHours();
    const minute: any =
      time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes();
    const second: any =
      time.getSeconds() < 10 ? "0" + time.getSeconds() : time.getSeconds();
    minute.value = hour + ":" + minute;
    second.value = second;
  }, 1000);
}
</script>
<style lang="scss" scoped>
.sidebar-time-container {
  width: 100%;
  height: 56px;
  font-size: 0.75rem;

  z-index: 10;

  .sidebar-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  p {
    margin: 3px;

    text-align: center;
  }
}
</style>
