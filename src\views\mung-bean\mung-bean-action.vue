<template>
  <div class="bean-reconciliation-container">
    <div class="container-header">
      <div class="left">绿豆账单</div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="search-header">
      <div class="filter-row">
        <el-date-picker
          class="date-picker"
          v-model="queryParams.month"
          type="month"
          size="large"
          placeholder="请选择月份"
          @change="getData"
        />
      </div>
      <div class="filter-row">
        本月：增加 {{ countBean.total_added }} ，减少
        {{ countBean.total_deducted }}
      </div>
      <!-- <div class="filter-row">月初余额：{{ countBean.bop_balance }}</div>
      <div class="filter-row">月末余额：{{ countBean.eop_balance }}</div> -->
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="30">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="日期" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.date }}
          </template>
        </el-table-column>
        <el-table-column
          label="增加"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.total_added }}
          </template>
        </el-table-column>

        <el-table-column
          label="减少"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.total_deducted }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { getMungBean, settleMungBean } from "@/api/mung-bean";

import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MungBeanReconciliation",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  month: new Date(),
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref<any>([]);
const countBean = reactive<any>({
  total_added: 0,
  total_deducted: 0,
  bop_balance: 0,
  eop_balance: 0,
});

// // 弹窗对象
// const dialog = reactive({
//   visible: false,
//   type: "dialog",
//   width: "40%",
//   title: "",
// });

onMounted(() => {
  getData();
});

function getData() {
  loading.value = true;
  const params = {
    // page: queryParams.pageNum,
    // per_page: queryParams.pageSize,
    month: queryParams.month
      ? parseTime(queryParams.month, "{y}{m}")
      : undefined,
  };
  settleMungBean(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.settlements.map((item: any) => {
          item.date = parseTime(item.date, "{y}-{m}-{d}");
          return item;
        });
        Object.assign(countBean, {
          total_added: res.data.total_added,
          total_deducted: res.data.total_deducted,
          bop_balance: res.data.bop_balance,
          eop_balance: res.data.eop_balance,
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleQuery() {
  getData();
}

function close() {
  router.go(-1);
}
function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.bean-reconciliation-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .left {
      display: flex;
      justify-content: space-between;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      font-size: 20px;
      color: #3b4664;
    }
    .right {
      .btn {
        border-radius: 13px;
        width: 52px;
        height: 28px;
        //font-family: 苹方-简, 苹方-简;
        font-weight: 400;
        font-size: 13px;
      }
    }
  }
  .search-header {
    width: 100%;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0px 20px;
    .filter-row {
      :deep(.el-date-editor) {
        width: 300px !important;
      }
      margin-left: 5%;
      &:nth-child(1) {
        margin-left: 0;
      }
    }
  }
  .content {
    // height: calc(100% - 240px);
    height: calc(100% - 140px);
    width: 100%;
    padding: 10px 20px 10px 20px;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
</style>
