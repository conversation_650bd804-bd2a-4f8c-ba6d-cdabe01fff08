// mock/user.ts
var user_default = [
  {
    url: "/api/hello_world",
    method: "get",
    response: (request) => {
      return {
        msg: "hello world",
        headers: request.headers
      };
    }
  },
  {
    url: "/api/v1/auth/captcha",
    method: "get",
    response: () => {
      return {
        code: "00000",
        data: {
          captchaKey: "534b8ef2b0a24121bec76391ddd159f9",
          captchaBase64: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAkCAIAAADNSmkJAAAFKUlEQVR4Xu2ZXUwcVRiGV70wMWo08V5NvPXCrDbFaGpMaZW2hqQxaoiJTRsaMBCNSYtpa2JTKiFSelFa+Q/QZcMWqEhBlh+htbEpZhMrBQrlJ0hBywLLyrJ0WZbje3bqOvPNLHPWrDvdOE9ONmfe78zkzMs335wzWJhJQrBQweS/wTQ6QWgYHdoIOcecOe05O+t2WkutO+p2ZF3Ksg/YV9ZW6FATYajR3nveg60H9327r3O8c35lHgp+r05dPdJzBL73TPSQ8SaCKIxGLsPlop+K0JHrEkPuoT31e5qGmmjARACF0agYyGVNlyVm/pzZXrN9fHGcBkz0UBid+31u93i3XFFT80vN8cvHqWqih8Lo1NpUqS5vwh3vnd223VQ10UNh9NbyrcFQUK6oCawHUipSqGqiB83oBf+CXFGDMp1mS6OqiR4Ko7FexkpOrqhpHGw82nOUqiZ6KIzGrkRuorW0dJMmOy+hOCfYGzb2RBFv6HRO0gEJw/U7y+pgL1bwmTxexN6sZ31TdEwEhdG+gA+7EqyXpUO1uZH20cWL8hMTRt1N9tBXzCJrOIRoCPJpSO2RAp4HmtCdIfZ+2JWgEBN9LbR28seTGU0Zue1tMLp+YIAMSADzfvbkKX4/eb28j4YODiGin3heqmIlLja5hAUCu+nmGY3JWKvpMAlqNGgebsauBOvlqSX+JEx7p7EbTLen53XlzfmWUioqXikrc68Y8N2juJ/fyVsNChGHEE//rBANYWaZz+TRQqpLaBgNsPfDrgSpbS21YtV87IdjrlkX9JZbt5DOma2t9ITo5F+5glN22WwL/n+yDv00mw06orKxOqQ5+J04hhViwzAXETIcJDVm8uxZqktoGx2Nj9t43Wgaul/ERQiGQvtbWnDWgZYW9CXlQFjZ/7ciyHNn+Z2MexTimIeLz59TiIln0M1e+IbPpOAaDUnEYPTi6iqKxpbycs/qKo1tCslfKcffPn9enuMiPPY1vxO/ckeFQ4h46cdGqUWoidE/y54q5tPY5WDrGzQqIXot4BgchEE57e00IMCw2/1qZSVO/7SjA78o9INzcxsbrL+fnTnDDh9mmZn8F30oG1Hm+nABv5mQMopDS/h1HxtqTzWbABMe9sxpPoe9zezeOo1GELqWhPS8t46M0IAYHbdvR1aHbaOjbjfLz2eFhez6dba4yAfgF30o0BFVE8+Mjh/wFxPI+I5mAEHU6Ls+38vhTFwOBGhMDF8gkFpbC5ffsdv/uBs6dIj19dExEtARVXv9YNbop8NFY3aZ6gRRo+tu3IBHnzmdNCBMXldXJKPfL74WzWUJRE+coDUknqsOdZXQbAJYwluVTbOZI3Qt8GFzMwxyjo3RgBiN4fr+elXVpZGRLWXl6PdOTtJBSlBDUK/lnIrjOlrtqWYTQDJaF6FrTXu9sOa1ysrVoM5HVE1GFxZQcyJ/p+xzv6K/rbr6N6+XDpUBl0tKFIrbz78qWB6YnWFMCBld4XLBms+7df75ook/GNzb0GCV7U1Qfz9p64TyQWNjYD3qe9rj4SMJtQP3MyjSDPzWIRHPjH7X4YAvfXoPuyZf9Pbi3PcuXIh4mp3NllYC6XY79C+jl2o8PBipxjnBttn4MgMNnWgfcRJGPI2OL8hTj3LloIlmRicvBhiNykvecpqoa3RSY4DRcLAwyicuOepVR1JjgNFYHWONHL04czTX0UmNAUYD7Pr+xc4wqTHGaBb2OtZvHUmNYUazcA2J6etdUmOk0f8rTKMTxF91RG0D1SwYGwAAAABJRU5ErkJggg=="
        },
        msg: "\u4E00\u5207ok"
      };
    }
  },
  {
    url: "/api/v1/auth/login",
    method: "post",
    response: () => {
      return {
        code: "00000",
        data: {
          accessToken: "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImp0aSI6IjE2YWJkNTlkOTAxNzQwZDliYmI3ZjczODBhZDkyNzNhIiwidXNlcklkIjoyLCJ1c2VybmFtZSI6ImFkbWluIiwiZGVwdElkIjoxLCJkYXRhU2NvcGUiOjEsImF1dGhvcml0aWVzIjpbIlJPTEVfQURNSU4iXSwiZXhwIjoxNjkxMTAzMzgyfQ.P4cuIfmPepl3HuguhMS7NXn5a7IUPpsLbmtA_rHOhHk",
          tokenType: "Bearer",
          refreshToken: null,
          expires: null
        },
        msg: "\u4E00\u5207ok"
      };
    }
  },
  {
    url: "/api/v1/users/me",
    method: "get",
    response: () => {
      return {
        code: "00000",
        data: {
          userId: 2,
          nickname: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
          avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
          roles: ["ADMIN"],
          perms: [
            "sys:menu:delete",
            "sys:dept:edit",
            "sys:dict_type:add",
            "sys:dict:edit",
            "sys:dict:delete",
            "sys:dict_type:edit",
            "sys:menu:add",
            "sys:user:add",
            "sys:role:edit",
            "sys:dept:delete",
            "sys:user:edit",
            "sys:user:delete",
            "sys:user:reset_pwd",
            "sys:dept:add",
            "sys:role:delete",
            "sys:dict_type:delete",
            "sys:menu:edit",
            "sys:dict:add",
            "sys:role:add"
          ]
        },
        msg: "\u4E00\u5207ok"
      };
    }
  },
  {
    url: "/api/v1/auth/logout",
    method: "delete",
    response: () => {
      return {
        code: "00000",
        data: {},
        msg: "string"
      };
    }
  },
  {
    url: "/api/v1/users/page",
    method: "get",
    response: () => {
      return {
        code: "00000",
        data: {
          list: [
            {
              id: 2,
              username: "admin",
              nickname: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
              mobile: "17621210366",
              genderLabel: "\u7537",
              avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
              email: null,
              status: 1,
              deptName: "\u6709\u6765\u6280\u672F",
              roleNames: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
              createTime: "2019-10-10"
            },
            {
              id: 3,
              username: "test",
              nickname: "\u6D4B\u8BD5\u5C0F\u7528\u6237",
              mobile: "17621210366",
              genderLabel: "\u7537",
              avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
              email: null,
              status: 1,
              deptName: "\u6D4B\u8BD5\u90E8\u95E8",
              roleNames: "\u8BBF\u95EE\u6E38\u5BA2",
              createTime: "2021-06-04"
            }
          ],
          total: 2
        },
        msg: "\u4E00\u5207ok"
      };
    }
  },
  {
    url: "/api/v1/users/:id/form",
    method: "get",
    response: ({ url }) => {
      const id = url.match(/\/api\/v1\/users\/(\d+)\/form/)[1];
      let formData = null;
      if (id == 2) {
        formData = {
          id: 2,
          username: "admin",
          nickname: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
          mobile: "17621210366",
          gender: 1,
          avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
          email: "",
          status: 1,
          deptId: 1,
          roleIds: [2]
        };
      } else if (id == 3) {
        formData = {
          id: 3,
          username: "test",
          nickname: "\u6D4B\u8BD5\u5C0F\u7528\u6237",
          mobile: "17621210366",
          gender: 1,
          avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
          email: "<EMAIL>",
          status: 1,
          deptId: 3,
          roleIds: [3]
        };
      }
      return {
        code: "00000",
        data: formData,
        msg: "\u4E00\u5207ok"
      };
    }
  }
];
export {
  user_default as default
};
//# sourceMappingURL=data:application/json;base64,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
