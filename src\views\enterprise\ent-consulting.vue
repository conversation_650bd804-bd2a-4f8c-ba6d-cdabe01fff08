<template>
  <div class="ent-consulting-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <
          :suffix-icon="`CaretBottom`"
            size="large"
            v-model="query.status"
            placeholder="请选择状态"
            filterable
            clearable
            @change="handleChange"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </>
        </div> -->
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="企业名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="姓名" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.contact || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="联系方式" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.contact_no || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="企业规模" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.ent_size || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          label="申请时间"
          align="center"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.created_at || "--" }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { getEnterprises } from "@/api/enterprise";
import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "EntConsulting",
  inheritAttrs: false,
});
/** 仓库*/
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  status: 50,
});
const sizeOptions = reactive<any>(["1-30", "31-50", "51-100", "100人以上"]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "待审核" },
  20: { type: "success", label: "正常" },
  30: { type: "info", label: "冻结" },
  40: { type: "danger", label: "注销" },
  50: { type: "warning", label: "咨询中" },
  60: { type: "error", label: "审核不通过" },
});
const total = ref(0); // 数据总数
const tableData = ref([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "55%",
  title: "详情",
});

// 用户表单数据
const rowId = ref();
const formData = ref<any>([]);

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function getData() {
  loading.value = true;
  const params = {
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    search: queryParams.search || undefined,
    status: 50,
  };
  getEnterprises(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.enterprises.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}
function handleList() {
  router.push({
    path: "bean-reconciliation",
    query: { type: "reconciliation" },
  });
}
function onRowClick(type: any, row: any) {
  if (type == "detail") {
    dialog.visible = true;
    dialog.title = "详情";
    dialog.type = "detail";
    formData.value = row.bg_cons;
    rowId.value = row.id;
  }
}

function closeDialog() {
  dialog.visible = false;
}
</script>

<style scoped lang="scss">
.ent-consulting-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .after-header {
    width: 100%;
    padding: 20px 20px 20px 20px;
  }
  .container-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 20px 20px;
    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 60%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      .btn {
        width: 120px;
        height: 38px;
      }
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
  }
  .content {
    // height: calc(100% - 240px);
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
    .cover-name {
      display: flex;
      align-items: center;
      .cover-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }
      span {
        width: 40%;
        display: inline-block;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
.dialog {
  .dialog-body {
    height: 60vh;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-top: 15px;
  }
}
</style>
