<script lang="ts" setup>
import { isExternal } from "@/utils/index";
import { useRoute, useRouter } from "vue-router";

import { useAppStore } from "@/store/modules/app";

const appStore = useAppStore();

// const sidebar = computed(() => appStore.sidebar);
// const device = computed(() => appStore.device);

const props = defineProps({
  to: {
    type: String,
    required: true,
  },
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
function push() {
  // if (device.value === "mobile" && sidebar.value.opened == true) {
  //   appStore.closeSideBar(false);
  // }
  needRedirect(props.to); //二次点击目录后采用路由重定向刷新路由

  // router.push({
  //   path: props.to,
  //   query: {
  //     t: +new Date(), //保证每次点击路由的query项都是不一样的，确保会重新刷新view，但是带的query不好看
  //   },
  // });

  // router.push(props.to).catch((err) => {
  //   console.error(err);
  // });
}
function needRedirect(to: any) {
  //点击目录刷新
  if (to === route.path) {
    const { fullPath } = route;
    router.replace({
      path: "/redirect" + fullPath,
    });
  } else {
    router.push(props.to).catch((err) => {
      console.error(err);
    });
  }
}
</script>

<template>
  <a v-if="isExternal(to)" :href="to" target="_blank" rel="noopener">
    <slot></slot>
  </a>
  <div v-else @click="push">
    <slot></slot>
  </div>
</template>
