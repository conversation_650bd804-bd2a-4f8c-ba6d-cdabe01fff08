<template>
  <div class="exam-logs-table-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>

        <!-- <div class="filter-row">
          <el-cascader
            v-model="queryParams.dptid"
            :props="depProps"
            :options="depOptions"
            placeholder="请选择部门"
            clearable
            filterable
            size="large"
            @change="handleQuery"
            :suffix-icon="`CaretBottom`"
          />
        </div> -->
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.type"
            placeholder="请选择课程性质"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in courseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.type"
            placeholder="请选择考试类型"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in courseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.grade"
            placeholder="请选择等级"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in gradesOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="40">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="50">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试时间" align="center" min-width="30">
          <template #default="scope">
            {{ scope.row.finished_at || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="成绩" align="center" min-width="30">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试次数" align="center" min-width="30">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="等级" align="center" min-width="30">
          <template #default="scope">
            <span :style="{ color: gradesMap[scope.row.status]?.color }">
              {{ gradesMap[scope.row.status]?.label }}</span
            >
          </template>
        </el-table-column>

        <el-table-column label="完成时间" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.finished_at || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="40">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn green-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn orange-btn"
                @click="onRowClick('reset', scope.row)"
              >
                重置
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="exam-log-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #header>
        <div class="dialog-title" :class="{ 'has-bg': dialog.type == 'reset' }">
          {{ dialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="exam-content" v-if="dialog.type == 'detail'">
          <el-scrollbar
            class="exam-detail-scrollbar"
            style="width: 100%; height: 100%"
            warp-style="overflow-x: hidden;"
          >
            <div class="exam-text">1</div>
            <div class="exam-info">1</div>
            <div class="exam-questions">1</div>
          </el-scrollbar>
        </div>
        <div class="reset-content" v-if="dialog.type == 'reset'">
          <div class="text">
            重置后将清除学员当前成绩和考试记录，请确认是否重置？
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="btn cancel-btn"
            @click="closeDialog"
            v-if="dialog.type == 'reset'"
          >
            取 消
          </div>
          <div class="btn primary-btn" @click="confirmDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { getCourses, getCoursesDetail } from "@/api/course";

import { parseTime, numberToChinese, containsAllElements } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";

import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "ExamLogs",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  courseId: {
    type: String,
    default: "",
  },
  courseDetail: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(false);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  status: null,
});

const courseId = ref<any>(props.courseId);
const courseDetail = reactive<any>(props.courseDetail);
const total = ref(0); // 数据总数
const tableData = ref<any>([
  {
    id: 1,
    user: { name: "张三" },
    finished_at: "2024-05-01 10:00:00",
    score: 95,
    exam_count: 1,
    status: 30, // 优秀
  },
  {
    id: 2,
    user: { name: "李四" },
    finished_at: "2024-05-03 14:30:00",
    score: 85,
    exam_count: 2,
    status: 20, // 良好
  },
  {
    id: 3,
    user: { name: "王五" },
    finished_at: "2024-05-10 09:20:00",
    score: 75,
    exam_count: 1,
    status: 10, // 及格
  },
  {
    id: 4,
    user: { name: "赵六" },
    finished_at: "2024-05-15 16:45:00",
    score: 55,
    exam_count: 3,
    status: 40, // 不及格
  },
]);

const gradesMap = reactive<any>({
  // 不及格，及格，良好，优秀
  10: { type: "info", label: "及格", color: "#909399" },
  20: { type: "primary", label: "良好", color: "#409eff" },
  30: { type: "success", label: "优秀", color: "#2ab7b0" },
  40: { type: "danger", label: "不及格", color: "#f56c6c" },
  // 30: { type: "warning", label: "已过期", color: "#e6a23c" },
});
const gradesOptions = ref<any>([
  { value: 10, label: "及格" },
  { value: 20, label: "良好" },
  { value: 30, label: "优秀" },
  { value: 40, label: "不及格" },
]);
// 考试状态，10-进行中，20-完成，30-超时，40-待移除
const examStatusOptions = ref<any>([
  { value: "10", label: "进行中" },
  { value: "20", label: "完成" },
  { value: "30", label: "超时" },
  { value: "40", label: "待移除" },
]);
const examStatusMap = reactive<any>({
  "10": { type: "info", label: "进行中", color: "#409eff" },
  "20": { type: "primary", label: "完成", color: "#2ab7b0" },
  "30": { type: "success", label: "超时", color: "#e6a23c" },
  "40": { type: "danger", label: "待移除", color: "#f56c6c" },
});
// 课程性质
const courseTypeOptions = ref<any>([
  { value: "1", label: "必修" },
  { value: "0", label: "选修" },
]);
const courseTypeMap = reactive<any>({
  true: { type: "primary", label: "必修", color: "#409eff" },
  false: { type: "success", label: "选修", color: "#2ab7b0" },
});

//考试类型
const examTypeOptions = ref<any>([]);
const examTypeMap = reactive<any>({
  // true: { type: "primary", label: "--", color: "#409eff" },
  // false: { type: "success", label: "--", color: "#2ab7b0" },
});

const depOptions = ref<any>([]);
const depProps = reactive<any>({
  checkStrictly: true,
});

const dialog = reactive<any>({
  visible: false,
  title: "学习明细",
  width: "40%",
  type: "",
});
const rowId = ref<any>(null);
const detailTableData = ref<any>([]);

onBeforeMount(() => {});
onMounted(() => {
  getData();
});

function optionsLoop(val: any) {
  const res = {
    label: val.name,
    value: val.id,
    children: val.children?.map(optionsLoop) ?? [],
  };
  return res;
}
function getDepts() {
  loading.value = true;
  const params = {
    per_page: 9999,
    page: 1,
  };
  // getDepartments(params).then((res: any) => {
  //   depOptions.value = res.data.departments.map(optionsLoop);
  // });
  loading.value = false;
}
function getExams() {
  loading.value = true;
  const params = {
    per_page: 9999,
    page: 1,
  };
  // getExams(params).then((res: any) => {
  //   examTypeOptions.value = res.data.exams.map(optionsLoop);
  // });
  loading.value = false;
}
function getData() {
  loading.value = true;
  const params = {
    course_id: courseId.value,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.type || undefined,
    status: queryParams.status,
  };
  loading.value = false;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  if (type == "detail") {
    dialog.visible = true;
    dialog.title = row.ent_user?.name + "考试记录";
    dialog.width = "40%";
    dialog.type = "detail";
    const params: any = {
      ent_user_id: row.ent_user.id,
      ent_training_id: courseId.value,
    };
    // getUserCourseDetail(params).then((res: any) => {
    //   // 拍平成表格数据
    //   detailTableData.value = res.data.flatMap((phase, index) =>
    //     phase.content.map((course) => ({
    //       id: phase.id,
    //       name: phase.name ? phase.name : `第${numberToChinese(index)}阶段`,
    //       course,
    //     }))
    //   );
    // });
  }
  if (type == "reset") {
    // 重置考试
    dialog.visible = true;
    dialog.title = "重置考试";
    dialog.width = "21.5%";
    dialog.type = "reset";
  }
}
function handleResetExam() {
  const params = {};
  ElMessageBox.confirm("此操作将重置该用户的考试，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // resetUserExam(params).then((res:any) => {
    // if (res.status == 200) {
    //   ElMessage.success({
    //     message: "重置成功!",
    //   });
    //   getData();
    // }
    // });
  });
}
function expiredAtFilter(value: any) {
  let res: any = "--";

  if (value.package?.expired_at) {
    res = parseTime(value.package.expired_at, "{y}-{m}-{d}");
  }
  return res;
}

function confirmDialog() {
  if (dialog.type == "reset") {
    handleResetExam();
  } else {
    closeDialog();
  }
}
function closeDialog() {
  dialog.visible = false;
}
</script>

<style scoped lang="scss">
.exam-logs-table-container {
  width: 100%;
  height: 100%;

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        border-radius: 2px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .export-btn {
        width: 116px;
        height: 40px;
        background: linear-gradient(180deg, #0ebc72 0%, #20c27c 100%);
        border-radius: 2px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 120px);
    padding: 10px 20px;

    :deep(.caret-wrapper) {
      transform: scale(1.5) !important;
    }

    .progress {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      .line {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .progress-line {
      width: 146px;
      height: 9px;
      margin-right: 10px;
      background: #7072787b;
      border-radius: 6px;
      box-shadow: 0 0 2px 1px rgb(55 134 104 / 16%),
        inset 0 0 4px 1px rgb(0 0 0 / 7%);

      .progress-bar {
        height: 9px;
        border-radius: 6px 0 0 6px;
      }

      .full-bar {
        border-radius: 6px;
      }

      .free-bar {
        // background: linear-gradient(90deg, #2abc7e 0%, #95dfe0 100%);
        background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%);
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
<style lang="scss">
.exam-log-dialog {
  padding: 0 !important;

  .el-dialog__header.show-close {
    padding: 0 !important;
  }

  .el-dialog__header {
    height: 88px;
    padding: 10px !important;
    text-align: center;
    // background: url("@/assets/images/dialog-header-orange.png") no-repeat;
    // background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;
    }

    .has-bg {
      background: url("@/assets/images/dialog-header-orange.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;

    svg {
      font-size: 20px;

      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .exam-content {
    height: 20rem;
  }

  .reset-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 500;
    color: #3b4664;
  }
}
</style>
