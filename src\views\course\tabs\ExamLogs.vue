<template>
  <div class="exam-logs-table-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>

        <!-- <div class="filter-row">
          <el-cascader
            v-model="queryParams.dptid"
            :props="depProps"
            :options="depOptions"
            placeholder="请选择部门"
            clearable
            filterable
            size="large"
            @change="handleQuery"
            :suffix-icon="`CaretBottom`"
          />
        </div> -->
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.type"
            placeholder="请选择课程性质"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in courseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.type"
            placeholder="请选择考试类型"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in courseTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.grade"
            placeholder="请选择等级"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in gradesOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="40">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="50">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试时间" align="center" min-width="30">
          <template #default="scope">
            {{ scope.row.finished_at || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="成绩" align="center" min-width="30">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试次数" align="center" min-width="30">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="等级" align="center" min-width="30">
          <template #default="scope">
            <span :style="{ color: gradesMap[scope.row.status]?.color }">
              {{ gradesMap[scope.row.status]?.label }}</span
            >
          </template>
        </el-table-column>

        <el-table-column label="完成时间" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.finished_at || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="40">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn green-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn orange-btn"
                @click="onRowClick('reset', scope.row)"
              >
                重置
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="exam-log-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #header>
        <div class="dialog-title" :class="{ 'has-bg': dialog.type == 'reset' }">
          {{ dialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="exam-content" v-if="dialog.type == 'detail'">
          <el-scrollbar
            class="exam-detail-scrollbar"
            style="width: 100%; height: 100%"
            warp-style="overflow-x: hidden;"
          >
            <div class="exam-text">
              <div class="left">
                <!--  -->
              </div>
              <div class="right">
                <span
                  >共 {{ examDetail.answer_stat.total }} 题 | 总分
                  {{ examDetail.exam.scores }} 分 | 限时
                  {{ examDetail.exam.time_limit }} 分钟</span
                >
              </div>
            </div>
            <div class="exam-info">
              <div class="score-info">
                <div
                  class="score-content"
                  :class="{
                    'score-bg-pass': examDetail.scores >= 60,
                    'score-bg-fail': examDetail.scores < 60,
                  }"
                >
                  <div
                    class="score-item num-font-type"
                    :class="{
                      correct: examDetail.scores >= 60,
                      wrong: examDetail.scores < 60,
                    }"
                  >
                    {{ examDetail.scores }}
                  </div>
                  <div class="total">卷面分数 {{ examDetail.exam.scores }}</div>
                </div>
              </div>
              <div class="paper-info">
                <div class="paper-time">
                  <div class="item">
                    <span>考试时间：{{ examDetail.taken_at }}</span>
                  </div>
                  <div class="item">
                    <span
                      >考试时长：{{
                        getExamDuration(
                          examDetail.taken_at,
                          examDetail.submit_at
                        )
                      }}</span
                    >
                  </div>
                  <div class="item">
                    <span>交卷时间：{{ examDetail.submit_at }}</span>
                  </div>
                </div>
                <div class="paper-answer">
                  <div class="item">
                    <span> 正确</span>
                    <span class="correct num-font-type">
                      {{ examDetail.answer_stat.correct }}</span
                    >
                  </div>
                  <div class="line"></div>
                  <div class="item">
                    <span> 错误</span>
                    <span class="wrong num-font-type">
                      {{ examDetail.answer_stat.wrong }}</span
                    >
                  </div>
                </div>
              </div>
              <div class="level-tag">
                <img :src="gradesMap[examDetail.grade].img" alt="" />
              </div>
            </div>
            <div class="exam-questions-page">
              <!--  -->
              <el-pagination
                background
                layout="prev, pager, next"
                :current-page="questionCurrentPage"
                :page-size="questionPageSize"
                :total="questionTotal"
                @current-change="handleQuestionPageChange"
              />
            </div>
            <div class="exam-questions">
              <div class="question-tabs" ref="questionTabs">
                <div
                  v-for="item in questionTabOptions"
                  :key="item.value"
                  :class="[
                    'tab-item',
                    item.value,
                    { active: activeQuestionTab === item.value },
                  ]"
                  @click="handleQuestionTab(item.value)"
                >
                  {{ item.label }}
                  {{
                    item.value === "all"
                      ? `（${item.count}）`
                      : `：${item.count}`
                  }}
                </div>
              </div>
              <div class="question-content">
                <el-scrollbar
                  style="height: 100%; overflow-x: hidden; width: 100%"
                >
                  <!-- :questionData="examDetail.paper[activeQuestionTab]" -->
                  <ExamPaper
                    :isOperable="false"
                    :showAnswerType="activeQuestionTab"
                    :showAnalysis="true"
                    :showAnswer="true"
                    :showSelectedAnswer="true"
                    :showQuestionScore="true"
                    :showGetScore="true"
                    :showAnswerTime="true"
                    :paperData="examDetail"
                  />
                </el-scrollbar>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <div class="reset-content" v-if="dialog.type == 'reset'">
          <div class="text">
            重置后将清除学员当前成绩和考试记录，请确认是否重置？
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="btn cancel-btn"
            @click="closeDialog"
            v-if="dialog.type == 'reset'"
          >
            取 消
          </div>
          <div class="btn primary-btn" @click="confirmDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";

import { parseTime, numberToChinese, containsAllElements } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
import ExamPaper from "@/views/exam/components/ExamPaper.vue";
import { getUserExams, updateUserExam, getUserExamDetail } from "@/api/exam";

import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { active } from "sortablejs";
import { fa } from "element-plus/es/locale";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "ExamLogs",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  courseId: {
    type: String,
    default: "",
  },
  courseDetail: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(false);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  status: null,
});

const courseId = ref<any>(props.courseId);
const courseDetail = reactive<any>(props.courseDetail);
const total = ref(0); // 数据总数
const tableData = ref<any>([
  {
    id: 1,
    user: { name: "张三" },
    finished_at: "2024-05-01 10:00:00",
    score: 95,
    exam_count: 1,
    status: 30, // 优秀
  },
  {
    id: 2,
    user: { name: "李四" },
    finished_at: "2024-05-03 14:30:00",
    score: 85,
    exam_count: 2,
    status: 20, // 良好
  },
  {
    id: 3,
    user: { name: "王五" },
    finished_at: "2024-05-10 09:20:00",
    score: 75,
    exam_count: 1,
    status: 10, // 及格
  },
  {
    id: 4,
    user: { name: "赵六" },
    finished_at: "2024-05-15 16:45:00",
    score: 55,
    exam_count: 3,
    status: 40, // 不及格
  },
]);

const gradesMap = reactive<any>({
  // 不及格，及格，良好，优秀
  10: {
    label: "及格",
    color: "#7EBFAE",
    img: new URL("@/assets/exam/pass-tag.png", import.meta.url).href,
  },
  20: {
    label: "良好",
    color: "#2ABF7E",
    img: new URL("@/assets/exam/good-tag.png", import.meta.url).href,
  },
  30: {
    label: "优秀",
    color: "#23A19A",
    img: new URL("@/assets/exam/excellent-tag.png", import.meta.url).href,
  },
  40: {
    label: "不及格",
    color: "#EA844F",
    img: new URL("@/assets/exam/fail-tag.png", import.meta.url).href,
  },
  50: {
    label: "未完成",
    color: "#3B4664",
    img: new URL("@/assets/exam/undone-tag.png", import.meta.url).href,
  },
});

const gradesOptions = ref<any>([
  { value: 10, label: "及格", en: "pass" },
  { value: 20, label: "良好", en: "good" },
  { value: 30, label: "优秀", en: "excellent" },
  { value: 40, label: "不及格", en: "fail" },
  { value: 50, label: "未完成", en: "undone" },
]);
// 考试状态，10-进行中，20-完成，30-超时，40-待移除
const examStatusOptions = ref<any>([
  { value: "10", label: "进行中" },
  { value: "20", label: "完成" },
  { value: "30", label: "超时" },
  { value: "40", label: "待移除" },
]);
const examStatusMap = reactive<any>({
  "10": { type: "info", label: "进行中", color: "#409eff" },
  "20": { type: "primary", label: "完成", color: "#2ab7b0" },
  "30": { type: "success", label: "超时", color: "#e6a23c" },
  "40": { type: "danger", label: "待移除", color: "#f56c6c" },
});
// 课程性质
const courseTypeOptions = ref<any>([
  { value: "1", label: "必修" },
  { value: "0", label: "选修" },
]);
const courseTypeMap = reactive<any>({
  true: { type: "primary", label: "必修", color: "#409eff" },
  false: { type: "success", label: "选修", color: "#2ab7b0" },
});

//考试类型
const examTypeOptions = ref<any>([]);
const examTypeMap = reactive<any>({
  // true: { type: "primary", label: "--", color: "#409eff" },
  // false: { type: "success", label: "--", color: "#2ab7b0" },
});

const depOptions = ref<any>([]);
const depProps = reactive<any>({
  checkStrictly: true,
});

const dialog = reactive<any>({
  visible: false,
  title: "学习明细",
  width: "40%",
  type: "",
});
const examDetail = reactive<any>({
  scores: 120,
  taken_at: "2024/11/15 11:29:00", //考试时间
  submit_at: "2024/11/15 12:01:16", //交卷时间
  taken_times: "", //考试次数
  times_limit: "", //次数限制
  grade: "20", //成绩等级，比如：优秀
  exam: {
    scores: 120,
    time_limit: 30,
  },
  answer_stat: {
    total: 4,
    correct: 3,
    wrong: 1,
  },
});
const rowId = ref<any>(null);
const activeQuestionTab = ref<any>("all");
const questionTabOptions = ref<any>([
  {
    value: "all",
    label: "全部",
    count: 4,
  },
  {
    value: "correct",
    label: "正确",
    count: 3,
  },
  {
    value: "wrong",
    label: "错误",
    count: 1,
  },
]);
const questionCurrentPage = ref(1);
const questionPageSize = ref(10);
const questionTotal = ref(10);

function handleQuestionPageChange(page: number) {
  questionCurrentPage.value = page;
}

onBeforeMount(() => {});
onMounted(() => {
  getData();
});

function optionsLoop(val: any) {
  const res = {
    label: val.name,
    value: val.id,
    children: val.children?.map(optionsLoop) ?? [],
  };
  return res;
}
function getDepts() {
  loading.value = true;
  const params = {
    per_page: 9999,
    page: 1,
  };
  // getDepartments(params).then((res: any) => {
  //   depOptions.value = res.data.departments.map(optionsLoop);
  // });
  loading.value = false;
}
function getExams() {
  loading.value = true;
  const params = {
    per_page: 9999,
    page: 1,
  };
  // getExams(params).then((res: any) => {
  //   examTypeOptions.value = res.data.exams.map(optionsLoop);
  // });
  loading.value = false;
}
function getExamDuration(taken_at: string, submit_at: string) {
  if (!taken_at || !submit_at) return "--";
  const start = new Date(taken_at.replace(/-/g, "/"));
  const end = new Date(submit_at.replace(/-/g, "/"));
  let diff = Math.max(0, end.getTime() - start.getTime()) / 1000; // 秒
  const hour = Math.floor(diff / 3600);
  diff = diff % 3600;
  const min = Math.floor(diff / 60);
  const sec = Math.floor(diff % 60);
  // 补零函数
  const pad = (n: number) => n.toString().padStart(2, "0");

  // if (hour > 0) {
  //   return `${pad(hour)}:${pad(min)}:${pad(sec)}`;
  // }
  // return `${pad(min)}:${pad(sec)}`;
  return `${pad(hour)}:${pad(min)}:${pad(sec)}`;
}
function getData() {
  loading.value = true;
  const params = {
    course_id: courseId.value,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.type || undefined,
    status: queryParams.status,
  };
  loading.value = false;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  if (type == "detail") {
    dialog.visible = true;
    dialog.title = row.ent_user?.name || "--" + "考试记录";
    dialog.width = "40%";
    dialog.type = "detail";
    // const params: any = {
    //   ent_user_id: row.ent_user.id,
    //   ent_training_id: courseId.value,
    // };
    // getUserCourseDetail(params).then((res: any) => {
    //   // 拍平成表格数据
    //   detailTableData.value = res.data.flatMap((phase, index) =>
    //     phase.content.map((course) => ({
    //       id: phase.id,
    //       name: phase.name ? phase.name : `第${numberToChinese(index)}阶段`,
    //       course,
    //     }))
    //   );
    // });
  }
  if (type == "reset") {
    // 重置考试
    dialog.visible = true;
    dialog.title = "重置考试";
    dialog.width = "21.5%";
    dialog.type = "reset";
  }
}
function handleResetExam() {
  const params = {};
  ElMessageBox.confirm("此操作将重置该用户的考试，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // resetUserExam(params).then((res:any) => {
    // if (res.status == 200) {
    //   ElMessage.success({
    //     message: "重置成功!",
    //   });
    //   getData();
    // }
    // });
  });
}

function handleQuestionTab(tab: any) {
  activeQuestionTab.value = tab;
}
function confirmDialog() {
  if (dialog.type == "reset") {
    handleResetExam();
  } else {
    closeDialog();
  }
}
function closeDialog() {
  dialog.visible = false;
}
</script>

<style scoped lang="scss">
.exam-logs-table-container {
  width: 100%;
  height: 100%;

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        border-radius: 2px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .export-btn {
        width: 116px;
        height: 40px;
        background: linear-gradient(180deg, #0ebc72 0%, #20c27c 100%);
        border-radius: 2px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 120px);
    padding: 10px 20px;

    :deep(.caret-wrapper) {
      transform: scale(1.5) !important;
    }

    .progress {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      .line {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .progress-line {
      width: 146px;
      height: 9px;
      margin-right: 10px;
      background: #7072787b;
      border-radius: 6px;
      box-shadow: 0 0 2px 1px rgb(55 134 104 / 16%),
        inset 0 0 4px 1px rgb(0 0 0 / 7%);

      .progress-bar {
        height: 9px;
        border-radius: 6px 0 0 6px;
      }

      .full-bar {
        border-radius: 6px;
      }

      .free-bar {
        // background: linear-gradient(90deg, #2abc7e 0%, #95dfe0 100%);
        background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%);
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
<style lang="scss">
.exam-log-dialog {
  padding: 0 !important;

  .el-dialog__header.show-close {
    padding: 0 !important;
  }

  .el-dialog__header {
    height: 88px;
    padding: 10px !important;
    text-align: center;
    // background: url("@/assets/images/dialog-header-orange.png") no-repeat;
    // background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;
    }

    .has-bg {
      background: url("@/assets/images/dialog-header-orange.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .el-dialog__body {
    padding: 0 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;

    svg {
      font-size: 20px;

      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .reset-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 500;
    color: #3b4664;
  }

  .exam-content {
    width: 100%;
    // height: 65vh;

    .exam-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      font-size: 16px;
      font-weight: 400;
      color: #3b4664;
    }

    .exam-info {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 100%;
      height: 244px;
      margin: 10px 0;
      font-size: 16px;
      font-weight: 400;
      color: #3b4664;
      border: 1px solid #fff;
      border-radius: 8px;
      box-shadow: 0px 0px 6px 1px rgba(191, 226, 206, 0.08),
        inset 0px 0px 10px 1px rgba(19, 69, 65, 0.2);
    }

    .num-font-type {
      font-family: "Fugaz One", sans-serif !important;
    }

    .score-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 35%;

      .score-content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 185px;
        height: 187px;
        .score-item {
          // font-size: 80px;
          font-size: 70px;
          font-weight: 400;
          text-align: center;
          font-style: normal;
          text-transform: none;
          // color: #00918c;
          line-height: 1;
        }
        .total {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 115px;
          height: 25px;
          font-size: 14px;
          font-weight: 400;
          color: #3b4664;
          background: linear-gradient(180deg, #c1edc1 0%, #7ebfae 100%);
          border-radius: 11px;
        }
      }
      .score-bg-pass {
        background: url("@/assets/exam/score-pass-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .score-bg-fail {
        background: url("@/assets/exam/score-fail-bg.png") no-repeat;
        background-size: 100% 100%;
      }
    }

    .paper-info {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 62%;
      height: 146px;

      .paper-time {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
        font-size: 16px;
        font-weight: 400;
        color: #3b4664;
        .item {
          display: flex;
          align-items: center;
          padding: 0 10px;
          margin-bottom: 3px;
          background: #fff;
          border: 1px solid #fff;
          position: relative;
          width: 280px;
          height: 46px;
          // 使用伪元素做背景并设置透明度，内容不受影响
          &::before {
            position: absolute;
            inset: 0;
            z-index: 0;
            pointer-events: none;
            content: "";
            background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
            border-radius: 11px;
            box-shadow: 0px 0px 6px 1px rgba(191, 226, 206, 0.08),
              inset 0px 0px 10px 1px rgba(19, 69, 65, 0.2);
            opacity: 0.49;
          }

          > * {
            position: relative;
            z-index: 1;
          }
        }

        .item:last-child {
          margin-bottom: 0;
        }
      }

      .paper-answer {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        position: relative;

        // 使用伪元素做背景并设置透明度，内容不受影响
        &::before {
          position: absolute;
          inset: 0;
          z-index: 0;
          pointer-events: none;
          content: "";
          background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
          border-radius: 11px;
          box-shadow: 0px 0px 6px 1px rgba(191, 226, 206, 0.08),
            inset 0px 0px 10px 1px rgba(19, 69, 65, 0.2);
          opacity: 0.49;
        }

        > * {
          position: relative;
          z-index: 1;
        }
        .line {
          height: 1px;
          width: calc(100% - 26px);
          background: hsla(200, 6%, 42%, 0.44);
          margin: 5px 0;
        }
        .item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 144px;
          font-weight: bold;
          font-size: 17px;
          color: #3b4664;

          .num-font-type {
            margin-left: 10px;
            font-size: 40px;
            font-weight: 400;
          }
        }
      }
    }
    .correct {
      color: #00918c;
    }
    .wrong {
      color: #f06e15;
    }
    .level-tag {
      width: 58px;
      height: 54px;
      position: absolute;
      top: 0;
      right: 28px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .exam-questions-page {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding-bottom: 10px;
      .el-pagination {
        margin: 0 auto;
        .el-pager li.is-active {
          background: #00918c;
          color: #fff;
          border-radius: 4px;
        }
      }
    }

    .exam-questions {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      // height: 421px;
      height: 320px;
      border: 1px solid #fff;
      box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%),
        inset 0 0 10px 1px rgb(19 69 65 / 20%);

      .question-tabs {
        display: flex;
        width: 100%;
        height: 40px;
        overflow: hidden;
        background: #eaf2fa;
        border-radius: 4px 4px 0 0;

        .tab-item {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          font-size: 15px;
          font-weight: 400;
          color: #3b4664;
          cursor: pointer;
          background: linear-gradient(180deg, #e7ecf0 0%, #cbccce 100%);
          border-right: 1px solid #dbe6f3;
          transition: background 0.2s, color 0.2s;

          &:last-child {
            border-right: none;
          }
        }

        .tab-item.active {
          color: #fff;
          background: #00918c;
        }
      }

      .question-content {
        flex: 1;
        width: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
