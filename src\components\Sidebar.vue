<template>
  <div class="sidebar-container">
    <!-- 侧边栏内容 -->
    <div class="sidebar-content">
      <!-- 体能分组 -->
      <div class="sidebar-group">
        <div class="group-header" @click="toggleGroup('physical')">
          <el-icon
            class="expand-icon"
            :class="{ expanded: expandedGroups.physical }"
          >
            <Plus />
          </el-icon>
          <span class="group-title">体能</span>
          <el-icon class="delete-icon" @click.stop="deleteGroup('physical')">
            <Delete />
          </el-icon>
        </div>
        <div v-show="expandedGroups.physical" class="group-content">
          <div class="group-item">
            <span class="item-text">主要各种主题的各种主要名称</span>
            <el-icon class="delete-icon" @click="deleteItem('physical', 0)">
              <Delete />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 输入新分组 -->
      <div class="sidebar-group">
        <div class="group-header">
          <el-icon class="expand-icon">
            <Plus />
          </el-icon>
          <el-input
            v-model="newGroupName"
            placeholder="输入新分组"
            class="group-input"
            @keyup.enter="addNewGroup"
            @blur="addNewGroup"
          />
          <el-icon class="delete-icon" @click="clearNewGroup">
            <Delete />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="sidebar-footer">
      <div class="footer-buttons">
        <el-button type="primary" @click="handleNewAdd">新增主题</el-button>
        <el-button @click="handleSave">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 展开状态
const expandedGroups = reactive({
  physical: false,
});

// 新分组名称
const newGroupName = ref("");

// 分组数据
const groups = ref([
  {
    id: "physical",
    name: "体能",
    items: [{ id: 1, name: "主要各种主题的各种主要名称" }],
  },
]);

// 切换分组展开状态
function toggleGroup(groupId: string) {
  expandedGroups[groupId] = !expandedGroups[groupId];
}

// 删除分组
function deleteGroup(groupId: string) {
  ElMessageBox.confirm("确定要删除该分组吗？", "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 删除分组逻辑
      ElMessage.success("分组删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 删除分组项
function deleteItem(groupId: string, itemIndex: number) {
  ElMessageBox.confirm("确定要删除该项目吗？", "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 删除项目逻辑
      ElMessage.success("项目删除成功");
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 添加新分组
function addNewGroup() {
  if (newGroupName.value.trim()) {
    // 添加新分组逻辑
    ElMessage.success(`新增分组：${newGroupName.value}`);
    newGroupName.value = "";
  }
}

// 清空新分组输入
function clearNewGroup() {
  newGroupName.value = "";
}

// 新增主题
function handleNewAdd() {
  ElMessage.success("新增主题功能");
}

// 保存
function handleSave() {
  ElMessage.success("保存成功");
}
</script>

<style scoped lang="scss">
.sidebar-container {
  width: 280px;
  height: 100vh;
  background: #f5f5f5;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  position: relative;

  .sidebar-content {
    flex: 1;
    padding: 20px 16px;
    overflow-y: auto;

    .sidebar-group {
      margin-bottom: 8px;

      .group-header {
        display: flex;
        align-items: center;
        padding: 12px 8px;
        background: #fff;
        border-radius: 4px;
        cursor: pointer;
        border: 1px solid #e4e7ed;

        .expand-icon {
          font-size: 14px;
          color: #666;
          margin-right: 8px;
          transition: transform 0.3s;

          &.expanded {
            transform: rotate(45deg);
          }
        }

        .group-title {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .group-input {
          flex: 1;
          margin-right: 8px;

          :deep(.el-input__wrapper) {
            box-shadow: none;
            background: transparent;
          }
        }

        .delete-icon {
          font-size: 14px;
          color: #999;
          cursor: pointer;

          &:hover {
            color: #f56c6c;
          }
        }
      }

      .group-content {
        margin-top: 4px;
        padding-left: 24px;

        .group-item {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          background: #fff;
          border-radius: 4px;
          margin-bottom: 4px;
          border: 1px solid #e4e7ed;

          .item-text {
            flex: 1;
            font-size: 13px;
            color: #666;
          }

          .delete-icon {
            font-size: 12px;
            color: #999;
            cursor: pointer;

            &:hover {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f5f5f5;
    padding: 16px;
    border-top: 1px solid #e4e7ed;

    .footer-buttons {
      display: flex;
      gap: 12px;

      .el-button {
        flex: 1;
        height: 36px;
      }
    }
  }
}
</style>
