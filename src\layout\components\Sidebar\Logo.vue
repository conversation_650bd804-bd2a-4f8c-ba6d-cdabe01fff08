<!--
 * @Author: czf <EMAIL>
 * @Date: 2024-10-28 17:01:07
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-12-26 11:57:35
 * @FilePath: \gkadmin\src\layout\components\Sidebar\Logo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts" setup>
import defaultSettings from "@/settings";
import { useSettingsStore } from "@/store/modules/settings";

const settingsStore = useSettingsStore();

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

// const logo = ref(new URL(`../../../assets/logo (2).png`, import.meta.url).href);
const logo = ref<any>(
  // "https://gkr.obs.cn-north-4.myhuaweicloud.com/image/default/defalut-logo.png"
  "http://obs.gknowledge.cn/image/default/defalut-logo.png"
);
</script>

<template>
  <div class="top-logo">
    <transition name="sidebarLogoFade">
      <!-- <router-link
        v-if="collapse"
        key="collapse"
        class="h-full w-full flex items-center justify-center"
        to="/"
      >
        <img v-if="settingsStore.sidebarLogo" :src="logo" class="img-logo" />
      </router-link> -->

      <router-link
        key="expand"
        class="h-full w-full flex items-center justify-center"
        to="/"
      >
        <img v-if="settingsStore.sidebarLogo" :src="logo" class="img-logo" />
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
// https://cn.vuejs.org/guide/built-ins/transition.html#the-transition-component
.sidebarLogoFade-enter-active {
  transition: opacity 2s;
}

.sidebarLogoFade-leave-active,
.sidebarLogoFade-enter-from,
.sidebarLogoFade-leave-to {
  opacity: 0;
}
.top-logo {
  background: #fff;
  height: 80px;
}
.img-logo {
  margin-left: 30px;
  height: 70%;
  width: 95%;
}
</style>
