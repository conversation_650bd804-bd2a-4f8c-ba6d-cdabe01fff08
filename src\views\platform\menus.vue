<template>
  <div class="menus-container">
    <div class="container-header">
      <div class="left">
        <!-- <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div> -->
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增菜单
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
        row-key="id"
        default-expand-all
      >
        <!-- <el-table-column label="ID" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column> -->
        <el-table-column label="菜单名" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="唯一名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.uni_name }}
          </template>
        </el-table-column>
        <el-table-column label="是否启用" align="center" min-width="120">
          <template #default="scope">
            <el-tag
              :type="enabledMap[scope.row.enabled + '']?.type || 'info'"
              >{{ enabledMap[scope.row.enabled + ""]?.label || "否" }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.seq }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div class="btn delete-btn" @click="handelDelete(scope.row)">
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form
          :model="formData"
          :rules="formDataRules"
          ref="formRef"
          label-width="6rem"
          :style="{
            pointerEvents: dialog.type == 'detail' ? 'none' : 'auto',
          }"
        >
          <el-form-item label="菜单名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入菜单名称"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="唯一名称" prop="uni_name">
            <el-input
              v-model="formData.uni_name"
              placeholder="请输入菜单唯一名称"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="上级菜单">
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="formData.parent"
              placeholder="请选择上级菜单"
              filterable
              clearable
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span class="tips"
              >*选择菜单后，新增菜单添加在上级菜单的下一级，上级菜单为无时，新增菜单为一级菜单</span
            >
          </el-form-item>
          <el-form-item label="菜单排序">
            <el-input
              v-model="formData.seq"
              placeholder="请输入排序号"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="是否启用" class="custom-el-switch">
            <el-switch v-model="formData.enabled" size="large" />
          </el-form-item>
          <!-- <el-form-item  label="是否超级管理员可见">
            <el-switch v-model="formData.is_super" size="large" />
          </el-form-item> -->
          <el-form-item label="备注信息">
            <el-input
              v-model="formData.remark"
              placeholder="备注信息"
              clearable
              size="large"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="dialog.type != 'detail'">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
        <div class="dialog-footer" v-if="dialog.type == 'detail'">
          <div class="btn cancel-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import {
  getSysMenus,
  deleteSysMenus,
  addSysMenus,
  updateSysMenus,
} from "@/api/system";
import { parseTime } from "@/utils";
import { validateMobile, validEmail } from "@/utils/validate";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Menus",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref();
const enabledMap = reactive<any>({
  true: { value: 10, label: "是", type: "success" },
  false: { value: 20, label: "否", type: "info" },
});
// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "新增菜单",
});

// 用户表单数据
const rowId = ref();
const formData = reactive<any>({
  name: "",
  uni_name: "",
  enabled: true,
  is_super: false,
  // icon:"",
  remark: "",
  parent: "",
  seq: 0,
});
const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  // mobile: [
  //   {
  //     required: true,
  //     trigger: "blur",
  //     validator: validateMobile,
  //   },
  // ],
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  uni_name: [{ required: true, message: "请输入唯一名称", trigger: "blur" }],
});
const options = computed(() => {
  return tableData.value
    .filter((item: any) => !item.parent)
    .map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
});

onMounted(() => {
  getData();
});
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getSysMenus(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.menus.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          if (item.children && item.children.length > 0) {
            item.children.map((itm: any) => {
              itm.created_at = parseTime(
                itm.created_at,
                "{y}-{m}-{d} {h}:{i}:{s}"
              );
              return itm;
            });
          }
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增菜单";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  if (type === "edit") {
    rowId.value = row.id;
    dialog.visible = true;
    dialog.title = "修改菜单";
    dialog.type = "edit";
    Object.assign(formData, row);
  }
  if (type === "delete") {
    handelDelete(row);
  }
  if (type === "detail") {
    dialog.visible = true;
    dialog.title = "菜单详情";
    dialog.type = "detail";
    Object.assign(formData, row);
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除菜单，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteSysMenus(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  getData();
}

function closeDialog() {
  dialog.visible = false;
  formRef.value.clearValidate();
  Object.assign(formData, {
    name: "",
    uni_name: "",
    enabled: true,
    is_super: false,
    remark: "",
    parent: "",
    seq: 0,
  });
}
function handleSubmit() {
  const data = {
    name: formData.name,
    uni_name: formData.uni_name,
    enabled: formData.enabled,
    is_super: formData.is_super,
    remark: formData.remark || undefined,
    parent: formData.parent || null,
    seq: formData.seq || 0,
  };
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      if (dialog.type === "create") {
        addSysMenus(data).then((res: any) => {
          ElMessage.success({
            message: "创建成功!",
          });
          closeDialog();
          getData();
        });
      }
      if (dialog.type === "edit") {
        updateSysMenus(rowId.value, data).then((res: any) => {
          ElMessage.success({
            message: "修改成功!",
          });
          closeDialog();
          getData();
        });
      }
    }
  });
}
</script>

<style scoped lang="scss">
.menus-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 170px);
    height: calc(100% - 90px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
