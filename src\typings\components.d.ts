/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {};

declare module "@vue/runtime-core" {
  export interface GlobalComponents {
    AppMain: typeof import("./../layout/components/AppMain.vue")["default"];
    BarChart: typeof import("./../views/dashboard/components/BarChart.vue")["default"];
    LineChart: typeof import("./../views/dashboard/components/Line.vue")["default"];
    Breadcrumb: typeof import("./../components/Breadcrumb/index.vue")["default"];
    DeptTree: typeof import("./../views/system/user/components/dept-tree.vue")["default"];
    Dictionary: typeof import("./../components/Dictionary/index.vue")["default"];
    DictItem: typeof import("./../views/system/dict/components/dict-item.vue")["default"];
    ElAlert: typeof import("element-plus/es")["ElAlert"];
    ElBreadcrumb: typeof import("element-plus/es")["ElBreadcrumb"];
    ElBreadcrumbItem: typeof import("element-plus/es")["ElBreadcrumbItem"];
    ElButton: typeof import("element-plus/es")["ElButton"];
    ElCard: typeof import("element-plus/es")["ElCard"];
    ElCheckbox: typeof import("element-plus/es")["ElCheckbox"];
    ElCheckboxGroup: typeof import("element-plus/es")["ElCheckboxGroup"];
    ElCol: typeof import("element-plus/es")["ElCol"];
    ElDatePicker: typeof import("element-plus/es")["ElDatePicker"];
    ElDialog: typeof import("element-plus/es")["ElDialog"];
    ElDivider: typeof import("element-plus/es")["ElDivider"];
    ElDropdown: typeof import("element-plus/es")["ElDropdown"];
    ElDropdownItem: typeof import("element-plus/es")["ElDropdownItem"];
    ElDropdownMenu: typeof import("element-plus/es")["ElDropdownMenu"];
    ElForm: typeof import("element-plus/es")["ElForm"];
    ElFormItem: typeof import("element-plus/es")["ElFormItem"];
    ElIcon: typeof import("element-plus/es")["ElIcon"];
    ElImage: typeof import("element-plus/es")["ElImage"];
    ElInput: typeof import("element-plus/es")["ElInput"];
    ElInputNumber: typeof import("element-plus/es")["ElInputNumber"];
    ElLink: typeof import("element-plus/es")["ElLink"];
    ElMenu: typeof import("element-plus/es")["ElMenu"];
    ElMenuItem: typeof import("element-plus/es")["ElMenuItem"];
    ElOption: typeof import("element-plus/es")["ElOption"];
    ElPagination: typeof import("element-plus/es")["ElPagination"];
    ElPopover: typeof import("element-plus/es")["ElPopover"];
    ElRadio: typeof import("element-plus/es")["ElRadio"];
    ElRadioButton: typeof import("element-plus/es")["ElRadioButton"];
    ElRadioGroup: typeof import("element-plus/es")["ElRadioGroup"];
    ElRate: typeof import("element-plus/es")["ElRate"];
    ElRow: typeof import("element-plus/es")["ElRow"];
    ElScrollbar: typeof import("element-plus/es")["ElScrollbar"];
    ElSelect: typeof import("element-plus/es")["ElSelect"];
    ElSubMenu: typeof import("element-plus/es")["ElSubMenu"];
    ElSwitch: typeof import("element-plus/es")["ElSwitch"];
    ElTable: typeof import("element-plus/es")["ElTable"];
    ElTableColumn: typeof import("element-plus/es")["ElTableColumn"];
    ElTabPane: typeof import("element-plus/es")["ElTabPane"];
    ElTabs: typeof import("element-plus/es")["ElTabs"];
    ElTag: typeof import("element-plus/es")["ElTag"];
    ElTooltip: typeof import("element-plus/es")["ElTooltip"];
    ElTree: typeof import("element-plus/es")["ElTree"];
    ElTreeSelect: typeof import("element-plus/es")["ElTreeSelect"];
    ElUpload: typeof import("element-plus/es")["ElUpload"];
    FixedThead: typeof import("./../views/demo/table/dynamic-table/components/FixedThead.vue")["default"];
    FunnelChart: typeof import("./../views/dashboard/components/FunnelChart.vue")["default"];
    GithubCorner: typeof import("./../components/GithubCorner/index.vue")["default"];
    Hamburger: typeof import("./../components/Hamburger/index.vue")["default"];
    IconSelect: typeof import("./../components/IconSelect/index.vue")["default"];
    IEpArrowDown: typeof import("~icons/ep/arrow-down")["default"];
    IEpCaretBottom: typeof import("~icons/ep/caret-bottom")["default"];
    IEpCaretTop: typeof import("~icons/ep/caret-top")["default"];
    IEpClose: typeof import("~icons/ep/close")["default"];
    IEpCollection: typeof import("~icons/ep/collection")["default"];
    IEpDelete: typeof import("~icons/ep/delete")["default"];
    IEpDownload: typeof import("~icons/ep/download")["default"];
    IEpEdit: typeof import("~icons/ep/edit")["default"];
    IEpPicture: typeof import("~icons/ep/picture")["default"];
    IEpPlus: typeof import("~icons/ep/plus")["default"];
    IEpPosition: typeof import("~icons/ep/position")["default"];
    IEpRefresh: typeof import("~icons/ep/refresh")["default"];
    IEpRefreshLeft: typeof import("~icons/ep/refresh-left")["default"];
    IEpSearch: typeof import("~icons/ep/search")["default"];
    IEpSetting: typeof import("~icons/ep/setting")["default"];
    IEpSortDown: typeof import("~icons/ep/sort-down")["default"];
    IEpSortUp: typeof import("~icons/ep/sort-up")["default"];
    IEpTop: typeof import("~icons/ep/top")["default"];
    IEpUploadFilled: typeof import("~icons/ep/upload-filled")["default"];
    Item: typeof import("./../layout/components/Sidebar/Item.vue")["default"];
    LangSelect: typeof import("./../components/LangSelect/index.vue")["default"];
    LeftMenu: typeof import("./../layout/components/Sidebar/LeftMenu.vue")["default"];
    Link: typeof import("./../layout/components/Sidebar/Link.vue")["default"];
    Logo: typeof import("./../layout/components/Sidebar/Logo.vue")["default"];
    MultiUpload: typeof import("./../components/Upload/MultiUpload.vue")["default"];
    NavBar: typeof import("./../layout/components/NavBar/index.vue")["default"];
    NavRight: typeof import("./../layout/components/NavBar/NavRight.vue")["default"];
    Pagination: typeof import("./../components/Pagination/index.vue")["default"];
    PieChart: typeof import("./../views/dashboard/components/PieChart.vue")["default"];
    RadarChart: typeof import("./../views/dashboard/components/RadarChart.vue")["default"];
    RightPanel: typeof import("./../components/RightPanel/index.vue")["default"];
    RouterLink: typeof import("vue-router")["RouterLink"];
    RouterView: typeof import("vue-router")["RouterView"];
    ScrollPane: typeof import("./../layout/components/TagsView/ScrollPane.vue")["default"];
    Settings: typeof import("./../layout/components/Settings/index.vue")["default"];
    Sidebar: typeof import("./../layout/components/Sidebar/index.vue")["default"];
    SidebarItem: typeof import("./../layout/components/Sidebar/SidebarItem.vue")["default"];
    SingleUpload: typeof import("./../components/Upload/SingleUpload.vue")["default"];
    SizeSelect: typeof import("./../components/SizeSelect/index.vue")["default"];
    SvgIcon: typeof import("./../components/SvgIcon/index.vue")["default"];
    SwitchRoles: typeof import("./../views/demo/permission/components/SwitchRoles.vue")["default"];
    TagInput: typeof import("./../components/TagInput/index.vue")["default"];
    TagsView: typeof import("./../layout/components/TagsView/index.vue")["default"];
    TopMenu: typeof import("./../layout/components/Sidebar/TopMenu.vue")["default"];
    UnfixedThead: typeof import("./../views/demo/table/dynamic-table/components/UnfixedThead.vue")["default"];
    WangEditor: typeof import("./../components/WangEditor/index.vue")["default"];
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import("element-plus/es")["ElLoadingDirective"];
  }
}
