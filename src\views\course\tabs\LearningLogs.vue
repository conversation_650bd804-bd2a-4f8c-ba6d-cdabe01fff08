<template>
  <div class="learning-logs-table-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="20">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="40">
          <template #default="scope">
            <span>{{ scope.row.user?.name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="学习进度" align="center" min-width="60">
          <template #default="scope">
            <div class="progress">
              <div class="line">
                <div class="progress-line">
                  <div
                    class="progress-bar"
                    :class="[
                      'free-bar',
                      scope.row.progress * 1 == 100 ? 'full-bar' : '',
                    ]"
                    :style="{
                      width: (scope.row.progress * 1 || 0) + '%',
                    }"
                  ></div>
                </div>
                <span style="color: #2ab7b0">
                  {{ (scope.row.progress || 0) + "%" }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="开始学习日期" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.created_at || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="40">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn green-btn"
                @click="onRowClick('detail', scope.row)"
              >
                明细
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="learning-log-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ dialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="table-content">
          <el-table
            element-loading-text="Loading"
            element-loading-background="#ffffffb4"
            :data="detailTableData"
            height="100%"
            border
            fit
            :highlight-current-row="false"
            :span-method="objectSpanMethod"
          >
            <el-table-column label="阶段名称" align="center" min-width="50">
              <template #default="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column label="学习内容" align="center" min-width="50">
              <template #default="scope">
                {{ scope.row.course.title }}
              </template>
            </el-table-column>
            <el-table-column label="进度 " align="center" min-width="30">
              <template #default="scope">
                {{ scope.row.course.progress }} %
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { getCourses, getCoursesDetail } from "@/api/course";

import { parseTime, numberToChinese, containsAllElements } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";

import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "LearningLogs",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  courseId: {
    type: String,
    default: "",
  },
  courseDetail: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(false);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  status: null,
});

const courseId = ref<any>(props.courseId);
const courseDetail = reactive<any>(props.courseDetail);
const total = ref(0); // 数据总数
const tableData = ref<any>([]);

const dialog = reactive<any>({
  visible: false,
  title: "学习明细",
  width: "40%",
  type: "",
});
const rowId = ref<any>(null);
const detailTableData = ref<any>([]);

onBeforeMount(() => {});
onMounted(() => {
  getData();
});

function optionsLoop(val: any) {
  const res = {
    label: val.name,
    value: val.id,
    children: val.children?.map(optionsLoop) ?? [],
  };
  return res;
}
function getData() {
  loading.value = true;

  const params = {
    course_id: courseId.value,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.type || undefined,
    status: queryParams.status,
  };
  loading.value = false;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function onRowClick(type: string, row: any) {
  if (type == "detail") {
    rowId.value = row.id;
    dialog.visible = true;
    dialog.title = row.ent_user?.name + "学习明细";
    const params: any = {
      ent_user_id: row.ent_user.id,
      ent_training_id: courseId.value,
    };
    // getUserCourseDetail(params).then((res: any) => {
    //   // 拍平成表格数据
    //   detailTableData.value = res.data.flatMap((phase, index) =>
    //     phase.content.map((course) => ({
    //       id: phase.id,
    //       name: phase.name ? phase.name : `第${numberToChinese(index)}阶段`,
    //       course,
    //     }))
    //   );
    // });
  }
}
function expiredAtFilter(value: any) {
  let res: any = "--";

  if (value.package?.expired_at) {
    res = parseTime(value.package.expired_at, "{y}-{m}-{d}");
  }
  return res;
}
function closeDialog() {
  dialog.visible = false;
}

//学习明细表格合并
function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  // 只处理第一列（阶段名称）
  if (columnIndex === 0) {
    // 获取当前阶段名称
    const currentPhaseName = row.name;
    // 统计该阶段名称在 detailTableData.value 中连续出现的次数
    let rowspan = 1;
    for (let i = rowIndex + 1; i < detailTableData.value.length; i++) {
      if (detailTableData.value[i].name === currentPhaseName) {
        rowspan++;
      } else {
        break;
      }
    }
    // 如果不是该阶段第一次出现，则隐藏
    if (
      rowIndex > 0 &&
      detailTableData.value[rowIndex - 1].name === currentPhaseName
    ) {
      return { rowspan: 0, colspan: 0 };
    }
    // 返回合并的行数
    return { rowspan, colspan: 1 };
  }
  // 其他列不合并
  return { rowspan: 1, colspan: 1 };
}
</script>

<style scoped lang="scss">
.learning-logs-table-container {
  width: 100%;
  height: 100%;

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        border-radius: 2px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .export-btn {
        width: 116px;
        height: 40px;
        background: linear-gradient(180deg, #0ebc72 0%, #20c27c 100%);
        border-radius: 2px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 120px);
    padding: 10px 20px;

    :deep(.caret-wrapper) {
      transform: scale(1.5) !important;
    }

    .progress {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      .line {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-bottom: 10px;
      }
    }

    .progress-line {
      width: 146px;
      height: 9px;
      margin-right: 10px;
      background: #7072787b;
      border-radius: 6px;
      box-shadow: 0 0 2px 1px rgb(55 134 104 / 16%),
        inset 0 0 4px 1px rgb(0 0 0 / 7%);

      .progress-bar {
        height: 9px;
        border-radius: 6px 0 0 6px;
      }

      .full-bar {
        border-radius: 6px;
      }

      .free-bar {
        background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%);
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
<style lang="scss">
.learning-log-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/detail-dailog-header.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;

    svg {
      font-size: 20px;

      path {
        fill: #00918c !important;
      }
    }
  }

  .table-content {
    height: 20rem;
  }
}
</style>
