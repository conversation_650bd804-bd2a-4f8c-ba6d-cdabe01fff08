import axios, { InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { useUserStoreHook } from "@/store/modules/user";

// 声明一个 Map 用于存储每个请求的标识 和 取消函数
const pending = new Map();
/**
 * 添加请求
 * @param {Object} config
 */
const addPending = (config: any) => {
  const url = [config.method, config.url, config.page].join("&");
  config.cancelToken =
    config.cancelToken ||
    new axios.CancelToken((cancel) => {
      // 去除重复请求
      if (!pending.has(url)) {
        //        (config.url.includes("gkapi/v1/checkins") ||
        // 如果 pending 中不存在当前请求，则添加进去
        // 查询参数 intercept=false ,则不拦截
        if (config.params) {
          if (config.params.intercept === false) {
            return;
          }
        }
        pending.set(url, cancel);
      }
    });
};
/**
 * 移除请求
 * @param {Object} config
 */
const removePending = (config: any) => {
  const url = [config.method, config.url, config.page].join("&");
  if (pending.has(url)) {
    // 如果在 pending 中存在当前请求标识，需要取消当前请求，并且移除
    const cancel = pending.get(url);
    cancel(url);
    pending.delete(url);
  }
};
/**
 * 清空 pending 中的请求（在路由跳转时调用）
 */
export const clearPending = () => {
  for (const [url, cancel] of pending) {
    cancel(url);
  }
  pending.clear();
};
// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 50000,
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userStore = useUserStoreHook();
    if (userStore.token) {
      // config.headers.Authorization = userStore.token;
      config.headers.Authorization =
        "Token token=" + userStore.token + ", identity=" + userStore.identity;
    }
    removePending(config);
    addPending(config);
    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: any) => {
    const { code, msg } = response.data;
    if (response.status == 200) {
      return response.data;
    }

    // // 响应数据为二进制流处理(Excel导出)
    // if (response.data instanceof ArrayBuffer) {
    //   return response;
    // }

    ElMessage.error(msg || "系统出错");
    return Promise.reject(new Error(msg || "Error"));
  },
  (error: any) => {
    console.log("响应error", error);
    let mes: any = "";
    if (error.response) {
      mes = error.message;
      if (
        error.response.status == 401 ||
        error.response.statusText == "Unauthorized"
      ) {
        mes = "鉴权失败，请重新登录";
        const userStore = useUserStoreHook();
        userStore.resetToken().then(() => {
          location.reload();
        });
      } else if (error.response.data.detail) {
        mes = error.response.data.detail;
      }

      ElMessage.error(mes);
    } else {
      //token过期，重新登录
      // const userStore = useUserStoreHook();
      // userStore.resetToken();
    }
    return Promise.reject(error);
  }
);

// 导出 axios 实例
export default service;
