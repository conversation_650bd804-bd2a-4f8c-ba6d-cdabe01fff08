<template>
  <div class="material-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <el-select
          :suffix-icon="`CaretBottom`"
            size="large"
            v-model="query.type"
            placeholder="请选择素材分类"
            filterable
            clearable
            @change="handleChange"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="query.sort"
            placeholder="请选择素材分组"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleChange"
          >
            <el-option
              v-for="item in sortOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增分组
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
        row-key="id"
        default-expand-all
      >
        <el-table-column label="ID" align="center" prop="id" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="分组名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.seq }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="120"
        >
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">
              {{ statusMap[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" min-width="50">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <!-- <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div> -->
              <div class="btn delete-btn" @click="handelDelete(scope.row)" v-show="enableDelete">
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form label-width="5rem">
          <el-form-item label="分组名称">
            <el-input
              v-model="formData.name"
              placeholder="请输入素材分组名称"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="上级分组">
            <el-select
              size="large"
              v-model="formData.pid"
              placeholder="请选择上级分组"
              filterable
              clearable
              :suffix-icon="`CaretBottom`"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span class="tips"
              >*选择分组后，新增分组添加在上级分组的下一级，上级分组为无时，新增分组为一级分组</span
            >
          </el-form-item>
          <el-form-item label="排序">
            <el-input
              v-model="formData.seq"
              placeholder="请输入排序"
              clearable
              size="large"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { checkUserPermission } from "@/utils/auth";
import {
  getResGroups,
  getResGroupsDetail,
  addResGroups,
  deleteResGroups,
  updateResGroups,
} from "@/api/resource";
import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MaterialLibrary",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref([]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "编辑中" },
  20: { type: "success", label: "上架" },
  30: { type: "info", label: "下架" },
  40: { type: "danger", label: "删除" },
  // add more status mappings as needed
});
// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "新增素材分组",
});

// 用户表单数据
const rowId = ref();
const formData = reactive<any>({
  name: "",
  seq: "",
  pid: "",
});
const options = ref<any>([]);

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 21);
});

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getResGroups(params).then((res: any) => {
    tableData.value = res.data.groups;
    total.value = res.total;
    loading.value = false;
    options.value = res.data.groups.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  });
}

function handleCreate() {
  formData.pid = "";
  dialog.visible = true;
  dialog.title = "新增素材分组";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  if (type === "edit") {
    dialog.visible = true;
    dialog.title = "修改素材分组";
    dialog.type = "edit";
    rowId.value = row.id;
    formData.name = row.name;
    formData.seq = row.seq;
    formData.pid = row.pid;
  }
  if (type === "detail") {
    dialog.visible = true;
    dialog.title = "素材分组详情";
    dialog.type = "detail";
  }
  if (type === "delete") {
    handelDelete(row);
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除素材分组，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteResGroups(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closeDialog() {
  dialog.visible = false;
  Object.assign(formData, {
    name: "",
    pid: "",
    seq: "",
  });
}
function handleSubmit() {
  const data: any = {
    name: formData.name,
    seq: formData.seq || undefined,
    pid: formData.pid,
  };
  if (dialog.type === "create") {
    addResGroups(data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "新增成功!",
        });
        closeDialog();
        getData();
      }
    });
  }
  if (dialog.type === "edit") {
    updateResGroups(rowId.value, data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "修改成功!",
        });
        closeDialog();
        getData();
      }
    });
  }
}
</script>

<style scoped lang="scss">
.material-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .container-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 60%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
  }
  .content {
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
</style>
