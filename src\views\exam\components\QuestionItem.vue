<template>
  <div class="question-item">
    <div class="question-header">
      <div class="question-type-label">
        {{ questionTypeLabel }}
      </div>
      <div class="question-actions">
        <el-button type="text" size="small" @click="handleEdit">
          编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          style="color: #f56c6c"
          @click="handleDelete"
        >
          删除
        </el-button>
      </div>
    </div>

    <!-- 题目编辑表单 -->
    <div class="question-form" v-if="questionData.isEditing">
      <el-form :model="questionData" label-width="80px" size="default">
        <el-form-item label="题目标题" required>
          <el-input
            v-model="questionData.title"
            placeholder="请输入题目标题"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <!-- 单选题选项 -->
        <template v-if="questionData.q_type === 10">
          <el-form-item label="选项设置">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in questionData.options"
                :key="optIndex"
              >
                <span class="option-label"
                  >{{ String.fromCharCode(65 + optIndex) }}.</span
                >
                <el-input
                  v-model="option.text"
                  placeholder="请输入选项内容"
                  style="flex: 1; margin: 0 10px"
                />
                <el-button
                  type="text"
                  size="small"
                  @click="removeOption(optIndex)"
                  v-if="questionData.options.length > 2"
                >
                  删除
                </el-button>
              </div>
              <el-button
                type="text"
                size="small"
                @click="addOption"
                v-if="questionData.options.length < 6"
              >
                + 添加选项
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="正确答案">
            <el-radio-group v-model="questionData.correctAnswer">
              <el-radio
                v-for="(option, optIndex) in questionData.options"
                :key="optIndex"
                :label="String.fromCharCode(65 + optIndex)"
              >
                {{ String.fromCharCode(65 + optIndex) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <!-- 多选题选项 -->
        <template v-if="questionData.q_type === 20">
          <el-form-item label="选项设置">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in questionData.options"
                :key="optIndex"
              >
                <span class="option-label"
                  >{{ String.fromCharCode(65 + optIndex) }}.</span
                >
                <el-input
                  v-model="option.text"
                  placeholder="请输入选项内容"
                  style="flex: 1; margin: 0 10px"
                />
                <el-button
                  type="text"
                  size="small"
                  @click="removeOption(optIndex)"
                  v-if="questionData.options.length > 2"
                >
                  删除
                </el-button>
              </div>
              <el-button
                type="text"
                size="small"
                @click="addOption"
                v-if="questionData.options.length < 6"
              >
                + 添加选项
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="正确答案">
            <el-checkbox-group v-model="questionData.correctAnswers">
              <el-checkbox
                v-for="(option, optIndex) in questionData.options"
                :key="optIndex"
                :label="String.fromCharCode(65 + optIndex)"
              >
                {{ String.fromCharCode(65 + optIndex) }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </template>

        <!-- 填空题 -->
        <template v-if="questionData.q_type === 30">
          <el-form-item label="正确答案">
            <el-input
              v-model="questionData.correctAnswer"
              placeholder="请输入正确答案"
            />
          </el-form-item>
        </template>

        <!-- 判断题 -->
        <template v-if="questionData.q_type === 40">
          <el-form-item label="正确答案">
            <el-radio-group v-model="questionData.correctAnswer">
              <el-radio label="A">A. 正确</el-radio>
              <el-radio label="B">B. 错误</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <el-form-item label="解析">
          <el-input
            v-model="questionData.explanation"
            placeholder="请输入题目解析（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="分值">
          <el-input-number
            v-model="questionData.score"
            :min="1"
            :max="100"
            placeholder="分值"
          />
        </el-form-item>

        <div class="form-actions">
          <el-button type="primary" @click="handleSave"> 保存 </el-button>
          <el-button @click="handleCancel"> 取消 </el-button>
        </div>
      </el-form>
    </div>

    <!-- 题目预览 -->
    <div class="question-preview" v-else>
      <div class="preview-content">
        <div class="question-title">
          {{ questionIndex + 1 }}. {{ questionData.title || "请编辑题目内容" }}
        </div>

        <!-- 单选题预览 -->
        <template v-if="questionData.q_type === 10 && questionData.options">
          <div class="options-preview">
            <div
              class="option-preview"
              v-for="(option, optIndex) in questionData.options"
              :key="optIndex"
            >
              <span class="option-label"
                >{{ String.fromCharCode(65 + optIndex) }}.</span
              >
              <span>{{ option.text }}</span>
            </div>
          </div>
          <div class="answer-preview" v-if="questionData.correctAnswer">
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{ questionData.correctAnswer }}</span>
          </div>
        </template>

        <!-- 多选题预览 -->
        <template v-if="questionData.q_type === 20 && questionData.options">
          <div class="options-preview">
            <div
              class="option-preview"
              v-for="(option, optIndex) in questionData.options"
              :key="optIndex"
            >
              <span class="option-label"
                >{{ String.fromCharCode(65 + optIndex) }}.</span
              >
              <span>{{ option.text }}</span>
            </div>
          </div>
          <div
            class="answer-preview"
            v-if="
              questionData.correctAnswers && questionData.correctAnswers.length
            "
          >
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{
              questionData.correctAnswers.join(", ")
            }}</span>
          </div>
        </template>

        <!-- 填空题预览 -->
        <template v-if="questionData.q_type === 30">
          <div class="fill-blank-preview">
            <div class="blank-input">_____________</div>
          </div>
          <div class="answer-preview" v-if="questionData.correctAnswer">
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{ questionData.correctAnswer }}</span>
          </div>
        </template>

        <!-- 判断题预览 -->
        <template v-if="questionData.q_type === 40">
          <div class="judgment-preview">
            <div class="judgment-options">
              <span class="judgment-option">A. 正确</span>
              <span class="judgment-option">B. 错误</span>
            </div>
          </div>
          <div class="answer-preview" v-if="questionData.correctAnswer">
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{
              questionData.correctAnswer === "A" ? "A. 正确" : "B. 错误"
            }}</span>
          </div>
        </template>

        <div class="explanation-preview" v-if="questionData.explanation">
          <span class="explanation-label">解析：</span>
          <span class="explanation-value">{{ questionData.explanation }}</span>
        </div>

        <div class="score-preview" v-if="questionData.score">
          <span class="score-label">分值：</span>
          <span class="score-value">{{ questionData.score }}分</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";

defineOptions({
  name: "QuestionItem",
  inheritAttrs: false,
});

const props = defineProps({
  questionData: {
    type: Object,
    required: true,
  },
  questionIndex: {
    type: Number,
    required: true,
  },
  questionTypeMap: {
    type: Object,
    default: () => ({
      10: { label: "单选题" },
      20: { label: "多选题" },
      30: { label: "填空题" },
      40: { label: "判断题" },
    }),
  },
});

const emit = defineEmits(["save", "delete", "cancel"]);

const questionTypeLabel = computed(() => {
  return props.questionTypeMap[props.questionData.q_type]?.label || "未知题型";
});

// 编辑题目
function handleEdit() {
  props.questionData.isEditing = true;
}

// 保存题目
function handleSave() {
  // 验证必填字段
  if (!props.questionData.title.trim()) {
    ElMessage.warning("请输入题目标题");
    return;
  }

  if (props.questionData.q_type === 10 || props.questionData.q_type === 20) {
    // 验证选项
    const hasEmptyOption = props.questionData.options.some(
      (opt: any) => !opt.text.trim()
    );
    if (hasEmptyOption) {
      ElMessage.warning("请填写所有选项内容");
      return;
    }
  }

  if (props.questionData.q_type === 10 && !props.questionData.correctAnswer) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  if (
    props.questionData.q_type === 20 &&
    (!props.questionData.correctAnswers ||
      props.questionData.correctAnswers.length === 0)
  ) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  if (
    props.questionData.q_type === 30 &&
    !props.questionData.correctAnswer.trim()
  ) {
    ElMessage.warning("请输入正确答案");
    return;
  }

  if (props.questionData.q_type === 40 && !props.questionData.correctAnswer) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  props.questionData.isEditing = false;
  ElMessage.success("题目保存成功");
  emit("save", props.questionData);
}

// 取消编辑
function handleCancel() {
  emit("cancel", props.questionData);
}

// 删除题目
function handleDelete() {
  emit("delete", props.questionIndex, props.questionData);
}

// 添加选项
function addOption() {
  if (props.questionData.options.length < 6) {
    props.questionData.options.push({ text: "" });
  }
}

// 删除选项
function removeOption(optIndex: number) {
  if (props.questionData.options.length > 2) {
    props.questionData.options.splice(optIndex, 1);

    // 如果删除的选项是正确答案，需要清空正确答案
    const deletedOptionKey = String.fromCharCode(65 + optIndex);
    if (
      props.questionData.q_type === 10 &&
      props.questionData.correctAnswer === deletedOptionKey
    ) {
      props.questionData.correctAnswer = "";
    } else if (
      props.questionData.q_type === 20 &&
      props.questionData.correctAnswers.includes(deletedOptionKey)
    ) {
      const answerIndex =
        props.questionData.correctAnswers.indexOf(deletedOptionKey);
      props.questionData.correctAnswers.splice(answerIndex, 1);
    }
  }
}
</script>

<style scoped lang="scss">
.question-item {
  width: 100%;
  padding: 20px;
  box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.25);
  border-radius: 8px;
  border: 1px solid #edeff4;
  margin-bottom: 20px;
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .question-type-label {
      font-size: 14px;
      font-weight: 500;
      color: #409eff;
      background: #ecf5ff;
      padding: 4px 12px;
      border-radius: 12px;
    }

    .question-actions {
      display: flex;
      gap: 8px;
    }
  }

  .question-form {
    .options-container {
      .option-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .option-label {
          font-weight: 500;
          color: #333;
          min-width: 30px;
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 12px;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;
    }
  }

  .question-preview {
    .preview-content {
      .question-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.5;
      }

      .options-preview {
        margin-bottom: 12px;

        .option-preview {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          font-size: 14px;
          color: #666;

          .option-label {
            font-weight: 500;
            color: #333;
            min-width: 30px;
          }
        }
      }

      .fill-blank-preview {
        margin-bottom: 12px;

        .blank-input {
          display: inline-block;
          border-bottom: 2px solid #409eff;
          min-width: 120px;
          height: 24px;
          text-align: center;
          font-size: 14px;
        }
      }

      .judgment-preview {
        margin-bottom: 12px;

        .judgment-options {
          display: flex;
          gap: 20px;

          .judgment-option {
            font-size: 14px;
            color: #666;
          }
        }
      }

      .answer-preview {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .answer-label {
          font-weight: 500;
          color: #409eff;
          margin-right: 8px;
        }

        .answer-value {
          color: #333;
          font-weight: 500;
        }
      }

      .explanation-preview {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 14px;

        .explanation-label {
          font-weight: 500;
          color: #909399;
          margin-right: 8px;
          min-width: 40px;
        }

        .explanation-value {
          color: #666;
          line-height: 1.5;
        }
      }

      .score-preview {
        display: flex;
        align-items: center;
        font-size: 14px;

        .score-label {
          font-weight: 500;
          color: #909399;
          margin-right: 8px;
        }

        .score-value {
          color: #f56c6c;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
