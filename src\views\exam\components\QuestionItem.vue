<template>
  <div class="question-item">
    <div class="question-header">
      <div class="question-header-left">
        <div class="drag-handle" title="拖拽排序">
          <svg-icon icon-class="drag3" />
        </div>
        <div class="question-type-label">
          {{ questionTypeLabel }}
        </div>
      </div>
      <div class="question-actions">
        <el-button type="text" size="small" @click="handleEdit">
          编辑
        </el-button>
        <el-button
          type="text"
          size="small"
          style="color: #f56c6c"
          @click="handleDelete"
        >
          删除
        </el-button>
      </div>
    </div>

    <!-- 题目编辑表单 -->
    <div class="question-form" v-if="localData.isEditing">
      <el-form :model="localData" label-width="80px" size="default">
        <el-form-item label="题目标题" required>
          <el-input
            v-model="localData.title"
            placeholder="请输入题目标题"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <!-- 单选题选项 -->
        <template v-if="localData.q_type === 10">
          <el-form-item label="选项设置">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <span class="option-label"
                  >{{ String.fromCharCode(65 + optIndex) }}.</span
                >
                <el-input
                  v-model="option.text"
                  placeholder="请输入选项内容"
                  style="flex: 1; margin: 0 10px"
                />
                <el-button
                  type="text"
                  size="small"
                  @click="removeOption(optIndex)"
                  v-if="localData.options.length > 2"
                >
                  删除
                </el-button>
              </div>
              <el-button
                type="text"
                size="small"
                @click="addOption"
                v-if="localData.options.length < 6"
              >
                + 添加选项
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="正确答案">
            <el-radio-group v-model="localData.correctAnswer">
              <el-radio
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
                :label="String.fromCharCode(65 + optIndex)"
              >
                {{ String.fromCharCode(65 + optIndex) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <!-- 多选题选项 -->
        <template v-if="localData.q_type === 20">
          <el-form-item label="选项设置">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <span class="option-label"
                  >{{ String.fromCharCode(65 + optIndex) }}.</span
                >
                <el-input
                  v-model="option.text"
                  placeholder="请输入选项内容"
                  style="flex: 1; margin: 0 10px"
                />
                <el-button
                  type="text"
                  size="small"
                  @click="removeOption(optIndex)"
                  v-if="localData.options.length > 2"
                >
                  删除
                </el-button>
              </div>
              <el-button
                type="text"
                size="small"
                @click="addOption"
                v-if="localData.options.length < 6"
              >
                + 添加选项
              </el-button>
            </div>
          </el-form-item>
          <el-form-item label="正确答案">
            <el-checkbox-group v-model="localData.correctAnswers">
              <el-checkbox
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
                :label="String.fromCharCode(65 + optIndex)"
              >
                {{ String.fromCharCode(65 + optIndex) }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </template>

        <!-- 填空题 -->
        <template v-if="localData.q_type === 30">
          <el-form-item label="正确答案">
            <el-input
              v-model="localData.correctAnswer"
              placeholder="请输入正确答案"
            />
          </el-form-item>
        </template>

        <!-- 判断题 -->
        <template v-if="localData.q_type === 40">
          <el-form-item label="正确答案">
            <el-radio-group v-model="localData.correctAnswer">
              <el-radio label="A">A. 正确</el-radio>
              <el-radio label="B">B. 错误</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <el-form-item label="解析">
          <el-input
            v-model="localData.explanation"
            placeholder="请输入题目解析（可选）"
            type="textarea"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="分值">
          <el-input-number
            v-model="localData.score"
            :min="1"
            :max="100"
            placeholder="分值"
          />
        </el-form-item>

        <div class="form-actions">
          <el-button type="primary" @click="handleSave"> 保存 </el-button>
          <el-button @click="handleCancel"> 取消 </el-button>
        </div>
      </el-form>
    </div>

    <!-- 题目预览 -->
    <div class="question-preview" v-else>
      <div class="preview-content">
        <div class="question-title">
          {{ questionIndex + 1 }}. {{ localData.title || "请编辑题目内容" }}
        </div>

        <!-- 单选题预览 -->
        <template v-if="localData.q_type === 10 && localData.options">
          <div class="options-preview">
            <div
              class="option-preview"
              v-for="(option, optIndex) in localData.options"
              :key="optIndex"
            >
              <span class="option-label"
                >{{ String.fromCharCode(65 + optIndex) }}.</span
              >
              <span>{{ option.text }}</span>
            </div>
          </div>
          <div class="answer-preview" v-if="localData.correctAnswer">
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{ localData.correctAnswer }}</span>
          </div>
        </template>

        <!-- 多选题预览 -->
        <template v-if="localData.q_type === 20 && localData.options">
          <div class="options-preview">
            <div
              class="option-preview"
              v-for="(option, optIndex) in localData.options"
              :key="optIndex"
            >
              <span class="option-label"
                >{{ String.fromCharCode(65 + optIndex) }}.</span
              >
              <span>{{ option.text }}</span>
            </div>
          </div>
          <div
            class="answer-preview"
            v-if="localData.correctAnswers && localData.correctAnswers.length"
          >
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{
              localData.correctAnswers.join(", ")
            }}</span>
          </div>
        </template>

        <!-- 填空题预览 -->
        <template v-if="localData.q_type === 30">
          <div class="fill-blank-preview">
            <div class="blank-input">_____________</div>
          </div>
          <div class="answer-preview" v-if="localData.correctAnswer">
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{ localData.correctAnswer }}</span>
          </div>
        </template>

        <!-- 判断题预览 -->
        <template v-if="localData.q_type === 40">
          <div class="judgment-preview">
            <div class="judgment-options">
              <span class="judgment-option">A. 正确</span>
              <span class="judgment-option">B. 错误</span>
            </div>
          </div>
          <div class="answer-preview" v-if="localData.correctAnswer">
            <span class="answer-label">答案：</span>
            <span class="answer-value">{{
              localData.correctAnswer === "A" ? "A. 正确" : "B. 错误"
            }}</span>
          </div>
        </template>

        <div class="explanation-preview" v-if="localData.explanation">
          <span class="explanation-label">解析：</span>
          <span class="explanation-value">{{ localData.explanation }}</span>
        </div>

        <div class="score-preview" v-if="localData.score">
          <span class="score-label">分值：</span>
          <span class="score-value">{{ localData.score }}分</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { DCaret } from "@element-plus/icons-vue";

defineOptions({
  name: "QuestionItem",
  inheritAttrs: false,
});

const props = defineProps({
  questionData: {
    type: Object,
    required: true,
  },
  questionIndex: {
    type: Number,
    required: true,
  },
  questionTypeMap: {
    type: Object,
    default: () => ({
      10: { label: "单选题" },
      20: { label: "多选题" },
      30: { label: "填空题" },
      40: { label: "判断题" },
    }),
  },
});

const emit = defineEmits(["save", "delete", "cancel", "update"]);

// 创建本地响应式数据副本，避免直接修改 props
const localData = ref({ ...props.questionData });

// 监听 props 变化，同步到本地数据
watch(
  () => props.questionData,
  (newValue) => {
    localData.value = { ...newValue };
  },
  { deep: true, immediate: true }
);

// 监听本地数据变化，实时同步到父组件
watch(
  localData,
  (newValue) => {
    emit("update", newValue);
  },
  { deep: true }
);

const questionTypeLabel = computed(() => {
  return props.questionTypeMap[localData.value.q_type]?.label || "未知题型";
});

// 编辑题目
function handleEdit() {
  localData.value.isEditing = true;
}

// 保存题目
function handleSave() {
  // 验证必填字段
  if (!localData.value.title.trim()) {
    ElMessage.warning("请输入题目标题");
    return;
  }

  if (localData.value.q_type === 10 || localData.value.q_type === 20) {
    // 验证选项
    const hasEmptyOption = localData.value.options.some(
      (opt: any) => !opt.text.trim()
    );
    if (hasEmptyOption) {
      ElMessage.warning("请填写所有选项内容");
      return;
    }
  }

  if (localData.value.q_type === 10 && !localData.value.correctAnswer) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  if (
    localData.value.q_type === 20 &&
    (!localData.value.correctAnswers ||
      localData.value.correctAnswers.length === 0)
  ) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  if (localData.value.q_type === 30 && !localData.value.correctAnswer.trim()) {
    ElMessage.warning("请输入正确答案");
    return;
  }

  if (localData.value.q_type === 40 && !localData.value.correctAnswer) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  localData.value.isEditing = false;
  ElMessage.success("题目保存成功");
  emit("save", localData.value);
}

// 取消编辑
function handleCancel() {
  emit("cancel", localData.value);
}

// 删除题目
function handleDelete() {
  emit("delete", props.questionIndex, localData.value);
}

// 添加选项
function addOption() {
  if (localData.value.options.length < 6) {
    localData.value.options.push({ text: "" });
  }
}

// 删除选项
function removeOption(optIndex: number) {
  if (localData.value.options.length > 2) {
    localData.value.options.splice(optIndex, 1);

    // 如果删除的选项是正确答案，需要清空正确答案
    const deletedOptionKey = String.fromCharCode(65 + optIndex);
    if (
      localData.value.q_type === 10 &&
      localData.value.correctAnswer === deletedOptionKey
    ) {
      localData.value.correctAnswer = "";
    } else if (
      localData.value.q_type === 20 &&
      localData.value.correctAnswers.includes(deletedOptionKey)
    ) {
      const answerIndex =
        localData.value.correctAnswers.indexOf(deletedOptionKey);
      localData.value.correctAnswers.splice(answerIndex, 1);
    }
  }
}
</script>

<style scoped lang="scss">
.question-item {
  width: 100%;
  padding: 20px;
  box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.25);
  border-radius: 8px;
  border: 1px solid #edeff4;
  margin-bottom: 20px;
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .question-header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .drag-handle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 30px;
      }

      .question-type-label {
        font-size: 14px;
        font-weight: 500;
        color: #409eff;
        background: #ecf5ff;
        padding: 4px 12px;
        border-radius: 12px;
      }
    }

    .question-actions {
      display: flex;
      gap: 8px;
    }
  }

  .question-form {
    .options-container {
      .option-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .option-label {
          font-weight: 500;
          color: #333;
          min-width: 30px;
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 12px;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #ebeef5;
    }
  }

  .question-preview {
    .preview-content {
      .question-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.5;
      }

      .options-preview {
        margin-bottom: 12px;

        .option-preview {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          font-size: 14px;
          color: #666;

          .option-label {
            font-weight: 500;
            color: #333;
            min-width: 30px;
          }
        }
      }

      .fill-blank-preview {
        margin-bottom: 12px;

        .blank-input {
          display: inline-block;
          border-bottom: 2px solid #409eff;
          min-width: 120px;
          height: 24px;
          text-align: center;
          font-size: 14px;
        }
      }

      .judgment-preview {
        margin-bottom: 12px;

        .judgment-options {
          display: flex;
          gap: 20px;

          .judgment-option {
            font-size: 14px;
            color: #666;
          }
        }
      }

      .answer-preview {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .answer-label {
          font-weight: 500;
          color: #409eff;
          margin-right: 8px;
        }

        .answer-value {
          color: #333;
          font-weight: 500;
        }
      }

      .explanation-preview {
        display: flex;
        align-items: flex-start;
        margin-bottom: 8px;
        font-size: 14px;

        .explanation-label {
          font-weight: 500;
          color: #909399;
          margin-right: 8px;
          min-width: 40px;
        }

        .explanation-value {
          color: #666;
          line-height: 1.5;
        }
      }

      .score-preview {
        display: flex;
        align-items: center;
        font-size: 14px;

        .score-label {
          font-weight: 500;
          color: #909399;
          margin-right: 8px;
        }

        .score-value {
          color: #f56c6c;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
