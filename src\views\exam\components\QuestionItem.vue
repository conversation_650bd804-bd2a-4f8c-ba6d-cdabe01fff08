<template>
  <div class="question-item">
    <div class="question-header">
      <div class="question-header-left">
        <div class="label">
          {{ questionTypeLabel }}
        </div>
        <div class="input">
          <el-input
            v-model="localData.title"
            placeholder="请输入题目 "
            type="textarea"
            maxlength="500"
            show-word-limit
            size="large"
            :rows="2"
            resize="none"
            clearable
          />
        </div>
      </div>
      <div class="question-actions">
        <div class="drag-handle">
          <svg-icon icon-class="drag3" />
        </div>
        <div class="delete-icon" @click="handleDelete">
          <svg-icon icon-class="delete" />
        </div>
      </div>
    </div>
    <div class="question-img-list">
      <div class="img-upload">
        <svg-icon icon-class="default-img" />
        <div class="img-tips">
          上传图片，题目图片上限 6 张，每张不可超过 2 Mb
        </div>
      </div>
    </div>

    <!-- 题目编辑表单 -->
    <div class="question-form">
      <!-- 选项 -->
      <div class="question-options">
        <div class="label">
          {{
            localData.q_type == 30 || localData.q_type == 40 ? " 答案" : "选项"
          }}
          <i-ep-circle-plus
            @click="addOption"
            v-if="
              localData.options &&
              localData.options.length < 6 &&
              (localData.q_type == 10 || localData.q_type == 20)
            "
          />
          <!-- 单选和多选才可以添加选项 -->
        </div>
        <div class="green-block">
          <!-- 单选 -->
          <template v-if="localData.q_type == 10">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <!-- <el-checkbox v-model="localData.correctAnswer" /> -->
                  <svg-icon icon-class="default-img" />
                  {{ String.fromCharCode(65 + optIndex) }}.
                </div>
                <div class="input">
                  <el-input
                    v-model="option.text"
                    placeholder="请输入答案"
                    type="text"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    resize="none"
                    clearable
                  />
                </div>
                <div
                  class="delete-icon"
                  @click="removeOption(optIndex)"
                  v-if="localData.options.length > 2"
                >
                  <svg-icon icon-class="delete" />
                </div>
              </div>
            </div>
          </template>

          <!-- 多选 -->
          <template v-if="localData.q_type == 20">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <!-- <el-checkbox v-model="localData.correctAnswer" /> -->
                  <svg-icon icon-class="default-img" />
                  {{ String.fromCharCode(65 + optIndex) }}.
                </div>
                <div class="input">
                  <el-input
                    v-model="option.text"
                    placeholder="请输入答案"
                    type="text"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    resize="none"
                    clearable
                  />
                </div>
                <div
                  class="delete-icon"
                  @click="removeOption(optIndex)"
                  v-if="localData.options.length > 2"
                >
                  <svg-icon icon-class="delete" />
                </div>
              </div>
            </div>
          </template>

          <!-- 填空 -->
          <template v-if="localData.q_type == 30">
            <div class="options-container">
              <div class="option-item">
                <div class="option-label">
                  {{ localData.correctAnswer }}
                </div>
                <div class="input">
                  <el-input
                    v-model="localData.correctAnswer"
                    placeholder="请输入答案"
                    type="text"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    resize="none"
                    clearable
                  />
                </div>
              </div>
            </div>
          </template>
          <!-- 判断 -->
          <template v-if="localData.q_type == 10">
            <div class="options-container">
              <div class="option-item">
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <el-checkbox v-model="localData.correctAnswer">
                    <svg-icon icon-class="default-img" />
                    A.对
                  </el-checkbox>
                  <el-checkbox v-model="localData.correctAnswer">
                    <svg-icon icon-class="default-img" />
                    B.错
                  </el-checkbox>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 解析 -->
      <div class="question-analysis">
        <div class="label">解析</div>
        <div class="green-block">
          <div class="input">
            <el-input
              v-model="localData.remark"
              placeholder="请输入解析"
              type="textarea"
              maxlength="500"
              show-word-limit
              size="large"
              :rows="2"
              resize="none"
              clearable
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "QuestionItem",
  inheritAttrs: false,
});

const props = defineProps({
  questionData: {
    type: Object,
    required: true,
  },
  questionIndex: {
    type: Number,
    required: true,
  },
  questionTypeMap: {
    type: Object,
    default: () => ({
      10: { label: "单选" },
      20: { label: "多选" },
      30: { label: "填空" },
      40: { label: "判断" },
    }),
  },
});

const emit = defineEmits(["delete", "update"]);

// 创建本地响应式数据副本，避免直接修改 props
const localData = ref({ ...props.questionData });

// 监听 props 变化，同步到本地数据
watch(
  () => props.questionData,
  (newValue) => {
    localData.value = { ...newValue };
  },
  { deep: true, immediate: true }
);

// 监听本地数据变化，实时同步到父组件
watch(
  localData,
  (newValue) => {
    emit("update", newValue);
  },
  { deep: true }
);

const questionTypeLabel = computed(() => {
  return props.questionTypeMap[localData.value.q_type]?.label + ".题目";
});

// 题目操作函数

// 删除题目
function handleDelete() {
  emit("delete", props.questionIndex, localData.value);
}

// 添加选项
function addOption() {
  if (localData.value.options.length < 6) {
    localData.value.options.push({ text: "" });
  }
}

// 删除选项
function removeOption(optIndex: number) {
  if (localData.value.options.length > 2) {
    localData.value.options.splice(optIndex, 1);

    // 如果删除的选项是正确答案，需要清空正确答案
    const deletedOptionKey = String.fromCharCode(65 + optIndex);
    if (
      localData.value.q_type === 10 &&
      localData.value.correctAnswer === deletedOptionKey
    ) {
      localData.value.correctAnswer = "";
    } else if (
      localData.value.q_type === 20 &&
      localData.value.correctAnswers.includes(deletedOptionKey)
    ) {
      const answerIndex =
        localData.value.correctAnswers.indexOf(deletedOptionKey);
      localData.value.correctAnswers.splice(answerIndex, 1);
    }
  }
}
</script>

<style scoped lang="scss">
.question-item {
  width: 100%;
  padding: 20px;
  box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.25);
  border-radius: 8px;
  border: 1px solid #edeff4;
  margin-bottom: 20px;
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    width: 80px;
    min-width: 80px;
    display: flex;
    // justify-content: center;
    align-items: center;
    flex-direction: column;

    svg {
      cursor: pointer;
      font-size: 28px;
      color: #00918c;
    }
  }
  .input {
    width: 100%;
  }
  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
    width: 100%;

    .question-header-left {
      display: flex;
      gap: 12px;
      width: 100%;
      font-weight: 500;
      font-size: 15px;
      color: #3b4664;
    }

    .question-actions {
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      height: 70px;
      gap: 8px;
      .drag-handle {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 24px;
      }
      .delete-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 24px;
      }
    }
  }

  .question-img-list {
    margin-left: 100px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    .img-upload {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 27px;
      svg {
        cursor: pointer;
      }
      .img-tips {
        margin-left: 10px;
        font-weight: 400;
        font-size: 14px;
        color: #8d9295;
      }
    }
    .img-item {
    }
  }

  .question-form {
    .green-block {
      // height: 116px;
      min-height: 116px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 24px;
      background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
      box-shadow: 0px 0px 6px 1px rgba(191, 226, 206, 0.16);
      border-radius: 13px 13px 13px 13px;
      border: 1px solid #ffffff;
      :deep(.el-textarea__inner),
      :deep(.el-input__inner) {
        background: #ffffff !important;
      }
    }
  }
  .question-analysis {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 0;
    width: 100%;
    padding-right: 20px;
    gap: 12px;
  }
  .question-options {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 0;
    width: 100%;
    padding-right: 20px;
    gap: 12px;
    .options-container {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      gap: 10px;
      background: #ffffff;
      box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.14);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #edeff4;
      :deep(.el-textarea__inner),
      :deep(.el-input__wrapper) {
        border-radius: none !important;
        box-shadow: none !important;
        background-color: none !important;
        border: none !important;
      }
      .option-item {
        width: 100%;
        border-bottom: 1px solid #c1c7d5;
        display: flex;
        align-items: center;
        padding: 10px 10px;
        .option-label {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 10px;
          svg {
            font-size: 24px;
            margin: 0 5px;
          }
        }
        .delete-icon {
          margin-left: 10px;
          cursor: pointer;
        }
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
