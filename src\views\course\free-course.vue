<template>
  <div class="free-course-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.cat_id"
            placeholder="请选择分类"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in cat_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.sec_id"
            placeholder="请选择栏目"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in sec_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          配置免费课程
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="ID" align="center" min-width="30">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="目标课程" align="center" min-width="140">
          <template #default="scope">
            <div class="cover-name">
              <img :src="scope.row.gk_course.thumb" alt="" class="cover-img" />
              <!-- + '?x-image-process=style/style-270' -->
              <span>{{ scope.row.gk_course.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">
              {{ statusMap[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="免费时段" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.timeRange }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="90">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn"
                :class="[
                  scope.row.status == 10 || scope.row.status == 30
                    ? 'light-green-btn'
                    : 'info-btn',
                ]"
                @click="onRowClick('status', scope.row)"
                v-show="enableStop"
              >
                {{
                  scope.row.status == 10 || scope.row.status == 30
                    ? "启用"
                    : scope.row.status == 20
                    ? "停用"
                    : "--"
                }}
              </div>
              <div
                class="btn delete-btn"
                v-show="enableDelete"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="course-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form
          label-width="6rem"
          :model="formData"
          :rules="formDataRules"
          ref="formRef"
        >
          <el-form-item
            label="免费时段"
            prop="time_range"
            :style="{ width: dialog.type == 'edit' ? '100%' : '50%' }"
          >
            <!-- <el-date-picker
                v-model="formData.time_range"
                placeholder="截至日期"
                class="date-picker"
                size="large"
                type="date"
                value-format="YYYY-MM-DD"
              /> -->
            <el-date-picker
              class="date-picker"
              size="large"
              v-model="formData.time_range"
              type="datetimerange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <!-- <el-form-item
            label="状态"
            :style="{ width: dialog.type == 'edit' ? '100%' : '50%' }"
          >
            <el-select
              size="large"
              v-model="formData.status"
              placeholder="请选择状态"
              :suffix-icon="`CaretBottom`"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item class="table-item" v-if="dialog.type === 'create'">
            <div class="table-search">
              <div class="filter-row">
                <el-input
                  v-model="dialogQueryParams.search"
                  placeholder="请输入关键字"
                  clearable
                  size="large"
                />
              </div>
              <div class="filter-row">
                <el-select
                  size="large"
                  v-model="dialogQueryParams.sec_id"
                  placeholder="请选择栏目"
                  filterable
                  clearable
                  :suffix-icon="`CaretBottom`"
                  @change="getAllCourseData"
                >
                  <el-option
                    v-for="item in sec_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="btn primary-btn" @click="getAllCourseData">
                <i-ep-search /> 搜索
              </div>
            </div>
            <el-table
              class="content"
              :data="dialogTableData"
              height="20rem"
              border
              fit
              highlight-current-row
              ref="courseRef"
              @select="selectCourse"
              v-loading="dialogLoading"
              element-loading-text="Loading"
              element-loading-background="#ffffffb4"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />
              <el-table-column label="课程" align="center" min-width="100">
                <template #default="scope">
                  <div class="cover-name">
                    <img :src="scope.row.thumb" alt="" class="cover-img" />
                    <span>{{ scope.row.title }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="所属栏目"
                align="center"
                prop="mobile"
                min-width="100"
              >
                <template #default="scope">
                  {{ scope.row.sections?.[0]?.name }}
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div class="table-footer" v-if="dialog.type === 'create'">
          <pagination
            v-if="dialogTotal > 0"
            v-model:total="dialogTotal"
            v-model:page="dialogQueryParams.pageNum"
            v-model:limit="dialogQueryParams.pageSize"
            @pagination="getAllCourseData"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  getCourses,
  getFreeCourses,
  deleteFreeCourses,
  updateFreeCourses,
  addFreeCourses,
  getCategories,
} from "@/api/course";
import { getSections } from "@/api/web-page";
import { parseTime, formatStringDate } from "@/utils";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "FreeCourse",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  cat_id: "",
  sec_id: "",
});

const total = ref(0); // 数据总数
const tableData = ref([]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "初始化" },
  20: { type: "success", label: "启用" },
  30: { type: "info", label: "停用" },
  40: { type: "danger", label: "删除" },
  // add more status mappings as needed
});
const statusOptions = reactive<any>([
  {
    value: 20,
    label: "启用",
  },
  {
    value: 30,
    label: "停用",
  },
]);
// 弹窗对象
const rowId = ref(0);
const dialog = reactive({
  visible: false,
  type: "",
  width: "60%",
  title: "",
});
const sec_options = ref<any>([]);
const cat_options = ref<any>([]);
const dialogQueryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  cat_id: "",
  sec_id: "",
});
const dialogTotal = ref(0); // 数据总数
const dialogTableData = ref([]);
const dialogLoading = ref<any>(false);
const courseRef = ref<any>(null);
// 用户表单数据
const formData = reactive<any>({
  status: 20,
  course_id: "", //	课程id
  time_range: [], //Object	时段配置。例 {"from": "20241223000000", "till": "20241224000000"}
});
const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  time_range: [
    { required: true, message: "请选择时段", trigger: ["blur", "change"] },
  ],
});

const enableStop = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 7);
});

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 6);
});
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getFreeCourses(params).then((res: any) => {
    if (res.status == 200) {
      tableData.value = res.data.free_courses.map((item: any) => {
        // item.created_at = parseTime(
        //   item.gk_course.created_at,
        //   "{y}-{m}-{d} {h}:{i}:{s}"
        // );
        item.timeRange =
          formatStringDate(item.time_range.from, "{y}-{m}-{d} {h}:{i}:{s}") +
          " ~ " +
          formatStringDate(item.time_range.till, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}

function getSearchOptions() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  // getCategories(params).then((res: any) => {
  //   cat_options.value = res.data.categories.map((item: any) => {
  //     return {
  //       label: item.name,
  //       value: item.id,
  //     };
  //   });
  // });
  getSections(params).then((res: any) => {
    sec_options.value = res.data.sections.map((item: any) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
}
function getAllCourseData() {
  dialogLoading.value = true;
  const params = {
    search: dialogQueryParams.search || undefined,
    page: dialogQueryParams.pageNum,
    per_page: dialogQueryParams.pageSize,
    cat_id: dialogQueryParams.cat_id || undefined,
    sec_id: dialogQueryParams.sec_id || undefined,
  };
  getCourses(params)
    .then((res: any) => {
      dialogTableData.value = res.data.courses.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.published_at = parseTime(
          item.published_at,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
        return item;
      });
      dialogTotal.value = res.total;
      dialogLoading.value = false;
    })
    .catch((e) => {
      dialogLoading.value = false;
    });
}

function handleCreate() {
  dialog.type = "create";
  dialog.title = "配置免费课程";
  dialog.width = "60%";
  dialog.visible = true;

  getSearchOptions();
  getAllCourseData();
}
function onRowClick(type: string, row: any) {
  if (type == "edit") {
    dialog.visible = true;
    dialog.title = "修改免费时段";
    dialog.type = "edit";
    dialog.width = "30%";
    rowId.value = row.id;
    formData.status = row.status;
    formData.time_range[0] = formatStringDate(
      row.time_range.from,
      "{y}-{m}-{d} {h}:{i}:{s}"
    );
    formData.time_range[1] = formatStringDate(
      row.time_range.till,
      "{y}-{m}-{d} {h}:{i}:{s}"
    );
  }
  if (type == "delete") {
    handelDelete(row);
  }
  if (type == "status") {
    const status =
      row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
    const message =
      row.status == 10 || row.status == 30
        ? "启用成功"
        : row.status == 20
        ? "停用成功"
        : "";
    const text = row.status == 20 ? "停用" : "启用";

    ElMessageBox.confirm("此操作将" + text + "该免费课程，是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      updateFreeCourses(row.id, { status }).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success(message);
          getData();
        }
      });
    });
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将取消该免费课程，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteFreeCourses(row.id).then((res: any) => {
      if (res.status === 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}

function selectCourse(selection: any) {
  let selectRow: any = {};
  if (selection.length > 1) {
    let del_row = selection.shift(); // 删除选中的第一项
    courseRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  }
  // 到这selection数据始终为1条
  if (selection.length) {
    selectRow = selection[0];
  }
  formData.course_id = selectRow.id;
}

function closeDialog() {
  dialog.visible = false;
  formData.time_range = [];
  formData.status = 20;
  formData.course_id = "";
  formRef.value.clearValidate();
  // formRef.value.resetFields();
}
function handleSubmit() {
  const params: any = {
    // status: formData.status,
    // time_range: formData.time_range,
  };
  params.time_range = {
    from: parseTime(formData.time_range[0], "{y}{m}{d}{h}{i}{s}"),
    till: parseTime(formData.time_range[1], "{y}{m}{d}{h}{i}{s}"),
  };
  formRef.value.validate((valid: any) => {
    if (valid) {
      if (dialog.type == "create") {
        if (!formData.course_id) {
          ElMessage.warning({
            message: "请选择课程",
          });
          return;
        }
        params.course_id = formData.course_id;
        addFreeCourses(params).then((res: any) => {
          if (res.status === 200) {
            ElMessage.success({
              message: "添加成功!",
            });
            getData();
            closeDialog();
          }
        });
      }
      if (dialog.type == "edit") {
        updateFreeCourses(rowId.value, params).then((res: any) => {
          if (res.status === 200) {
            ElMessage.success({
              message: "编辑成功!",
            });
            getData();
            closeDialog();
          }
        });
      }
    }
  });
}
</script>

<style scoped lang="scss">
.free-course-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      display: flex;
      align-items: center;

      .cover-img {
        // width: 150px !important;
        // height: 100px !important;
        width: 162px !important;
        height: 86px !important;
        object-fit: cover;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }
    // .cover-name {
    //   display: flex;
    //   align-items: center;
    //   .cover-img {
    //     width: 60%;
    //     height: 106px;
    //   }
    //   span {
    //     width: 40%;
    //     display: inline-block;
    //   }
    // }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.course-dialog {
  .table-item {
    width: 100%;

    .table-search {
      display: flex;
      width: 100%;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        width: 22.5%;
        // justify-content: center;
        // align-items: center;
        margin-left: 20px;
        // width: 360px;
        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }
      }

      .primary-btn {
        width: 116px !important;
        margin-left: 20px !important;
      }
    }

    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-checkbox {
          display: none;
          visibility: hidden;
        }
      }
    }
  }

  .cover-name {
    display: flex;
    align-items: center;

    .cover-img {
      // width: 150px !important;
      // height: 100px !important;
      width: 162px !important;
      height: 86px !important;
      object-fit: cover;
      border-radius: 8px;
    }

    span {
      display: inline-block;
      width: 40%;
    }
  }

  .table-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
}
</style>
