<template>
  <div class="course-detail-container">
    <div class="container-header">
      <div class="left">
        <span>课程管理--{{ typeMap[type].label }}</span>
        <span class="tips" v-if="route.query.type == 'edit'"
          >注意：章节的修改需要进行保存章节的操作，课时以及附件的修改操作将直接保存</span
        >
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar height="100%" warp-style="overflow-x: hidden;">
        <div class="block top" :class="{ 'no-action': !canAction }">
          <el-col :span="12" align="left"
            ><div class="label label-title">基本信息</div></el-col
          >
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>名称</div>
              <div class="input">
                <el-input
                  v-model="courseForm.title"
                  placeholder="请输入课程名称"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">分类</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="courseForm.category_ids"
                  placeholder="请选择分类"
                  filterable
                  clearable
                  multiple
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in catOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12" align="center">
              <div class="label">栏目</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="courseForm.section_ids"
                  placeholder="请选择"
                  filterable
                  clearable
                  multiple
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in secOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">栏目展示位置</div>
              <div class="input">
                <el-input
                  v-model="courseForm.seq"
                  placeholder="请输入排序位置"
                  clearable
                  size="large"
                />
                <!-- <el-select
                  size="large"
                  v-model="courseForm.seq"
                  placeholder="请选择"
                  filterable
                  clearable
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in secSeqOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select> -->
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>封面</div>
              <div class="input">
                <UploadImg v-model:imgUrl="imgUrl" :type="'course'" />
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">学习目标</div>
              <div class="input">
                <el-input
                  v-model="courseForm.short_desc"
                  placeholder="请输入学习目标"
                  clearable
                  type="textarea"
                  :rows="5"
                  size="large"
                  resize="none"
                />
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>售价（绿豆）</div>
              <div class="input">
                <el-input
                  v-model="courseForm.bean_price"
                  placeholder="请输入售价"
                  clearable
                  size="large"
                  :disabled="!enableEditPrice"
                >
                  <template #append>绿豆</template>
                </el-input>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>发布时间</div>
              <div class="input">
                <el-date-picker
                  class="date-picker"
                  v-model="courseForm.published_at"
                  type="date"
                  size="large"
                  placeholder="请选择发布时间"
                  clearable
                />
              </div>
            </el-col>
            <!-- <el-col :span="12" align="center">
              <div class="label">售价（金额）</div>
              <div class="input">
                <el-input
                  v-model="courseForm.price"
                  placeholder="请输入售价"
                  clearable
                  size="large"
                >
                  <template #append>元</template>
                </el-input>
              </div>
            </el-col> -->
          </el-row>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"></div>
              <div class="input">
                <span
                  >0 表示免费
                  <span
                    v-if="!enableEditPrice"
                    style="margin-left: 1rem; color: red"
                    >注意：您无权限修改价格，请联系超级管理员</span
                  ></span
                >
              </div>
            </el-col>
            <!-- <el-col :span="12" align="center">
              <div class="label"></div>
              <div class="input">
                <span>0 表示免费</span>
              </div>
            </el-col> -->
          </el-row>
          <el-row>
            <!-- <el-col :span="12" align="center">
              <div class="label">发布时间</div>
              <div class="input">
                <el-date-picker
                  class="date-picker"
                  v-model="courseForm.published_at"
                  type="date"
                  size="large"
                  placeholder="请选择发布时间"
                  clearable
                />
              </div>
            </el-col> -->
            <!-- <el-col :span="12" align="center">
              <div class="label">有效期</div>
              <div class="input">
                <el-input
                  v-model="courseForm.name"
                  placeholder="请输入有效期 (99999 表示永久)"
                  clearable
                  size="large"
                >
                  <template #append>天</template>
                </el-input>
              </div>
            </el-col> -->
          </el-row>
          <el-row>
            <div class="short-detail">
              <div class="label">课程介绍</div>
              <div class="input" style="width: 60rem">
                <Editor v-model="courseForm.detail" />
              </div>
            </div>
          </el-row>
        </div>
        <div class="block middle" :class="{ 'no-action': !canAction }">
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>课时列表</div>
              <div class="input">
                <template v-if="route.query.type == 'create'">
                  <el-radio-group v-model="courseForm.has_chapter">
                    <el-radio :value="false">无章节</el-radio>
                    <el-radio :value="true">有章节</el-radio>
                  </el-radio-group>
                </template>
              </div>
            </el-col>
            <el-col
              v-if="courseForm.has_chapter == false && lessonList.length < 1"
              :span="12"
              align="center"
              class="col-btn"
            >
              <div
                class="text-btn"
                @click="handleLessonVideo(null, 'add', null, null)"
              >
                <i-ep-circle-plus style="margin-right: 5px" />
                新增课时
              </div>
            </el-col>
            <el-col
              v-if="courseForm.has_chapter == true"
              :span="12"
              align="center"
              class="col-btn"
            >
              <!-- <div class="text-plain-btn" @click="handleLessonsFee">
                课时收费管理
              </div> -->
              <div class="text-btn" @click="addCourseChapter">
                <i-ep-circle-plus style="margin-right: 5px" />
                新增章节
              </div>
            </el-col>
          </el-row>
          <el-row
            v-if="courseForm.has_chapter == true"
            style="display: flex; align-items: center; justify-content: center"
          >
            <div class="block-list">
              <!-- 不拖拽做法 -->
              <!-- <div
                class="inside-item"
                v-for="(item, index) in courseChaptersList"
                :key="index"
              >
                <div class="title">
                  <span>章节{{ numberToChinese(index) }}</span>
                  <el-input
                    v-model="courseChaptersList[index].title"
                    placeholder="请输入章节名称"
                    clearable
                    size="large"
                  />
                  <div
                    class="btn primary-btn"
                    @click="handleLessonVideo(item, 'add', index, null)"
                  >
                    添加课时
                  </div>
                  <div
                    class="btn save-btn2"
                    @click="submitCourseChapter(item, 'update', index)"
                  >
                    {{ item.id != null ? "更新章节" : "保存章节" }}
                  </div>
                  <div
                    class="btn delete-btn"
                    @click="deleteCourseChapter(index, item)"
                  >
                    删除章节
                  </div>
                </div>
                <div class="chapter-content">
                  <el-input
                    v-model="courseChaptersList[index].short_desc"
                    placeholder="请输入章节简介"
                    clearable
                    size="large"
                  />
                </div>
                <el-scrollbar
                  style="height: 70%"
                  class="scrollbar-item"
                  warp-style="overflow-x: hidden;"
                >
                  <draggable
                    v-model="item.videos"
                    class="drag-container"
                    :item-key="index + ''"
                    @end="endVideosMove(index)"
                    :handle="'.sort'"
                  >
                    <template #item="{ element: itm, index: idx }">
                      <div class="green-item lesson-item" :key="idx">
                        <div class="green-title">
                          <div class="left">
                            <div class="icon play"><i-ep-video-play /></div>
                            <span>{{ itm.name || "未命名xx" }}</span>
                          </div>

                          <div class="right">
                            <div
                              class="icon edit"
                              @click="
                                handleLessonVideo(itm, 'edit', index, idx)
                              "
                            >
                              <i-ep-edit />
                            </div>

                            <div
                              class="icon delete"
                              @click="
                                handleLessonVideo(itm, 'delete', index, idx)
                              "
                            >
                              <i-ep-delete />
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </draggable>
                  
                </el-scrollbar>
              </div> -->

              <!-- 拖拽做法 -->
              <draggable
                v-model="courseChaptersList"
                class="chapter-drag-container"
                :item-key="'chapter'"
                @end="endChaptersMove"
                :handle="'.sort'"
              >
                <template #item="{ element: item, index: index }">
                  <div class="inside-item" :key="index">
                    <div class="title">
                      <div class="icon sort">
                        <svg-icon icon-class="drag2" />
                      </div>
                      <span>章节{{ numberToChinese(index) }}</span>
                      <el-input
                        v-model="courseChaptersList[index].title"
                        placeholder="请输入章节名称"
                        clearable
                        size="large"
                      />
                      <div
                        class="btn primary-btn"
                        @click="handleLessonVideo(item, 'add', index, null)"
                      >
                        添加课时
                      </div>
                      <div
                        class="btn save-btn2"
                        @click="
                          submitCourseChapter(item, 'update', index, false)
                        "
                      >
                        {{ item.id != null ? "更新章节" : "保存章节" }}
                      </div>

                      <div
                        class="btn delete-btn"
                        @click="deleteCourseChapter(index, item)"
                      >
                        删除章节
                      </div>
                    </div>
                    <div class="chapter-content">
                      <el-input
                        v-model="courseChaptersList[index].short_desc"
                        placeholder="请输入章节简介"
                        clearable
                        size="large"
                      />
                    </div>
                    <el-scrollbar
                      style="height: 70%"
                      class="scrollbar-item"
                      warp-style="overflow-x: hidden;"
                    >
                      <draggable
                        v-model="item.videos"
                        class="drag-container"
                        :item-key="index + ''"
                        @end="endVideosMove(index)"
                        :handle="'.sort'"
                      >
                        <template #item="{ element: itm, index: idx }">
                          <div class="green-item lesson-item" :key="idx">
                            <div class="green-title">
                              <div class="left">
                                <div class="icon play">
                                  <svg-icon icon-class="play" />
                                </div>
                                <span>{{ itm.name || "未命名xx" }}</span>
                              </div>

                              <div class="right">
                                <div
                                  class="icon edit"
                                  @click="
                                    handleLessonVideo(itm, 'edit', index, idx)
                                  "
                                >
                                  <svg-icon icon-class="edit2" />
                                </div>
                                <div
                                  class="icon delete"
                                  @click="
                                    handleLessonVideo(itm, 'delete', index, idx)
                                  "
                                >
                                  <svg-icon icon-class="delete" />
                                </div>
                              </div>
                            </div>
                            <!-- <div class="green-content">
                          <el-input
                            v-model="itm.short_desc"
                            placeholder="请概述课时内容"
                            clearable
                            size="large"
                            type="textarea"
                            :rows="2"
                            resize="none"
                          />
                        </div> -->
                          </div>
                        </template>
                      </draggable>
                    </el-scrollbar>
                  </div>
                </template>
              </draggable>
            </div>
          </el-row>
          <el-row
            v-if="courseForm.has_chapter == false"
            style="display: flex; align-items: center; justify-content: center"
          >
            <div class="block-list" v-if="lessonList.length > 0">
              <div class="inside-item" :style="{ height: '8rem' }">
                <el-scrollbar
                  style="height: 100%"
                  class="scrollbar-item"
                  warp-style="overflow-x: hidden;"
                >
                  <draggable
                    v-model="lessonList"
                    class="drag-container"
                    :item-key="'video'"
                    @end="endLessonsMove"
                    :handle="'.sort'"
                  >
                    <template #item="{ element: itm, index: idx }">
                      <div class="green-item lesson-item" :key="idx">
                        <div class="green-title">
                          <div class="left">
                            <div class="icon0 play">
                              <svg-icon icon-class="play" />
                            </div>
                            <span>{{ itm.name || "未命名" }}</span>
                          </div>

                          <div class="right">
                            <div
                              class="icon edit"
                              @click="handleLessonVideo(itm, 'edit', null, idx)"
                            >
                              <svg-icon icon-class="edit2" />
                            </div>
                            <div
                              class="icon delete"
                              @click="
                                handleLessonVideo(itm, 'delete', null, idx)
                              "
                            >
                              <svg-icon icon-class="delete" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </draggable>
                  <!-- <div
                    class="green-item lesson-item"
                    v-for="(itm, idx) in lessonList"
                    :key="idx"
                  >
                    <div class="green-title">
                      <div class="left">
                        <div class="icon0 play"><i-ep-video-play /></div>
                        <span>{{ itm.name || "未命名" }}</span>
                      </div>

                      <div class="right">
                        <div
                          class="icon edit"
                          @click="handleLessonVideo(itm, 'edit', null, idx)"
                        >
                          <i-ep-edit />
                        </div>
                        <div class="icon sort"><i-ep-sort /></div>
                        <div
                          class="icon delete"
                          @click="handleLessonVideo(itm, 'delete', null, idx)"
                        >
                          <i-ep-delete />
                        </div>
                      </div>
                    </div>
                  </div> -->
                </el-scrollbar>
              </div>
            </div>
          </el-row>
        </div>

        <div class="block bottom" :class="{ 'no-action': !canAction }">
          <!-- <el-row v-if="courseForm.has_chapter == false">
            <el-col :span="12" align="center">
              <div class="label">课时收费管理</div>
              <div class="input">
                <el-radio-group v-model="courseForm.is_free">
                  <el-radio :value="true">免费</el-radio>
                  <el-radio :value="false">收费</el-radio>
                </el-radio-group>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">是否试看</div>
              <div class="input">
                <el-radio-group v-model="courseForm.is_free">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </div>
            </el-col>
          </el-row> -->
          <!-- <el-row>
            <el-col :span="12" align="center">
              <div class="label">课程附件</div>
              <div class="input">
                <div class="block-list">
                  <div class="inside-item attachments">
                    <el-scrollbar
                      style="height: 100%"
                      class="scrollbar-item"
                      warp-style="overflow-x: hidden;"
                    >
                      <div
                        class="green-item attachments-item"
                        v-for="(itm, idx) in attachmentsList"
                        :key="idx"
                      >
                        <div class="green-title">
                          <div class="left">
                            <div class="icon0 play"><i-ep-files /></div>
                            <span>{{ itm.name || "未命名" }}</span>
                          </div>
                          <div class="right"> -->
          <!-- <div
                              class="icon download"
                              @click="handleAttachment('download', itm, idx)"
                            >
                              <i-ep-download />
                            </div> -->
          <!-- <div
                              class="icon delete"
                              @click="handleAttachment('delete', itm, idx)"
                            >
                              <i-ep-delete />
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12" align="center" class="col-btn">
              <div
                class="text-btn"
                @click="handleAttachment('add', null, null)"
              >
                <i-ep-circle-plus style="margin-right: 5px" />
                新增附件
              </div>
            </el-col>
          </el-row> -->

          <div class="footer" v-if="route.query.type !== 'detail'">
            <div class="btn save" @click="handleSave">保存</div>
            <div
              class="btn green-btn2"
              @click="handlePublish"
              v-if="route.query.type !== 'create'"
            >
              {{ courseForm.status == 20 ? "下架" : "上架" }}
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 弹窗--章节\课时弹窗?，选择视频 -->
    <el-dialog
      class="videoDialog"
      v-model="videoDialog.visible"
      :title="videoDialog.title"
      :width="videoDialog.width"
      append-to-body
      @close="closeVideoDialog"
    >
      <div class="dialog-body">
        <el-form>
          <!-- <el-form-item label="课时名称">
            <el-input
              v-model="lessonItemForm.name"
              placeholder="课时名称：1-1 课程名xxx"
              clearable
              size="large"
            />
          </el-form-item> -->
          <el-form-item label="课时名称">
            <el-col :span="12">
              <el-input
                v-model="lessonItemForm.name"
                placeholder="课时名称"
                clearable
                size="large"
                readonly
              />
            </el-col>
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否免费" align="center">
                <el-radio-group v-model="lessonItemForm.is_free">
                  <el-radio :value="true">免费</el-radio>
                  <el-radio :value="false">收费</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示位置" align="center">
                <el-input
                  v-model="lessonItemForm.seq"
                  placeholder="请输入位置数字"
                  clearable
                  size="large"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item class="table-item">
            <div class="table-search">
              <div class="filter-row">
                <el-input
                  v-model="videoQueryParams.search"
                  placeholder="请输入关键字"
                  clearable
                  size="large"
                />
              </div>
              <div class="btn primary-btn" @click="handleVideoQuery(true)">
                <i-ep-search /> 搜索
              </div>
            </div>
            <el-table
              :data="videoTableList"
              height="20rem"
              border
              fit
              highlight-current-row
              ref="videoRef"
              @select="selectVideo"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />
              <!-- <el-table-column
                label="分组"
                align="center"
                prop="mobile"
                min-width="60"
              >
                <template #default="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column> -->
              <el-table-column label="名称" align="center" min-width="80">
                <template #default="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center" min-width="40">
                <template #default="scope">
                  <span>{{ resourceTypeMap[scope.row.res_type]?.label }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="大小"
                align="center"
                prop="name"
                min-width="40"
              >
                <template #default="scope">
                  <span>{{ scope.row.size }} Mb</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" min-width="120">
                <template #default="scope">
                  {{ scope.row.created_at }}
                </template>
              </el-table-column>
            </el-table>
            <div class="table-footer">
              <pagination
                v-if="videoTotal > 0"
                v-model:total="videoTotal"
                v-model:page="videoQueryParams.pageNum"
                v-model:limit="videoQueryParams.pageSize"
                @pagination="handleVideoQuery(false)"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeVideoDialog">取 消</div>
          <div class="btn primary-btn" @click="handleVideoSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>

    <!-- 课时收费管理 ：废弃 -->
    <!-- <el-dialog
      class="lessonsFeeDialog"
      v-model="lessonsFeeDialog.visible"
      :title="lessonsFeeDialog.title"
      :width="lessonsFeeDialog.width"
      append-to-body
      @close="closeLessonsFeeDialog"
    >
      <div class="dialog-body">
        <el-checkbox
          v-model="checkAll"
          @change="handleCheckAll"
          class="custom-checkbox"
        >
          全选
        </el-checkbox>
        <div class="lesson-content">
          <el-tree
            class="custom-checkbox"
            :props="lessonProps"
            ref="lessonItemRef"
            :data="lessonsFeeForm"
            default-expand-all
            highlight-current
            node-key="id"
            show-checkbox
            :default-checked-keys="lesson_ids"
            @check="handleCheck"
            @check-change="handleLessonsFeeCheckedChange"
          />

        </div>
        <el-form>
          <el-form-item label="免费时长" align="center">
            <el-input
              v-model="freeDuration"
              placeholder="请输入时长"
              clearable
              size="large"
            >
              <template #append>秒</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeLessonsFeeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleLessonsFeeSubmit">
            保 存
          </div>
        </div>
      </template>
    </el-dialog> -->

    <!-- 附件弹窗 -->
    <el-dialog
      class="attachmentsDialog"
      v-model="attachmentsDialog.visible"
      :title="attachmentsDialog.title"
      :width="attachmentsDialog.width"
      append-to-body
      @close="closeAttachmentsDialog"
    >
      <div class="dialog-body">
        <el-form label-width="6rem">
          <!-- <el-form-item label="附件名称">
            <el-input
              v-model="attachmentForm.name"
              placeholder="附件名称"
              clearable
              size="large"
            />
          </el-form-item> -->
          <el-form-item label="附件">
            <el-col :span="12">
              <el-input
                v-model="attachmentForm.name"
                placeholder="附件名称"
                clearable
                size="large"
                readonly
              />
            </el-col>
          </el-form-item>
          <el-form-item class="table-item">
            <div class="table-search">
              <div class="filter-row">
                <el-input
                  v-model="attachmentsQueryParams.search"
                  placeholder="请输入关键字"
                  clearable
                  size="large"
                />
              </div>
              <div class="btn primary-btn" @click="handleAttachmentsQuery">
                <i-ep-search /> 搜索
              </div>
            </div>
            <el-table
              :data="attachmentsTableList"
              height="20rem"
              border
              fit
              highlight-current-row
              ref="attachmentRef"
              @select="selectAttachment"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />
              <el-table-column label="名称" align="center" min-width="80">
                <template #default="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center" min-width="40">
                <template #default="scope">
                  <span>{{ resourceTypeMap[scope.row.res_type]?.label }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="大小"
                align="center"
                prop="name"
                min-width="40"
              >
                <template #default="scope">
                  <span>{{ scope.row.size }} Mb</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-footer">
              <pagination
                v-if="attachmentsTotal > 0"
                v-model:total="attachmentsTotal"
                v-model:page="attachmentsQueryParams.pageNum"
                v-model:limit="attachmentsQueryParams.pageSize"
                @pagination="handleAttachmentsQuery"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeAttachmentsDialog">
            取 消
          </div>
          <div class="btn primary-btn" @click="handleAttachmentSubmit">
            确 定
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { numberToChinese, deepClone } from "@/utils";
import UploadImg from "@/components/Upload/UploadImg.vue";
import SingleUpload from "@/components/Upload/SingleUpload.vue";
import Editor from "@/components/WangEditor/index.vue";
import { parseTime } from "@/utils";
import {
  getCoursesDetail,
  addCourses, //创建完整的课程信息
  updateCourses, //基本信息的更新只可以用这个接口

  //特定内容只可以用以下接口增删改
  //章节接口-多个章节，章节内多个视频
  // getCourseChapters,
  addCourseChapters,
  updateCourseChapters,
  deleteCourseChapters,
  //章节视频的接口--即不分章节只有一个课程一个视频
  // getCourseResources,
  addCourseResources,
  updateCourseResources,
  deleteCourseResources,
  //附件的接口
  getAttachments,
  addAttachments,
  // updateAttachments,
  deleteAttachments,
  //获取分类
  getCategories,
} from "@/api/course";
import { getSections } from "@/api/web-page";
//素材列表接口
import { getResources } from "@/api/resource";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "CourseAction",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const enableEditPrice = computed(() => {
  //编辑时权限控制修改金额
  if (route.query.type == "edit") {
    return checkUserPermission(userStore.userInfo.permissions, 2);
  } else return true;
});
const type: any = route.query.type;
const courseId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
  // add more status mappings as needed
});
const imgUrl = ref<any>();
const previewImgUrl = imgUrl.value;

const canAction = ref(true);

const dialogImg = reactive({
  visible: false,
});

const resourceTypeMap = reactive<any>({
  10: { value: 10, label: "视频", dict: "video" },
  20: { value: 20, label: "图片", dict: "image" },
  30: { value: 30, label: "文档", dict: "document" },
  40: { value: 40, label: "音频", dict: "audio" },
  50: { value: 50, label: "其他", dict: "other" },
  // add more status mappings as needed
});
// 弹窗对象1-章节的课时
const videoDialog = reactive<any>({
  visible: false,
  type: "video-form",
  width: "50%",
  title: "课时视频管理",
});

// 用户表单数据

const catOptions = ref<any>([]);
const secOptions = ref<any>([]);
const secSeqOptions = ref<any>([]);

const courseForm = reactive<any>({
  category_ids: "",
  section_ids: "",
  title: "",
  thumb: imgUrl.value,
  short_desc: "",
  detail: "",
  price: "",
  bean_price: "",
  status: "",
  watched_count: "", //观看人数
  is_free: false,
  has_chapter: false,
  published_at: "",
  chapters: [], //has_chapter==true
  videos: [], // has_chapter==false才有\
  attachments: [], //附件列表
});
const courseChaptersList = ref<any>([]);
const chapterForm = reactive<any>({
  seq: "",
  title: "", //提交时变为title
  short_desc: "",
  videos: [], //lessonItemForm
});
const lessonItemForm = reactive<any>({
  seq: "",
  name: "",
  resource_id: "",
  // is_free: true, //必填
  // free_duration: 9999, //免费时长，必填
  is_free: false, //免费课程的配置另外有接口,单个视频免费还是需要的
  free_duration: 0,
  duration: 0,
});
const chapterIndex = ref<any>("");
const videoIndex = ref<any>("");

//课时收费管理??
const lessonsFeeDialog = reactive<any>({
  visible: false,
  type: "lessonsFee",
  width: "35%",
  title: "课时收费管理",
});
const lessonsFeeForm = ref<any>([]);
const lesson_ids = ref<any>([]);
const lessonItemRef = ref<any>();
const freeDuration = ref("");
const checkChapters = ref<any>([]);
const checkAll = ref(false);

const lessonProps = reactive<any>({
  //自定义label
  label: (data: { name: any }) => {
    return data.name; // name为你要显示的名称 可以自定义，就是将name替换label
  },
  children: "videos",
});

//视频素材
const videoTableList = ref<any>([]); //视频素材列表
const videoRef = ref();
const videoTotal = ref<any>(20);
const videoQueryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
//课程视频？无章节分配时的视频列表？
const lessonList = ref<any>([]); //无章节时的课时列表

//附件
const attachmentRef = ref();
const attachmentsDialog = reactive({
  visible: false,
  type: "attachment-form",
  width: "60%",
  title: "附件管理",
});
const attachmentsList = ref<any>([]);
const attachmentsTableList = ref<any>([]); //所有素材都可以事附件
const attachmentsTotal = ref<any>(20);
const attachmentsQueryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
const attachmentForm = reactive<any>({});

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
watch(
  () => courseForm.detail,
  (val) => {}
);
watch(
  () => route.query,
  (val) => {
    if (val) {
      if (val.type == "detail") {
        canAction.value = false;
      }
    }
  },
  { immediate: true }
);
watch(
  () => lesson_ids.value,
  (val) => {
    if (val.length > 0 && lessonsFeeDialog.visible == true) {
      const allChecked = lessonsFeeForm.value
        .map((item: any) => {
          return item.videos.length + 1; //videos+章节的总长度
        })
        .reduce((a: any, b: any) => a + b, 0);
      checkAll.value = lesson_ids.value.length >= allChecked;
    }
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  getSearchOptions();
  if (route.query.type !== "create" && route.query.id) {
    getData();
    getAttachments({ cid: courseId }).then((res) => {
      attachmentsList.value = res.data.attachments;
    });
  }
});
function handleBack() {
  router.go(-1);
}
function getSearchOptions() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getSections(params).then((res: any) => {
    secOptions.value = res.data.sections.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
  getCategories(params).then((res: any) => {
    catOptions.value = res.data.categories.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
}
//获取详情
function getData() {
  const data: any = {
    id: route.query.id,
  };
  getCoursesDetail(data.id).then((res: any) => {
    if (res.status === 200) {
      Object.assign(courseForm, res.data);
      courseForm.category_ids = res.data.categories.map((item: any) => item.id);
      courseForm.section_ids = res.data.sections.map((item: any) => item.id);
      courseForm.published_at = res.data.published_at
        ? parseTime(res.data.published_at, "{y}-{m}-{d}")
        : "";
      if (res.data.chapters.length > 0) {
        courseChaptersList.value = [
          ...res.data.chapters.map((item: any) => {
            item.name = item.title;
            return item;
          }),
        ];
      }
      if (res.data.videos.length > 0) {
        lessonList.value = [...res.data.videos].sort((a, b) => a.seq - b.seq);
      }
      // if (res.data.attachments.length > 0) {
      //   attachmentsList.value = [...res.data.attachments];
      // }
      imgUrl.value = res.data.thumb; //+ "?x-image-process=style/style-270";
    }
  });
}

// //上传图片相关TODO:待优化成素材上传用的OBS
// function handlePreview() {
//   dialogImg.visible = true;
// }
// function handleDelete() {
//   imgUrl.value = "";
// }

// function handleFileChange(file: any) {
//   console.log("change", file);
// }
// function handleFileProgress(file: any) {
//   console.log("progress", file);
// }
// function handleBeforeUpload(file: any) {
//   console.log(file);
// }

//课程章节的排序
function endChaptersMove(index: any) {
  console.log("章节在拖拽", courseChaptersList.value);
  courseChaptersList.value = courseChaptersList.value.map(
    (item: any, index: any) => {
      item.seq = index;
      item.videos = item.videos.map((itm: any, idx: any) => {
        itm.seq = idx;
        return itm;
      });
      return item;
    }
  );
  if (route.query.type == "edit") {
    // 还要再判断拖拽时是否有新增的章节要用add?
    courseChaptersList.value.forEach((item: any, index: any) => {
      submitCourseChapter(item, "update", index, true);
      if (index == courseChaptersList.value.length - 1) {
        setTimeout(() => {
          ElMessage.success({
            message: "拖拽更新成功!",
          });
          getData(); //拖拽后更新重新获取遍数据
        }, 500);
      }
    });
  }
}

//课时的拖拽
function endVideosMove(index: any) {
  const newVideos = courseChaptersList.value[index].videos.map(
    (item: any, idx: any) => {
      item.seq = idx;
      return item;
    }
  );
  console.log("章节内的课时在拖拽", newVideos);
  courseChaptersList.value[index].videos = newVideos;
}
function endLessonsMove(index: any) {
  if (route.query.type == "edit") {
    lessonList.value.forEach((item: any, idx: any) => {
      const data: any = {};
      Object.assign(data, {
        course_id: courseId,
        id: item.id,
        name: item.name,
        resource_id: item.gk_resource || item.resource_id,
        is_free: item.is_free,
        free_duration: item.free_duration,
        duration: item.duration || 0,
        seq: item.seq || idx,
      });
      updateCourseResources(item.id, data);
    });
  }
}

//课时收费管理,废弃
// function handleLessonsFee() {
//   lessonsFeeDialog.visible = true;
//   lessonsFeeForm.value = courseChaptersList.value;
//   lesson_ids.value = [];
//   lessonsFeeForm.value.forEach((item: any, index: any) => {
//     item.videos.forEach((itm: any, idx: any) => {
//       if (itm.is_free == true) {
//         lesson_ids.value.push(itm.id);
//       }
//     });
//   });
//   setTimeout(() => {
//     handleTreeChange();
//   }, 500);
// // }
// function handleTreeChange() {
//   lesson_ids.value.forEach((item: any) => {
//     var node = proxy?.$refs.lessonItemRef.getNode(item);
//     proxy?.$refs.lessonItemRef.setChecked(node, true);
//     // if (node && node.isLeaf) {
//     //   proxy?.$refs.lessonItemRef.setChecked(node, true);
//     //   // node.expand();
//     // }
//   });
// }
// function closeLessonsFeeDialog() {
//   lessonsFeeDialog.visible = false;
//   proxy?.$refs.lessonItemRef.setCheckedKeys([]);
//   lesson_ids.value = [];
// }
// function handleLessonsFeeSubmit() {
//   courseChaptersList.value.map((item: any, index: any) => {
//     item.videos.map((itm: any, idx: any) => {
//       if (lesson_ids.value.includes(itm.id)) {
//         itm.is_free = true;
//         itm.free_duration = freeDuration.value;
//       } else {
//         itm.is_free = false;
//         itm.free_duration = 0;
//       }
//       return itm;
//     });
//     return item;
//   });
//   closeLessonsFeeDialog();
// }
// function handleCheckAll() {
//   lesson_ids.value = [];
//   if (checkAll.value) {
//     lessonsFeeForm.value.forEach((item: any, index: any) => {
//       item.videos.forEach((itm: any, idx: any) => {
//         lesson_ids.value.push(itm.id);
//       });
//     });
//   } else {
//     lesson_ids.value = [];
//     proxy?.$refs.lessonItemRef.setCheckedKeys([]);
//   }
// }

// function handleCheck(checkedKeys: any, halfCheckedKeys: any) {
//   const alreadyCheck: any = lessonItemRef.value.getCheckedNodes();
//   lesson_ids.value = alreadyCheck.map((item: any) => {
//     return item.id;
//   });
//   console.log(alreadyCheck, lesson_ids.value);
// }
// function handleLessonsFeeCheckedChange(
//   data: any,
//   checked: any,
//   indeterminate: any
// ) {}

// function handleCheckChapters() {}

//章节管理相关
function addCourseChapter() {
  courseChaptersList.value.push({
    seq: "",
    title: "", //提交时变为title
    short_desc: "",
    videos: [], //lessonItemForm
  });
  // chapterForm
}
function submitCourseChapter(
  row: any,
  type: any,
  index: any,
  dragging: any = false
) {
  console.log("row", row);
  if (!courseId || courseId == "") {
    ElMessage.warning("请先保存课程！");
    return;
  }
  if (row.title == "") {
    ElMessage.warning("章节名称不能为空！");
    return;
  }
  if (row.videos.length == 0) {
    ElMessage.warning("课时不能为空！");
    return;
  }
  if (row.id == null) {
    type = "add";
  }
  const data: any = {};
  Object.assign(data, {
    title: row.title,
    course_id: courseId,
    thumb: row.thumb,
    short_desc: row.short_desc,
    seq: row.seq || index,
    videos: row.videos.map((item: any, idx: any) => {
      return {
        // name: item.name,
        // id: item.id,//有章节的课时操作--这个id暂时用不上？
        resource_id: item.gk_resource || item.resource_id,
        is_free: item.is_free,
        free_duration: item.free_duration,
        duration: item.duration || 0,
        seq: item.seq || idx,
      };
    }),
  });
  if (type == "add") {
    addCourseChapters(data).then((res) => {
      if (res.status == 200) {
        if (!dragging) {
          ElMessage.success({
            message: "新增成功!",
          });
          getData();
        }
      }
    });
  }
  if (type == "update") {
    updateCourseChapters(row.id, data).then((res) => {
      if (res.status == 200) {
        if (!dragging) {
          ElMessage.success({
            message: "更新成功!",
          });
          getData();
        }
      }
    });
  }
}
function deleteCourseChapter(i: any, row: any) {
  console.log("deleteCourseChapter——row", row);
  if (!row.id || row.id == "") {
    courseChaptersList.value = courseChaptersList.value.filter(
      (item: any, index: any) => {
        return index !== i;
      }
    );
    return;
  }
  //修改课程时只有一个章节不能删除，创建课程时暂时不用判断，在保存课程时会校验
  if (courseChaptersList.value.length == 1) {
    // && route.query.type == "edit"
    ElMessage.warning("不能删除！至少保存一个章节！");
    return;
  }
  ElMessageBox.confirm("此操作将删除该章节，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let chapter_id = "";
    courseChaptersList.value = courseChaptersList.value.filter(
      (item: any, index: any) => {
        if (index == i) {
          chapter_id = item.id;
        }
        return index !== i;
      }
    );
    if (route.query.type == "edit" && courseForm.has_chapter) {
      deleteCourseChapters(row.id).then((res) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "删除成功!",
          });
          getData();
        }
      });
    }
  });
}

//点击课时的交互
async function handleLessonVideo(
  row: any,
  type: any,
  chapterIdx: any,
  videoIdx: any
) {
  Object.assign(videoQueryParams, {
    search: "",
    pageNum: 1,
    pageSize: 20,
  });

  chapterIndex.value = chapterIdx;
  videoIndex.value = videoIdx;
  if (type == "add") {
    videoDialog.visible = true;
    videoDialog.type = "add";
    if (courseForm.has_chapter) {
      videoDialog.title = "添加章节课时";
    } else {
      videoDialog.title = "添加课时";
    }
    await handleVideoQuery(false);
  }
  if (type == "edit") {
    videoDialog.visible = true;
    videoDialog.type = "edit";
    if (courseForm.has_chapter) {
      videoDialog.title = "修改章节课时";
      const obj = courseChaptersList.value[chapterIndex.value];
      const videos = obj.videos.filter((item: any, index: any) => {
        return index == videoIdx;
      });
      Object.assign(lessonItemForm, videos[0]);
      if (videos[0].gk_resource) {
        lessonItemForm.resource_id = videos[0].gk_resource;
      }
    } else {
      const videos = lessonList.value.filter((item: any, index: any) => {
        return index == videoIdx;
      });
      Object.assign(lessonItemForm, videos[0]);
      if (videos[0].gk_resource) {
        lessonItemForm.resource_id = videos[0].gk_resource;
      }
    }
    videoQueryParams.search = lessonItemForm.name || "";
    await handleVideoQuery(false);

    const selectRow = videoTableList.value.filter((item: any, index: any) => {
      return (
        lessonItemForm.resource_id == item.id ||
        lessonItemForm.gk_resource == item.id
      );
    });
    // console.log(
    //   "selectRow",
    //   selectRow,
    //   "lessonItemForm",
    //   lessonItemForm,
    //   "videoTableList",
    //   videoTableList.value
    // );

    videoRef.value.toggleRowSelection(selectRow[0], true);
  }
  if (type == "delete") {
    if (courseForm.has_chapter) {
      const obj = courseChaptersList.value[chapterIdx];
      //修改课程时只有一个课程和一个课时不能删除
      if (courseChaptersList.value.length == 1 && obj.videos.length == 1) {
        //&& route.query.type == "edit"
        ElMessage.warning("不能删除！至少保存一个章节和一个课时！");
        return;
      }
      const videos = obj.videos.filter((item: any, index: any) => {
        return index !== videoIdx;
      });
      courseChaptersList.value[chapterIdx].videos = videos;
    } else {
      if (!row.id) {
        lessonList.value = [];
        return;
      }
      //修改课程时只有一个课时不能删除
      if (lessonList.value.length == 1) {
        // && route.query.type == "edit"
        ElMessage.warning("不能删除！课程至少要有一个课时");
        return;
      }
      ElMessageBox.confirm("此操作将删除该课时，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let video_id = "";
        lessonList.value = lessonList.value.filter((item: any, index: any) => {
          if (index == videoIdx) {
            video_id = item.id;
          }
          return index !== videoIdx;
        });
        if (route.query.type == "edit" && !courseForm.has_chapter) {
          deleteCourseResources(video_id).then((res) => {
            if (res.status == 200) {
              ElMessage.success({
                message: "删除成功!",
              });
            }
          });
        }
      });
    }
  }
}

//视频素材弹窗的处理
function handleVideoSubmit() {
  console.log("lessonItemForm", lessonItemForm);
  if (lessonItemForm.resource_id == "") {
    ElMessage.warning("请选择课时!");
    return;
  }
  const data: any = {};
  Object.assign(data, {
    course_id: courseId,
    id: lessonItemForm.id,
    name: lessonItemForm.name,
    resource_id: lessonItemForm.resource_id,
    is_free: lessonItemForm.is_free,
    free_duration: lessonItemForm.free_duration,
    duration: lessonItemForm.duration || 0,
    seq: lessonItemForm.seq || courseChaptersList.value.length,
  });
  //有章节的课时操作
  if (courseForm.has_chapter) {
    if (videoDialog.type == "add") {
      courseChaptersList.value[chapterIndex.value].videos.push(data);
    } else if (videoDialog.type == "edit") {
      courseChaptersList.value[chapterIndex.value].videos[videoIndex.value] =
        data;
    }
    closeVideoDialog();
  } else {
    //无章节的课时操作
    if (videoDialog.type == "add") {
      //判断是否调用无章节的课时接口
      if (route.query.type == "edit") {
        addCourseResources(data).then((res) => {
          if (res.status == 200) {
            ElMessage.success({
              message: "新增成功!",
            });
          }
          lessonList.value.push(data);
          closeVideoDialog();
        });
      } else {
        lessonList.value.push(data);
        closeVideoDialog();
      }
    } else if (videoDialog.type == "edit") {
      //判断是否调用无章节的课时接口
      if (route.query.type == "edit") {
        updateCourseResources(data.id, data).then((res) => {
          if (res.status == 200) {
            ElMessage.success({
              message: "修改成功!",
            });
            lessonList.value[videoIndex.value] = data;
            closeVideoDialog();
          }
        });
      } else {
        lessonList.value[videoIndex.value] = data;
        closeVideoDialog();
      }
    }
  }
}
function selectVideo(selection: any) {
  let selectRow: any = {};
  if (selection.length > 1) {
    let del_row = selection.shift(); // 删除选中的第一项
    videoRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  }
  // 到这selection数据始终为1条
  if (selection.length) {
    selectRow = selection[0];
  }
  // Object.assign(lessonItemForm, selectRow, { resource_id: selectRow.id });//会覆盖课时的id
  // lessonItemForm.seq = selectRow.seq || "";
  // lessonItemForm.name = selectRow.name;
  // lessonItemForm.resource_id = selectRow.id;
  // lessonItemForm.duration = selectRow.duration;
  Object.assign(lessonItemForm, {
    resource_id: selectRow.id,
    seq: selectRow.seq || "",
    name: selectRow.name,
    duration: selectRow.duration,
  });
  // console.log("lessonItemForm", lessonItemForm, "row", selectRow);
}

async function handleVideoQuery(flag: any = null) {
  const params = {
    search: videoQueryParams.search || undefined,
    page: videoQueryParams.pageNum,
    per_page: videoQueryParams.pageSize,
    type: 10,
    // gid: queryParams.gid || undefined,
  };
  if (flag) {
    videoQueryParams.pageNum = 1;
  }
  await getResources(params).then((res: any) => {
    videoTableList.value = res.data.resources.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      item.size = (item.size / 1024 / 1024)?.toFixed(2);
      if (item.res_type !== 10) {
        item.size = item.res_info.size
          ? (item.res_info.size / 1024 / 1024)?.toFixed(2)
          : (item.size / 1024 / 1024)?.toFixed(2);
      }
      return item;
    });
    videoTotal.value = res.total;
  });
}
function closeVideoDialog() {
  videoDialog.visible = false;

  Object.assign(lessonItemForm, {
    seq: "",
    name: "",
    resource_id: "",
    is_free: false, //免费课程的配置另外有接口
    free_duration: 0,
    duration: "",
  });
  Object.assign(videoQueryParams, {
    search: "",
    pageNum: 1,
    pageSize: 20,
  });
  chapterIndex.value = "";
  videoIndex.value = "";
  videoRef.value.clearSelection();
}

//课时--无章节课程

//附件管理
function handleAttachment(type: any, row: any, index: any) {
  if (type == "add") {
    attachmentsDialog.visible = true;
    handleAttachmentsQuery();
  }
  if (type == "delete") {
    const obj = attachmentsList.value[index];
    deleteAttachment(row);
  }
  if (type == "download") {
    const link = document.createElement("a");
    link.download = row.name;
    link.href = JSON.parse(row.res_info).url;
    document.body.appendChild(link);
    link.click();
  }
}
function closeAttachmentsDialog() {
  attachmentsDialog.visible = false;
  attachmentRef.value.clearSelection();
  for (let key in attachmentForm) {
    if (attachmentForm.hasOwnProperty(key)) {
      delete attachmentForm[key];
    }
  } //清空表单1
  Object.assign(attachmentsQueryParams, {
    search: "",
    pageNum: 1,
    pageSize: 20,
  }); //清空表单2
}
function selectAttachment(selection: any) {
  let selectRow: any = {};
  if (selection.length > 1) {
    let del_row = selection.shift(); // 删除选中的第一项
    attachmentRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  }
  // 到这selection数据始终为1条
  if (selection.length) {
    selectRow = selection[0];
  }
  Object.assign(attachmentForm, selectRow, { resource_id: selectRow.id });
}

//查询总素材列表添加附件
function handleAttachmentsQuery() {
  const params = {
    search: attachmentsQueryParams.search || undefined,
    page: attachmentsQueryParams.pageNum,
    per_page: attachmentsQueryParams.pageSize,
    // gid: queryParams.gid || undefined,
  };
  getResources(params).then((res: any) => {
    attachmentsTableList.value = res.data.resources.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return item;
    });
    attachmentsTotal.value = res.total;
  });
}
function handleAttachmentSubmit() {
  console.log("附件", attachmentForm);
  const data: any = {};
  Object.assign(data, {
    course_id: courseId,
    resource_id: attachmentForm.resource_id,
  });
  if (route.query.type == "edit") {
    addAttachments(data).then((res) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "增加成功!",
        });
      }
    });
    setTimeout(() => {
      getAttachments({ cid: courseId }).then((res) => {
        attachmentsList.value = res.data.attachments;
      });
    }, 1000);
  } else {
    data.name = attachmentForm.name;
    data.id = attachmentForm.resource_id;
    attachmentsList.value.push(data);
  }
  closeAttachmentsDialog();
}
function deleteAttachment(row: any) {
  ElMessageBox.confirm("此操作将删除该附件，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    if (route.query.type == "edit") {
      deleteAttachments(row.id).then((res) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "删除成功!",
          });
          setTimeout(() => {
            getAttachments({ cid: courseId }).then((res) => {
              attachmentsList.value = res.data.attachments;
            });
          }, 1000);
        }
      });
    } else {
      attachmentsList.value = attachmentsList.value.filter(
        (item: any, index: any) => {
          return item.id !== row.id;
        }
      );
    }
    // getAttachments()
  });
}

function validBasic(data: any) {
  let flag: any = false;
  if (!data.title) {
    ElMessage.warning("请填写课程名称");
    flag = true;
  }
  // else if (!data.category_ids || data.category_ids.length == 0) {
  //   ElMessage.warning("请选择分类");
  //   flag = true;
  // } else if (!data.section_ids || data.section_ids.length == 0) {
  //   ElMessage.warning("请选择栏目");
  //   flag = true;
  // }
  else if (!data.thumb) {
    ElMessage.warning("请选择课程封面");
    flag = true;
  }
  // if (!data.short_desc) {
  //   ElMessage.warning("请填写学习目标");
  //   flag = true;
  //   return;
  // }
  // if (!data.detail) {
  //   ElMessage.warning("请填写课程介绍");
  //   flag = true;
  //   return;
  // }
  else if (!data.bean_price) {
    ElMessage.warning("请填写课程售价");
    flag = true;
  } else if (!data.published_at) {
    ElMessage.warning("请选择发布日期");
    flag = true;
  }
  return flag;
}
//只是保存编辑中的状态，status=10，校验字段
function handleSave() {
  const data: any = {
    // id: courseId,
    status: courseForm.status,
    category_ids: courseForm.category_ids,
    section_ids: courseForm.section_ids,
    title: courseForm.title,
    thumb: imgUrl.value,
    short_desc: courseForm.short_desc,
    detail: courseForm.detail,
    // price: courseForm.price,
    price: courseForm.bean_price * 10,
    bean_price: courseForm.bean_price,
    // is_free: courseForm.is_free,
    published_at: courseForm.published_at
      ? parseTime(courseForm.published_at, "{y}{m}{d}000000")
      : undefined,
    seq: courseForm.seq,
  };
  //增加表单校验
  const flag = validBasic(data);
  if (flag) {
    return;
  }

  // const flag = Object.keys(data).every((key) => {
  //   console.log(data[key]);
  //   return data[key] !== null || data[key] !== "";
  // });
  // if (!flag) {
  //   ElMessage.warning("基本信息都是必填项，请检查！");
  //   return;
  // }

  // 校验课时
  let videoValid: any = true;
  const videos = lessonList.value?.map((item: any) => {
    return {
      name: item.name,
      resource_id: item.gk_resource || item.resource_id,
      is_free: item.is_free,
      free_duration: item.free_duration,
      duration: item.duration || 0,
      seq: item.seq,
    };
  });

  // 校验章节
  let chapterValid: any = true;
  let chapterNameValid: any = true;
  const chapters = courseChaptersList.value?.map((item: any, index: any) => {
    if (item.videos.length == 0) {
      videoValid = false;
      return;
    }

    const res: any = {
      id: item.id,
      title: item.title || item.name,
      thumb: item.thumb,
      short_desc: item.short_desc,
      seq: item.seq || index,
      videos: item.videos.map((itm: any) => {
        return {
          name: itm.name,
          resource_id: itm.gk_resource || itm.resource_id,
          is_free: itm.is_free,
          free_duration: itm.free_duration,
          duration: itm.duration || 0,
          seq: itm.seq,
        };
      }),
    };
    if (!res.title || res.title == "") {
      chapterNameValid = false;
      return;
    }
    return res;
  });
  const attachments = attachmentsList.value?.map((item: any) => {
    return item.gk_resource || item.resource_id;
  });
  // data.videos = lessonList.value.length > 0 ? videos : undefined;
  // data.chapters = courseChaptersList.value.length > 0 ? chapters : undefined;

  data.has_chapter = courseForm.has_chapter;
  if (data.has_chapter) {
    data.chapters = courseChaptersList.value.length > 0 ? chapters : undefined;
    if (courseChaptersList.value.length <= 0) {
      chapterValid = false;
    }
  } else {
    data.videos = lessonList.value.length > 0 ? videos : undefined;
    if (lessonList.value.length <= 0) {
      videoValid = false;
    }
  }

  // 课时列表校验，列表不能为空
  if (!chapterValid && data.has_chapter) {
    ElMessage.warning("章节不能为空！");
    return;
  }
  if (chapterValid && !chapterNameValid && data.has_chapter) {
    ElMessage.warning("章节名称不能为空！");
    return;
  }
  if (!videoValid) {
    ElMessage.warning(`${data.has_chapter ? "章节" : ""}课时不能为空！`);
    return;
  }

  ElMessageBox.confirm("是否确认操作?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    if (route.query.type == "create") {
      if (chapters.length == 0 && videos.length == 0) {
        ElMessage.warning("请添加课时或章节");
        return;
      }
      data.attachments =
        attachmentsList.value.length > 0 ? attachments : undefined;
      addCourses(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "新增成功!",
          });
          router.go(-1);
        }
      });
    } else if (route.query.type == "edit") {
      updateCourses(courseId, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "修改成功!",
          });
          getData();
          // router.go(-1);
        }
      });
    }
  });
}

//上架、发布
function handlePublish() {
  const has_chapter = courseChaptersList.value.length > 0;
  const has_videos = lessonList.value.length > 0;
  if (!has_chapter && !has_videos) {
    ElMessage.warning("课程视频不能为空");
    return;
  }
  const data: any = {
    // id: courseId,
    status: courseForm.status == 30 ? 20 : 30, //上架 ，30下架
  };
  var text = courseForm.status == 30 ? "上架" : "下架";
  ElMessageBox.confirm("是否确认" + text + "该课程?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    updateCourses(courseId, data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: text + "成功!",
        });
        getData();
        // router.go(-1);
      }
    });
  });
}
</script>

<style scoped lang="scss">
.course-detail-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        //font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        //font-family: 苹方-简, 苹方-简;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
  }

  .no-action {
    pointer-events: none;
  }

  .block {
    width: 100%;
    padding: 10px;
    border-bottom: 6px solid #edeeee;
    border-radius: 8px 8px 0 0;

    .el-row {
      margin: 20px 0;
    }

    .short-detail {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0 20px;

      .label {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 160px;

        span {
          color: red;
        }
      }
    }

    .el-col {
      display: flex;
      align-items: center;
      padding: 0 20px;
    }

    .label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 200px;
      font-size: 15px;
      //font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #3b4664;

      span {
        color: red;
      }
    }

    .label-title {
      justify-content: flex-start;
    }

    .input {
      display: flex;
      align-items: center;
      // flex-direction: column;
      justify-content: flex-start;
      width: 100%;

      img {
        width: 50%;
        height: 160px;
      }

      span {
        margin-top: 10px;
        font-size: 13px;
        font-weight: 400;
        color: #8d9295;
        text-align: left;
      }

      :deep(.el-date-editor) {
        width: 100% !important;
      }
    }

    &:nth-child(3) {
      border-bottom: none;
    }

    .single-uploader {
      position: relative;
      display: flex;
      justify-content: flex-start;

      .single-uploader__image {
        position: relative;
        width: 100%;
        height: 100%;
      }

      .img-upload__overlay {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 10%;
      }
    }
  }

  .middle {
    display: flex;
    flex-direction: column;

    .col-btn {
      display: flex;
      justify-content: flex-end;
      height: 40px;
    }

    .text-plain-btn {
      width: 124px;
      height: 38px;
    }

    .text-btn {
      margin-left: 40px;
      font-size: 16px;
      font-weight: 400;
      cursor: pointer;

      svg {
        font-size: 28px;
      }
    }
  }

  .block-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 20px;
    // &::after {
    //   content: "";
    //   width: 660px;
    //   height: 0px;
    //   border-bottom: 1px solid #edeff4;
    //   flex: auto;
    // }

    .chapter-drag-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .drag-container {
      width: 100%;
    }

    .sort,
    .edit,
    .delete,
    .play {
      font-size: 30px;
    }

    .attachments {
      width: 100% !important;
      height: 182px !important;

      span {
        margin-top: 0 !important;
      }
    }

    .inside-item {
      width: 48%;
      //  width: 660px;
      height: 412px;
      padding: 10px 20px;
      margin: 10px;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 25%);

      .icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        cursor: pointer;
      }

      .title {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 10px 0;

        span {
          display: inline-block;
          width: 10%;
          margin-right: 10px;
        }

        .el-input {
          width: 70%;
        }

        .btn {
          width: 90px;
          height: 30px;
          margin-left: 20px;
          font-size: 12px;
          font-weight: 400;

          &:nth-child(1) {
            margin-left: 0;
          }
        }
      }

      .scrollbar-item {
        width: 100%;
        margin-top: 10px;
      }

      .attachments-item {
        height: 70px !important;
      }

      .lesson-item {
        height: 70px !important;
      }

      .green-item {
        width: 100%;
        height: 140px;
        margin-top: 10px;
        background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
        border: 1px solid #fff;
        border-radius: 13px;
        box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%);

        &:nth-child(1) {
          margin-top: 0;
        }

        .green-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          padding: 20px;
          font-size: 15px;
          //font-family: 苹方-简, 苹方-简;
          font-weight: 400;
          color: #3b4664;

          .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            cursor: pointer;

            .download {
              margin-right: 10px;
            }
          }

          .play {
            cursor: text;
          }

          .icon0 {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            cursor: auto;
          }

          .left {
            display: flex;
            align-items: center;
            justify-content: center;

            span {
              display: inline-block;
              // margin-left: 10px;
            }
          }

          .right {
            display: flex;
            align-items: center;

            span {
              display: inline-block;
            }
          }
        }

        .green-content {
          padding: 0 20px;

          :deep(.el-textarea) {
            .el-input__wrapper,
            .el-textarea__inner {
              background: #fff !important;
            }
          }
        }
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding: 15px 15px 0 0;

    .btn {
      width: 150px;
      height: 50px;
      margin-left: 20px;

      &:nth-child(1) {
        margin-left: 0;
      }
    }
  }
}

.videoDialog,
.attachmentsDialog {
  .table-item {
    width: 100%;

    .table-search {
      display: flex;
      width: 100%;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 360px;
        margin-left: 20px;

        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }
      }

      .primary-btn {
        width: 116px !important;
        margin-left: 20px !important;
        // height: 40px !important;
      }
    }

    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-checkbox {
          display: none;
          visibility: hidden;
        }
      }
    }

    .table-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
  }
}

.lessonsFeeDialog {
  .lesson-content {
    padding: 20px;
    margin: 15px 0;
    border: 1px solid #edeff4;
    border-radius: 8px;
    box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);
  }
}
</style>
