<template>
  <div class="ent-material-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.ent_id"
            placeholder="请选择企业"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in entOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.type"
            placeholder="请选择素材类型"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="right"></div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="素材名称" align="center" min-width="80">
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center" min-width="80">
          <template #default="scope">
            <span>{{ typeMap[scope.row.res_type]?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
          prop="createTime"
          min-width="50"
        >
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="所属企业" align="center" min-width="60">
          <template #default="scope">
            {{ scope.row.enterprise.name }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="60">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="120">
          <template #default="scope">
            <div class="option-btn">
              <div
                v-if="scope.row.status == 30 || scope.row.status == 10"
                class="btn light-green-btn"
                @click="onRowClick('available', scope.row)"
              >
                上架
              </div>
              <div
                v-if="scope.row.status == 20"
                class="btn info-btn"
                @click="onRowClick('removed', scope.row)"
              >
                下架
              </div>
              <div
                class="btn primary-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <!-- <div class="btn delete-btn" @click="handelDelete(scope.row)">
                删除
              </div> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 弹窗 -->
    <!-- <el-dialog
      class="material-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import {
  getEnterprises,
  getEntResources,
  updateEntResources,
} from "@/api/enterprise";
import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "EntMaterial",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const sortOptions = reactive<any>([]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  ent_id: "",
  type: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref<any>([]);
const entOptions = ref<any>([]);
const typeOptions = reactive([
  {
    value: 10,
    label: "视频",
  },
  {
    value: 20,
    label: "图片",
  },
  {
    value: 30,
    label: "文档",
  },
  {
    value: 40,
    label: "音频",
  },
  {
    value: 50,
    label: "其他",
  },
]);
const typeMap = reactive<any>({
  10: { value: 10, label: "视频" },
  20: { value: 20, label: "图片" },
  30: { value: 30, label: "文档" },
  40: { value: 40, label: "音频" },
  50: { value: 50, label: "其他" },
  // add more status mappings as needed
});
const statusOptions = reactive<any>([
  {
    value: 20,
    label: "上架",
  },
  {
    value: 30,
    label: "下架",
  },
]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "编辑中" },
  20: { type: "success", label: "上架" },
  30: { type: "danger", label: "下架" },
  40: { type: "danger", label: "删除" },
  // add more status mappings as needed
});
// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "35%",
  title: "详情",
});

// 用户表单数据
const formData = reactive<any>({
  status: 1,
});

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });

onMounted(() => {
  getEnt();
  getData();
});
function getEnt() {
  const params = {
    page: 1,
    per_page: 999,
  };
  getEnterprises(params).then((res: any) => {
    if (res.status == 200) {
      entOptions.value = res.data.enterprises.map((item: any) => {
        item.label = item.name;
        item.value = item.id;
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
    }
  });
}
function getData() {
  loading.value = true;
  const params = {
    ent_id: queryParams.ent_id || undefined,
    type: queryParams.type || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getEntResources(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.ent_resources.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          item.published_at = parseTime(
            item.published_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function onRowClick(type: string, row: any) {
  if (type == "detail") {
    router.push({
      path: "ent-material-action",
      query: { type: row.res_type, id: row.id },
    });
  } else {
    updateStatus(type, row);
  }
}

function updateStatus(type: string, row: any) {
  ElMessageBox.confirm(
    `此操作将${type == "available" ? "上架" : "下架"}素材，是否继续?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    if (type === "available") {
      const data = {
        status: 20,
      };
      updateEntResources(row.id, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "上架成功!",
          });
          getData();
        }
      });
    } else if (type == "removed") {
      const data = {
        status: 30,
      };
      updateEntResources(row.id, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "下架成功!",
          });
          getData();
        }
      });
    }
  });
}

function handleQuery() {
  getData();
}

// function closeDialog() {
//   dialog.visible = false;
// }
</script>

<style scoped lang="scss">
.ent-material-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .container-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 60%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
  }
  .content {
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
    .cover-name {
      display: flex;
      align-items: center;
      .cover-img {
        width: 60%;
        height: 106px;
      }
      span {
        width: 40%;
        display: inline-block;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
</style>
