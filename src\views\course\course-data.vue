<template>
  <div class="course-data-container" ref="courseDetailRef">
    <div class="container-header" ref="containerHeaderRef">
      <div class="left">课程--课程数据</div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="container-content">
      <div class="top-desc">
        <div class="desc-left">
          <div class="cover-img">
            <img :src="courseDetail.thumb" alt="" />
          </div>
        </div>
        <div class="desc-right">
          <div class="title">{{ courseDetail.title }}</div>
          <div class="category">
            所属分类：{{ filterCategoriesName(courseDetail.categories) }}
          </div>
          <div class="section">
            所属栏目：{{ filterSectionsName(courseDetail.sections) }}
          </div>
          <div class="bean">
            <svg-icon icon-class="lvdou" />
            <span class="discount-price" v-if="courseDetail.discount">
              {{ courseDetail.discount?.discount_bean }}
            </span>
            <span
              class="origin-price"
              :class="{
                'price-line': courseDetail.discount || courseDetail.free,
              }"
            >
              {{ courseDetail.bean_price }}
            </span>
          </div>
        </div>
      </div>
      <div class="bottom-desc">
        <div class="pre-header">
          <el-tabs
            v-model="activeTab"
            class="header-tabs"
            @tab-change="handleTab"
          >
            <el-tab-pane
              v-for="(item, index) in typeOptions"
              :key="index"
              :label="item.label"
              :name="item.value"
            />
          </el-tabs>
        </div>
        <div class="tab-content">
          <SellLogs
            class="log-table"
            v-if="activeTab === 'sellLogs'"
            :courseId="courseId"
            :courseDetail="courseDetail"
          />
          <LearningLogs
            class="log-table"
            v-if="activeTab === 'learningLogs'"
            :courseId="courseId"
            :courseDetail="courseDetail"
          />
          <ExamLogs
            class="log-table"
            v-if="activeTab === 'examLogs'"
            :courseId="courseId"
            :courseDetail="courseDetail"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { parseTime, secondsToHoursAndMinutes } from "@/utils";
import SellLogs from "./tabs/SellLogs.vue";
import LearningLogs from "./tabs/LearningLogs.vue";
import ExamLogs from "./tabs/ExamLogs.vue";
import { getCoursesDetail } from "@/api/course";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "CourseData",
  inheritAttrs: false,
});
/** 仓库*/
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const pageType: any = route.query.type;
const courseId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
  // add more status mappings as needed
});

const courseDetail = reactive<any>({});
const activeTab = ref("sellLogs");
const typeOptions = ref<any>([
  { value: "sellLogs", label: "销售记录" },
  { value: "learningLogs", label: "学习记录" },
  { value: "examLogs", label: "考试记录" },
]);

const examTypeMap = reactive<any>({
  total: { value: 0, label: "全部" },
  obligatory: { value: 0, label: "必修" },
  elective: { value: 0, label: "选修" },
});
const examTypeOptions = ref<any>([]);

onMounted(() => {
  getData();
});
// function getSearchOptions() {
//   const params = {
//     per_page: 9999,
//     page: 1,
//   };
//   getSections(params).then((res: any) => {
//     secOptions.value = res.data.sections.map((item: any) => {

//       return {
//         label: item.name,
//         value: item.id,
//       };
//     });
//   });
//   getCategories(params).then((res: any) => {
//     catOptions.value = res.data.categories.map((item: any) => {

//       return {
//         label: item.name,
//         value: item.id,
//       };
//     });
//   });
// }
//获取详情
function getData() {
  const data: any = {
    id: route.query.id,
  };
  getCoursesDetail(data.id).then((res: any) => {
    if (res.status === 200) {
      Object.assign(courseDetail, res.data);
      console.log("courseDetail", courseDetail);
    }
  });
}
//课程分类、栏目显示
function filterCategoriesName(value: any) {
  let res = " ";
  res = value?.map((item: any) => item.name).join(",");
  return res || "--";
}
function filterSectionsName(value: any) {
  let res = " ";
  res = value?.map((item: any) => item.name).join(",");
  return res || "--";
}
function handleTab() {}
function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.course-data-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .container-content {
    position: relative;
    width: 100%;
    height: calc(100% - 80px);
    padding: 0 40px;

    .top-desc {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 150px;

      .desc-left {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        .cover-img {
          width: 252px;
          height: 132px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .desc-right {
        display: flex;
        flex-direction: column;
        // align-items: center;
        justify-content: space-around;
        height: 90%;
        margin-left: 20px;

        .title {
          font-size: 23px;
          font-weight: 500;
          color: #3b4664;
        }

        .category {
          font-size: 16px;
          font-weight: 400;
          color: #3d4040;
        }

        .section {
          font-size: 16px;
          font-weight: 400;
          color: #3d4040;
        }

        .bean {
          svg {
            margin-right: 10px;
            font-size: 28px;
          }

          position: relative;
          display: flex;
          align-items: center;
          font-size: 22px;
          font-weight: 400;
          color: #0db582;

          .origin-price {
            position: relative;
            display: flex;
            align-items: center;
          }

          .price-line {
            font-size: 14px;
            color: #becec4;
          }

          .price-line::after {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 1px;
            content: "";
            background: currentcolor;
            transform: translateY(-50%);
          }
        }
      }
    }

    .bottom-desc {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: calc(100% - 240px);
      margin-top: 40px;
      // .pre-header{}
      .tab-content {
        width: 100%;
        height: calc(100% - 40px);
      }
    }
  }
}
</style>
