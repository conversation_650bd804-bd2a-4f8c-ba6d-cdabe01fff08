<template>
  <div class="question-bank-container"></div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "question-bank",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

onBeforeMount(() => {});
onMounted(() => {});
</script>

<style scoped lang="scss">
.question-bank-container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

}
</style>
