<template>
  <div class="question-bank-container">
    <!-- 头部搜索区域 -->
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增题目
        </div>
      </div>
    </div>

    <!-- 表格内容区域 -->
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="题库名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.bankName }}
          </template>
        </el-table-column>

        <el-table-column label="试题类型" align="center" min-width="100">
          <template #default="scope">
            {{ getQuestionTypeText(scope.row.questionType) }}
          </template>
        </el-table-column>

        <el-table-column label="创建人" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.creator }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.createdAt }}
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" min-width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="180">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                查看
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import Pagination from "@/components/Pagination/index.vue";

defineOptions({
  name: "QuestionBank",
  inheritAttrs: false,
});

 
const loading = ref(false);
const total = ref(0);

 
const queryParams = reactive({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

// 表格数据
const tableData = ref([
  {
    id: 1,
    bankName: "安全生产知识题库",
    questionType: 1, // 1-单选题, 2-多选题, 3-判断题, 4-填空题, 5-简答题
    creator: "管理员",
    createdAt: "2024-03-20 10:13:21",
    status: 1, // 1-启用, 0-禁用
  },
  {
    id: 2,
    bankName: "消防安全题库",
    questionType: 2,
    creator: "张三",
    createdAt: "2024-03-19 15:30:45",
    status: 1,
  },
  {
    id: 3,
    bankName: "职业健康题库",
    questionType: 3,
    creator: "李四",
    createdAt: "2024-03-18 09:25:30",
    status: 1,
  },
  {
    id: 4,
    bankName: "环境保护题库",
    questionType: 4,
    creator: "王五",
    createdAt: "2024-03-17 14:18:12",
    status: 0,
  },
  {
    id: 5,
    bankName: "应急管理题库",
    questionType: 5,
    creator: "赵六",
    createdAt: "2024-03-16 11:42:55",
    status: 1,
  },
]);

// 题目类型映射
const questionTypeMap = {
  1: "单选题",
  2: "多选题",
  3: "判断题",
  4: "填空题",
  5: "简答题",
};

// 获取题目类型文本
function getQuestionTypeText(type: any) {
  return questionTypeMap[type] || "未知";
}

// 搜索查询
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

// 新增题目
function handleCreate() {
  // 跳转到新增页面或打开弹窗
  console.log("新增题目");
  ElMessage.success("新增功能待开发");
}

// 行操作
function onRowClick(type: string, row: any) {
  switch (type) {
    case "edit":
      console.log("编辑", row);
      ElMessage.success("编辑功能待开发");
      break;
    case "detail":
      console.log("查看详情", row);
      ElMessage.success("查看功能待开发");
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

// 删除操作
function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除题库"${row.bankName}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 这里应该调用删除API
      console.log("删除", row);
      ElMessage.success("删除成功");
      getData();
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 获取数据
const getData = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 这里应该调用实际的API
    total.value = tableData.value.length;
    loading.value = false;
  }, 500);
};

onBeforeMount(() => {});

onMounted(() => {
  getData();
});
</script>

<style scoped lang="scss">
.question-bank-container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding: 20px;

  .container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .left {
      display: flex;
      align-items: center;
      gap: 15px;

      .filter-row {
        width: 300px;
      }

      .btn {
        width: 100px;
        height: 40px;
        font-size: 14px;
      }
    }

    .right {
      .text-btn {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .content {
    flex: 1;
    background: #ffffff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .footer {
    margin-top: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
