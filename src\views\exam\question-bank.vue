<template>
  <div class="question-bank-layout">
    <!-- 侧边栏 -->
    <Sidebar v-if="showSidebar" />

    <!-- 主内容区域 -->
    <div
      class="question-bank-container"
      :class="{ 'with-sidebar': showSidebar }"
    >
      <div class="left-sidebar"></div>
      <div class="right-content">
        <div class="container-header">
          <div class="left">
            <div class="filter-row">
              <el-input
                v-model="queryParams.search"
                placeholder="请输入关键字"
                clearable
                size="large"
              />
            </div>
            <div class="btn primary-btn" @click="handleQuery">
              <i-ep-search /> 搜索
            </div>
          </div>
          <div class="right">
            <div class="text-btn" @click="handleCreate">
              <i-ep-circle-plus style="margin-right: 5px" />
              新增题库
            </div>
          </div>
        </div>

        <div class="content">
          <el-table
            v-loading="loading"
            element-loading-text="Loading"
            element-loading-background="#ffffffb4"
            :data="tableData"
            height="100%"
            border
            fit
            highlight-current-row
          >
            <el-table-column label="题库名称" align="center" min-width="120">
              <template #default="scope">
                {{ scope.row.bankName }}
              </template>
            </el-table-column>
            <el-table-column label="关联课程" align="center" min-width="100">
              <template #default="scope">
                {{ getQuestionTypeText(scope.row.questionType) }}
              </template>
            </el-table-column>
            <el-table-column label="题目数量" align="center" min-width="60">
              <template #default="scope">
                {{ scope.row.questionType }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="试题类型" align="center" min-width="100">
          <template #default="scope">
            {{ getQuestionTypeText(scope.row.questionType) }}
          </template>
        </el-table-column> -->

            <el-table-column label="创建人" align="center" min-width="100">
              <template #default="scope">
                {{ scope.row.creator }}
              </template>
            </el-table-column>

            <el-table-column label="创建时间" align="center" min-width="120">
              <template #default="scope">
                {{ scope.row.createdAt }}
              </template>
            </el-table-column>

            <el-table-column label="状态" align="center" min-width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status === 10 ? 'success' : 'danger'">
                  {{ scope.row.status === 10 ? "启用" : "停用" }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" min-width="220">
              <template #default="scope">
                <div class="option-btn">
                  <div
                    class="btn primary-btn"
                    @click="onRowClick('link', scope.row)"
                  >
                    关联课程
                  </div>
                  <div
                    class="btn"
                    :class="[
                      scope.row.status == 10 || scope.row.status == 30
                        ? 'light-green-btn'
                        : 'info-btn',
                    ]"
                    @click="onRowClick('status', scope.row)"
                  >
                    {{
                      scope.row.status == 10 || scope.row.status == 30
                        ? "启用"
                        : scope.row.status == 20
                        ? "停用"
                        : "--"
                    }}
                  </div>
                  <div
                    class="btn light-blue-btn"
                    @click="onRowClick('edit', scope.row)"
                  >
                    修改
                  </div>
                  <div
                    class="btn delete-btn"
                    @click="onRowClick('delete', scope.row)"
                  >
                    删除
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="footer">
          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getData"
          />
        </div>
      </div>
      <!-- 关联课程弹窗 -->
      <el-dialog
        class="link-course-dialog"
        v-model="linkCourseDialog.visible"
        :title="linkCourseDialog.title"
        :width="linkCourseDialog.width"
        :before-close="closeLinkCourseDialog"
        append-to-body
      >
        <div class="link-course-content">
          <div class="course-selection">
            <!-- 左侧：未关联课程 -->
            <div class="course-list">
              <div class="list-header">
                <span class="header-title">未关联课程</span>
              </div>
              <div class="search-box">
                <el-input
                  v-model="linkCourseForm.leftSearch"
                  placeholder="请输入名称"
                  clearable
                  size="large"
                >
                  <template #suffix>
                    <el-icon><i-ep-search /></el-icon>
                  </template>
                </el-input>
                <!-- 全选选项 -->
                <div class="select-all-box">
                  <el-checkbox
                    :model-value="isAllUnlinkedSelected"
                    :indeterminate="isUnlinkedIndeterminate"
                    @change="handleSelectAllUnlinked"
                  >
                    全选
                  </el-checkbox>
                </div>
              </div>
              <div class="course-items">
                <div
                  v-for="course in filteredUnlinkedCourses"
                  :key="course.id"
                  class="course-item custom-checkbox-circle checkbox-circle-orange"
                  @click="toggleCourseSelection(course, 'unlinked')"
                >
                  <el-checkbox
                    v-model="linkCourseForm.selectedUnlinkedIds"
                    :label="course.name"
                    @change="
                      (checked) =>
                        handleCheckboxChange(course, 'unlinked', checked)
                    "
                  />
                </div>
              </div>
            </div>

            <!-- 中间：操作按钮 -->
            <div class="operation-buttons">
              <div class="btn-group">
                <div
                  class="btn primary-btn"
                  @click="addCourseLink"
                  :class="{
                    'disabled-btn':
                      linkCourseForm.selectedUnlinkedIds.length === 0,
                  }"
                >
                  →
                </div>

                <div
                  class="btn primary-btn"
                  @click="removeCourseLink"
                  :class="{
                    'disabled-btn':
                      linkCourseForm.selectedLinkedIds.length === 0,
                  }"
                >
                  ←
                </div>
              </div>
            </div>

            <!-- 右侧：已关联课程 -->
            <div class="course-list">
              <div class="list-header">
                <span class="header-title">已关联课程</span>
              </div>
              <div class="search-box">
                <el-input
                  v-model="linkCourseForm.rightSearch"
                  placeholder="请输入名称"
                  clearable
                  size="large"
                >
                  <template #suffix>
                    <el-icon><i-ep-search /></el-icon>
                  </template>
                </el-input>
                <!-- 全选选项 -->
                <div class="select-all-box">
                  <el-checkbox
                    :model-value="isAllLinkedSelected"
                    :indeterminate="isLinkedIndeterminate"
                    @change="handleSelectAllLinked"
                  >
                    全选
                  </el-checkbox>
                </div>
              </div>
              <div class="course-items">
                <div
                  v-for="course in filteredLinkedCourses"
                  :key="course.id"
                  class="course-item custom-checkbox-circle checkbox-circle-orange"
                  @click="toggleCourseSelection(course, 'linked')"
                >
                  <!-- :model-value="
                    linkCourseForm.selectedLinkedIds.includes(course.id)
                  " -->
                  <el-checkbox
                    v-model="linkCourseForm.selectedLinkedIds"
                    :label="course.name"
                    @change="(checked: boolean) => handleCheckboxChange(course, 'linked', checked)"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 底部提示 -->
          <div class="tips">
            <span class="tip-text"
              >注：一个题库可关联多个课程；一个课程只能关联一个题库</span
            >
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <div class="btn cancel-btn" @click="closeLinkCourseDialog">
              取 消
            </div>
            <div class="btn primary-btn" @click="saveLinkCourse">保 存</div>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import Sidebar from "@/components/Sidebar.vue";

defineOptions({
  name: "QuestionBank",
  inheritAttrs: false,
});

// 控制侧边栏显示
const showSidebar = ref(true);

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const tableData = ref([
  {
    id: 1,
    bankName: "安全生产知识题库",
    questionType: 1, // 1-单选题, 2-多选题, 3-判断题, 4-填空题, 5-简答题
    creator: "管理员",
    createdAt: "2024-03-20 10:13:21",
    status: 10, // 1-启用, 0-禁用
  },
  {
    id: 2,
    bankName: "消防安全题库",
    questionType: 2,
    creator: "张三",
    createdAt: "2024-03-19 15:30:45",
    status: 10,
  },
  {
    id: 3,
    bankName: "职业健康题库",
    questionType: 3,
    creator: "李四",
    createdAt: "2024-03-18 09:25:30",
    status: 10,
  },
  {
    id: 4,
    bankName: "环境保护题库",
    questionType: 4,
    creator: "王五",
    createdAt: "2024-03-17 14:18:12",
    status: 20,
  },
  {
    id: 5,
    bankName: "应急管理题库",
    questionType: 5,
    creator: "赵六",
    createdAt: "2024-03-16 11:42:55",
    status: 10,
  },
]);

// 题目类型映射
const questionTypeMap = {
  1: "单选题",
  2: "多选题",
  3: "判断题",
  4: "填空题",
  5: "简答题",
};

// 关联课程弹窗数据
const linkCourseDialog = reactive<any>({
  visible: false,
  width: "50%",
  title: "关联课程",
});
const linkCourseForm = reactive<any>({
  currentQuestionBank: null as any,
  leftSearch: "",
  rightSearch: "",
  selectedUnlinkedIds: [] as number[], // 改为数组支持多选
  selectedLinkedIds: [] as number[], // 改为数组支持多选
});

// 模拟课程数据
const allCourses = ref([
  { id: 1, name: "安全生产基础知识", linked: false },
  { id: 2, name: "消防安全管理", linked: true },
  { id: 3, name: "职业健康防护", linked: false },
  { id: 4, name: "环境保护法规", linked: false },
  { id: 5, name: "应急救援技能", linked: true },
  { id: 6, name: "危险化学品管理", linked: false },
  { id: 7, name: "特种设备安全", linked: false },
  { id: 8, name: "建筑施工安全", linked: false },
]);

// 过滤未关联课程
const filteredUnlinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => !course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.leftSearch.toLowerCase())
    );
});

//  过滤已关联课程
const filteredLinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.rightSearch.toLowerCase())
    );
});

// 全选相关计算属性
// 未关联课程全选状态
const isAllUnlinkedSelected = computed(() => {
  const filtered = filteredUnlinkedCourses.value;
  return (
    filtered.length > 0 &&
    filtered.every((course) =>
      linkCourseForm.selectedUnlinkedIds.includes(course.id)
    )
  );
});

// 未关联课程半选状态
const isUnlinkedIndeterminate = computed(() => {
  const filtered = filteredUnlinkedCourses.value;
  const selectedCount = filtered.filter((course) =>
    linkCourseForm.selectedUnlinkedIds.includes(course.id)
  ).length;
  return selectedCount > 0 && selectedCount < filtered.length;
});

// 已关联课程全选状态
const isAllLinkedSelected = computed(() => {
  const filtered = filteredLinkedCourses.value;
  return (
    filtered.length > 0 &&
    filtered.every((course) =>
      linkCourseForm.selectedLinkedIds.includes(course.id)
    )
  );
});

// 已关联课程半选状态
const isLinkedIndeterminate = computed(() => {
  const filtered = filteredLinkedCourses.value;
  const selectedCount = filtered.filter((course) =>
    linkCourseForm.selectedLinkedIds.includes(course.id)
  ).length;
  return selectedCount > 0 && selectedCount < filtered.length;
});

onBeforeMount(() => {});

onMounted(() => {
  getData();
});

function getQuestionTypeText(type: any) {
  return questionTypeMap[type] || "未知";
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  console.log("新增题目");
  ElMessage.success("新增功能待开发");
}

function getData() {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 这里应该调用实际的API
    total.value = tableData.value.length;
    loading.value = false;
  }, 500);
}
function onRowClick(type: string, row: any) {
  switch (type) {
    case "link":
      console.log("关联课程", row);
      openLinkCourseDialog(row);
      break;
    case "status":
      const status =
        row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
      const message =
        row.status == 10 || row.status == 30
          ? "启用成功"
          : row.status == 20
          ? "停用成功"
          : "";
      const text = row.status == 20 ? "停用" : "启用";

      ElMessageBox.confirm("此操作将" + text + "该题库，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //
      });
      break;
    case "edit":
      console.log("编辑题库", row);
      ElMessage.success("编辑功能待开发");
      // 可以跳转到编辑页面或打开编辑弹窗
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除题库"${row.bankName}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      console.log("删除", row);
      ElMessage.success("删除成功");
      getData();
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 关联课程相关方法
function openLinkCourseDialog(row: any) {
  linkCourseForm.currentCourseData = row;
  linkCourseDialog.visible = true;
  linkCourseForm.leftSearch = "";
  linkCourseForm.rightSearch = "";
  linkCourseForm.selectedUnlinkedIds = [];
  linkCourseForm.selectedLinkedIds = [];
}

// 处理checkbox变化事件
function handleCheckboxChange(course: any, type: string, checked: boolean) {
  if (type === "unlinked") {
    if (checked) {
      // 添加到选中列表
      if (!linkCourseForm.selectedUnlinkedIds.includes(course.id)) {
        linkCourseForm.selectedUnlinkedIds.push(course.id);
      }
    } else {
      // 从选中列表移除
      const index = linkCourseForm.selectedUnlinkedIds.indexOf(course.id);
      if (index > -1) {
        linkCourseForm.selectedUnlinkedIds.splice(index, 1);
      }
    }
  } else {
    if (checked) {
      // 添加到选中列表
      if (!linkCourseForm.selectedLinkedIds.includes(course.id)) {
        linkCourseForm.selectedLinkedIds.push(course.id);
      }
    } else {
      // 从选中列表移除
      const index = linkCourseForm.selectedLinkedIds.indexOf(course.id);
      if (index > -1) {
        linkCourseForm.selectedLinkedIds.splice(index, 1);
      }
    }
  }
}

// 点击课程项切换选择状态
function toggleCourseSelection(course: any, type: string) {
  if (type === "unlinked") {
    const isSelected = linkCourseForm.selectedUnlinkedIds.includes(course.id);
    handleCheckboxChange(course, type, !isSelected);
  } else {
    const isSelected = linkCourseForm.selectedLinkedIds.includes(course.id);
    handleCheckboxChange(course, type, !isSelected);
  }
}

// 处理未关联课程全选
function handleSelectAllUnlinked(checked: boolean) {
  if (checked) {
    // 全选：将所有过滤后的未关联课程ID添加到选中列表
    const allUnlinkedIds = filteredUnlinkedCourses.value.map(
      (course) => course.id
    );
    linkCourseForm.selectedUnlinkedIds = [...allUnlinkedIds];
  } else {
    // 取消全选：清空选中列表
    linkCourseForm.selectedUnlinkedIds = [];
  }
}

// 处理已关联课程全选
function handleSelectAllLinked(checked: boolean) {
  if (checked) {
    // 全选：将所有过滤后的已关联课程ID添加到选中列表
    const allLinkedIds = filteredLinkedCourses.value.map((course) => course.id);
    linkCourseForm.selectedLinkedIds = [...allLinkedIds];
  } else {
    // 取消全选：清空选中列表
    linkCourseForm.selectedLinkedIds = [];
  }
}

function addCourseLink() {
  if (linkCourseForm.selectedUnlinkedIds.length > 0) {
    let successCount = 0;
    linkCourseForm.selectedUnlinkedIds.forEach((courseId) => {
      const course = allCourses.value.find((c) => c.id === courseId);
      if (course) {
        course.linked = true;
        successCount++;
      }
    });

    // 清空选中状态
    linkCourseForm.selectedUnlinkedIds = [];

    // if (successCount > 0) {
    //   ElMessage.success(`成功关联 ${successCount} 个课程`);
    // }
  }
}

function removeCourseLink() {
  if (linkCourseForm.selectedLinkedIds.length > 0) {
    let successCount = 0;
    linkCourseForm.selectedLinkedIds.forEach((courseId) => {
      const course = allCourses.value.find((c) => c.id === courseId);
      if (course) {
        course.linked = false;
        successCount++;
      }
    });

    // 清空选中状态
    linkCourseForm.selectedLinkedIds = [];

    // if (successCount > 0) {
    //   ElMessage.success(`成功取消关联 ${successCount} 个课程`);
    // }
  }
}

function closeLinkCourseDialog() {
  linkCourseDialog.visible = false;
  linkCourseForm.currentCourseData = null;
  linkCourseForm.selectedUnlinkedIds = [];
  linkCourseForm.selectedLinkedIds = [];
}

function saveLinkCourse() {
  console.log("保存关联课程", linkCourseForm.currentCourseData);
  ElMessage.success("保存成功");
  closeLinkCourseDialog();
}
</script>

<style scoped lang="scss">
.question-bank-layout {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.question-bank-container {
  flex: 1;
  height: 95%;
  margin: 20px;
  // background: #fff;
  // border-radius: 8px;
  // box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .left-sidebar {
    width: 15%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  }
  .right-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
    width: 84%;
    height: 100%;
  }
  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

// 关联课程弹窗样式
.link-course-dialog {
  .link-course-content {
    .course-selection {
      display: flex;
      gap: 20px;
      height: 400px;

      .course-list {
        flex: 1;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        display: flex;
        flex-direction: column;

        .list-header {
          background: #00918c;
          color: white;
          padding: 10px;
          text-align: center;
          font-weight: 500;

          .header-title {
            font-size: 14px;
          }
        }

        .search-box {
          padding: 10px;
          border-bottom: 1px solid #e4e7ed;

          .select-all-box {
            margin-top: 8px;
            padding: 4px 0;

            :deep(.el-checkbox) {
              .el-checkbox__label {
                font-size: 12px;
                color: #666;
              }

              .el-checkbox__input {
                &.is-checked .el-checkbox__inner {
                  background-color: #00918c;
                  border-color: #00918c;
                }

                &.is-indeterminate .el-checkbox__inner {
                  background-color: #00918c;
                  border-color: #00918c;
                }
              }
            }
          }
        }

        .course-items {
          flex: 1;
          overflow-y: auto;
          padding: 10px;

          .course-item {
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 5px;

            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }

      .operation-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;

        .btn-group {
          display: flex;
          flex-direction: column;
          gap: 10px;

          .primary-btn {
            width: 40px;
            height: 40px;
          }
        }
      }
    }

    .tips {
      margin-top: 20px;
      padding: 10px;
      background-color: #fff6f7;
      border: 1px solid #fbc4c4;
      border-radius: 4px;

      .tip-text {
        color: #e6a23c;
        font-size: 12px;
      }
    }
  }
}
</style>
