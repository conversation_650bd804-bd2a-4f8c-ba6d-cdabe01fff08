<template>
  <div class="question-bank-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增题库
        </div>
      </div>
    </div>

    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="题库名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.bankName }}
          </template>
        </el-table-column>

        <el-table-column label="试题类型" align="center" min-width="100">
          <template #default="scope">
            {{ getQuestionTypeText(scope.row.questionType) }}
          </template>
        </el-table-column>

        <el-table-column label="创建人" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.creator }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.createdAt }}
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" min-width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 10 ? 'success' : 'danger'">
              {{ scope.row.status === 10 ? "启用" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('link', scope.row)"
              >
                关联课程
              </div>
              <div
                class="btn"
                :class="[
                  scope.row.status == 10 || scope.row.status == 30
                    ? 'light-green-btn'
                    : 'info-btn',
                ]"
                @click="onRowClick('status', scope.row)"
              >
                {{
                  scope.row.status == 10 || scope.row.status == 30
                    ? "启用"
                    : scope.row.status == 20
                    ? "停用"
                    : "--"
                }}
              </div>
              <div
                class="btn light-blue-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 关联课程弹窗 -->
    <el-dialog
      class="link-course-dialog"
      v-model="linkCourseDialog.visible"
      :title="linkCourseDialog.title"
      :width="linkCourseDialog.width"
      :before-close="closeLinkCourseDialog"
      append-to-body
    >
      <div class="link-course-content">
        <div class="course-selection">
          <!-- 左侧：未关联课程 -->
          <div class="course-list">
            <div class="list-header">
              <span class="header-title">未关联课程</span>
            </div>
            <div class="search-box">
              <el-input
                v-model="linkCourseForm.leftSearch"
                placeholder="请输入名称"
                clearable
                size="large"
              >
                <template #suffix>
                  <el-icon><i-ep-search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="course-items">
              <div
                v-for="course in filteredUnlinkedCourses"
                :key="course.id"
                class="course-item custom-checkbox"
                @click="selectCourse(course, 'unlinked')"
              >
                <el-checkbox
                  :model-value="
                    linkCourseForm.selectedUnlinked?.id === course.id
                  "
                  :label="course.name"
                  @change="() => selectCourse(course, 'unlinked')"
                />
              </div>
            </div>
          </div>

          <!-- 中间：操作按钮 -->
          <div class="operation-buttons">
            <div class="btn-group">
              <div
                class="btn primary-btn"
                @click="addCourseLink"
                :class="{
                  'disabled-btn': !linkCourseForm.selectedUnlinked,
                }"
              >
                →
              </div>

              <div
                class="btn primary-btn"
                @click="removeCourseLink"
                :class="{
                  'disabled-btn': !linkCourseForm.selectedLinked,
                }"
              >
                ←
              </div>
            </div>
          </div>

          <!-- 右侧：已关联课程 -->
          <div class="course-list">
            <div class="list-header">
              <span class="header-title">已关联课程</span>
            </div>
            <div class="search-box">
              <el-input
                v-model="linkCourseForm.rightSearch"
                placeholder="请输入名称"
                clearable
                size="large"
              >
                <template #suffix>
                  <el-icon><i-ep-search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="course-items">
              <div
                v-for="course in filteredLinkedCourses"
                :key="course.id"
                class="course-item custom-checkbox"
                @click="selectCourse(course, 'linked')"
              >
                <el-checkbox
                  :model-value="linkCourseForm.selectedLinked?.id === course.id"
                  :label="course.name"
                  @change="() => selectCourse(course, 'linked')"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 底部提示 -->
        <div class="tips">
          <span class="tip-text"
            >注：一个题库可关联多个课程；一个课程只能关联一个题库</span
          >
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeLinkCourseDialog">取 消</div>
          <div class="btn primary-btn" @click="saveLinkCourse">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";

import { parseTime, formatStringDate } from "@/utils";
import { checkUserPermission } from "@/utils/auth";
defineOptions({
  name: "QuestionBank",
  inheritAttrs: false,
});

const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const tableData = ref([
  {
    id: 1,
    bankName: "安全生产知识题库",
    questionType: 1, // 1-单选题, 2-多选题, 3-判断题, 4-填空题, 5-简答题
    creator: "管理员",
    createdAt: "2024-03-20 10:13:21",
    status: 10, // 1-启用, 0-禁用
  },
  {
    id: 2,
    bankName: "消防安全题库",
    questionType: 2,
    creator: "张三",
    createdAt: "2024-03-19 15:30:45",
    status: 10,
  },
  {
    id: 3,
    bankName: "职业健康题库",
    questionType: 3,
    creator: "李四",
    createdAt: "2024-03-18 09:25:30",
    status: 10,
  },
  {
    id: 4,
    bankName: "环境保护题库",
    questionType: 4,
    creator: "王五",
    createdAt: "2024-03-17 14:18:12",
    status: 20,
  },
  {
    id: 5,
    bankName: "应急管理题库",
    questionType: 5,
    creator: "赵六",
    createdAt: "2024-03-16 11:42:55",
    status: 10,
  },
]);

// 题目类型映射
const questionTypeMap = {
  1: "单选题",
  2: "多选题",
  3: "判断题",
  4: "填空题",
  5: "简答题",
};

// 关联课程弹窗数据
const linkCourseDialog = reactive<any>({
  visible: false,
  width: "50%",
  title: "关联课程",
});
const linkCourseForm = reactive<any>({
  currentQuestionBank: null as any,
  leftSearch: "",
  rightSearch: "",
  selectedUnlinked: null as any,
  selectedLinked: null as any,
});

// 模拟课程数据
const allCourses = ref([
  { id: 1, name: "安全生产基础知识", linked: false },
  { id: 2, name: "消防安全管理", linked: true },
  { id: 3, name: "职业健康防护", linked: false },
  { id: 4, name: "环境保护法规", linked: false },
  { id: 5, name: "应急救援技能", linked: true },
  { id: 6, name: "危险化学品管理", linked: false },
  { id: 7, name: "特种设备安全", linked: false },
  { id: 8, name: "建筑施工安全", linked: false },
]);

// 过滤未关联课程
const filteredUnlinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => !course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.leftSearch.toLowerCase())
    );
});

//  过滤已关联课程
const filteredLinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.rightSearch.toLowerCase())
    );
});

onBeforeMount(() => {});

onMounted(() => {
  getData();
});

function getQuestionTypeText(type: any) {
  return questionTypeMap[type] || "未知";
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  console.log("新增题目");
  ElMessage.success("新增功能待开发");
}

function getData() {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 这里应该调用实际的API
    total.value = tableData.value.length;
    loading.value = false;
  }, 500);
}
function onRowClick(type: string, row: any) {
  switch (type) {
    case "link":
      console.log("关联课程", row);
      openLinkCourseDialog(row);
      break;
    case "status":
      const status =
        row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
      const message =
        row.status == 10 || row.status == 30
          ? "启用成功"
          : row.status == 20
          ? "停用成功"
          : "";
      const text = row.status == 20 ? "停用" : "启用";

      ElMessageBox.confirm("此操作将" + text + "该题库，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //
      });
      break;
    case "edit":
      console.log("编辑题库", row);
      ElMessage.success("编辑功能待开发");
      // 可以跳转到编辑页面或打开编辑弹窗
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除题库"${row.bankName}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      console.log("删除", row);
      ElMessage.success("删除成功");
      getData();
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 关联课程相关方法
function openLinkCourseDialog(row: any) {
  linkCourseForm.currentCourseData = row;
  linkCourseDialog.visible = true;
  linkCourseForm.leftSearch = "";
  linkCourseForm.rightSearch = "";
  linkCourseForm.selectedUnlinked = null;
  linkCourseForm.selectedLinked = null;
}

function selectCourse(course: any, type: string) {
  if (type === "unlinked") {
    // 如果点击的是已选中的课程，则取消选择
    if (linkCourseForm.selectedUnlinked?.id === course.id) {
      linkCourseForm.selectedUnlinked = null;
    } else {
      // 否则选择新的课程（实现单选）
      linkCourseForm.selectedUnlinked = course;
    }
    // 清除另一侧的选择
    linkCourseForm.selectedLinked = null;
  } else {
    // 如果点击的是已选中的课程，则取消选择
    if (linkCourseForm.selectedLinked?.id === course.id) {
      linkCourseForm.selectedLinked = null;
    } else {
      // 否则选择新的课程（实现单选）
      linkCourseForm.selectedLinked = course;
    }
    // 清除另一侧的选择
    linkCourseForm.selectedUnlinked = null;
  }
}

function addCourseLink() {
  if (linkCourseForm.selectedUnlinked) {
    const course = allCourses.value.find(
      (c) => c.id === linkCourseForm.selectedUnlinked.id
    );
    if (course) {
      course.linked = true;
      linkCourseForm.selectedUnlinked = null;
      ElMessage.success("课程关联成功");
    }
  }
}

function removeCourseLink() {
  if (linkCourseForm.selectedLinked) {
    const course = allCourses.value.find(
      (c) => c.id === linkCourseForm.selectedLinked.id
    );
    if (course) {
      course.linked = false;
      linkCourseForm.selectedLinked = null;
      ElMessage.success("取消关联成功");
    }
  }
}

function closeLinkCourseDialog() {
  linkCourseDialog.visible = false;
  linkCourseForm.currentCourseData = null;
  linkCourseForm.selectedUnlinked = null;
  linkCourseForm.selectedLinked = null;
}

function saveLinkCourse() {
  console.log("保存关联课程", linkCourseForm.currentCourseData);
  ElMessage.success("保存成功");
  closeLinkCourseDialog();
}
</script>

<style scoped lang="scss">
.question-bank-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

// 关联课程弹窗样式
.link-course-dialog {
  .link-course-content {
    .course-selection {
      display: flex;
      gap: 20px;
      height: 400px;

      .course-list {
        flex: 1;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        display: flex;
        flex-direction: column;

        .list-header {
          background: #00918c;
          color: white;
          padding: 10px;
          text-align: center;
          font-weight: 500;

          .header-title {
            font-size: 14px;
          }
        }

        .search-box {
          padding: 10px;
          border-bottom: 1px solid #e4e7ed;
        }

        .course-items {
          flex: 1;
          overflow-y: auto;
          padding: 10px;

          .course-item {
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 5px;

            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }

      .operation-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;

        .btn-group {
          display: flex;
          flex-direction: column;
          gap: 10px;

          .primary-btn {
            width: 40px;
            height: 40px;
          }
        }
      }
    }

    .tips {
      margin-top: 20px;
      padding: 10px;
      background-color: #fff6f7;
      border: 1px solid #fbc4c4;
      border-radius: 4px;

      .tip-text {
        color: #e6a23c;
        font-size: 12px;
      }
    }
  }
}
</style>
