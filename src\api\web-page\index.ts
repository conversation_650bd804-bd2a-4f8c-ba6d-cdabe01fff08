import request from "@/utils/request";
import { AxiosPromise } from "axios";

//banner管理
export function getBanners(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/banners",
    method: "get",
    params: queryParams,
  });
}
export function getBannersDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/banners/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param dat哦·                                       */
export function addBanners(data: any) {
  return request({
    url: "/gkadmin/v1/banners",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateBanners(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/banners/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteBanners(ids: string) {
  return request({
    url: "/gkadmin/v1/banners/" + ids,
    method: "delete",
  });
}

//栏目管理
export function getSections(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/sections",
    method: "get",
    params: queryParams,
  });
}
export function getSectionsDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/sections/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param dat哦·                                       */
export function addSections(data: any) {
  return request({
    url: "/gkadmin/v1/sections",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateSections(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/sections/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteSections(ids: string) {
  return request({
    url: "/gkadmin/v1/sections/" + ids,
    method: "delete",
  });
}
