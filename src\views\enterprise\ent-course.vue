<template>
  <div class="ent-course-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn"><i-ep-search /> 搜索</div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.ent_id"
            placeholder="请选择企业"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in entOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.status"
            placeholder="请选择状态"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="right"></div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="名称" align="center" prop="name" min-width="80">
          <template #default="scope">
            <div class="cover-name">
              <img :src="scope.row.thumb" alt="" class="cover-img" />
              <span>{{ scope.row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="所属企业" align="center" min-width="60">
          <template #default="scope">
            {{ scope.row.enterprise.name }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
          prop="createTime"
          min-width="50"
        >
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="所属分类" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.categories?.name||'--' }}
          </template>
        </el-table-column> -->
        <el-table-column label="上架时间" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.published_at }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                v-if="scope.row.status == 30 || scope.row.status == 10"
                class="btn light-green-btn"
                @click="onRowClick('available', scope.row)"
                v-show="enableList"
              >
                上架
              </div>
              <div
                v-if="scope.row.status == 20"
                class="btn info-btn"
                @click="onRowClick('removed', scope.row)"
                v-show="enableList"
              >
                下架
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <!-- <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import {
  getEnterprises,
  getEntCourses,
  updateEntCourses,
} from "@/api/enterprise";
import { parseTime } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "EntCourse",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const sortOptions = reactive<any>([]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  ent_id: "",
  status: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref<any>([]);

const entOptions = ref<any>([]);
const statusOptions = reactive<any>([
  {
    value: 20,
    label: "上架",
  },
  {
    value: 30,
    label: "下架",
  },
]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "编辑中", color: "#409eff" },
  20: { type: "success", label: "上架", color: "#2ab7b0" },
  30: { type: "info", label: "下架", color: "#909399" },
  40: { type: "danger", label: "删除", color: "#f56c6c" },
  50: { type: "warning", label: "过期", color: "#e6a23c" },
  // add more status mappings as needed
});
// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "35%",
  title: "详情",
});

// 用户表单数据
const formData = reactive<any>({
  status: 1,
});

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });

onMounted(() => {
  getEnt();
  getData();
});

const enableList = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 15);
});

function getEnt() {
  const params = {
    page: 1,
    pageSize: 2000,
  };
  getEnterprises(params).then((res: any) => {
    if (res.status == 200) {
      entOptions.value = res.data.enterprises.map((item: any) => {
        item.label = item.name;
        item.value = item.id;
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
    }
  });
}
function getData() {
  loading.value = true;
  const params = {
    ent_id: queryParams.ent_id || undefined,
    status: queryParams.status || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getEntCourses(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.ent_courses.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          item.published_at = parseTime(
            item.published_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function onRowClick(type: string, row: any) {
  ElMessageBox.confirm(
    `此操作将${type == "available" ? "上架" : "下架"}课程，是否继续?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    if (type === "available") {
      const data = {
        status: 20,
      };
      updateEntCourses(row.id, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "上架成功!",
          });
          getData();
        }
      });
    } else if (type == "removed") {
      const data = {
        status: 30,
      };
      updateEntCourses(row.id, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "下架成功!",
          });
          getData();
        }
      });
    }
  });
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

// function closeDialog() {
//   dialog.visible = false;
// }
// function handleSubmit() {}
</script>

<style scoped lang="scss">
.ent-course-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      display: flex;
      align-items: center;

      .cover-img {
        // width: 150px !important;
        // height: 100px !important;
        width: 162px !important;
        height: 86px !important;
        object-fit: cover;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
