<template>
  <div class="dashboard-container">
    <!-- 数据卡片 -->
    <el-row :gutter="10" class="card-container mt-3">
      <el-col :xs="24" :sm="12" :lg="6">
        <div class="card-content green">
          <div class="top">
            <div class="text-left">课程数</div>
            <div class="text-right">
              <span> {{ courseStat.total }}</span>
              个
            </div>
          </div>
          <div class="bottom">当月新增： {{ courseStat.month_added }}</div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="6">
        <div class="card-content blue">
          <div class="top">
            <div class="text-left">用户人数</div>
            <div class="text-right">
              <span> {{ userStat.total }}</span>
              个
            </div>
          </div>
          <div class="bottom">当月新增： {{ userStat.month_added }}</div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="6">
        <div class="card-content purple">
          <div class="top">
            <div class="text-left">企业用户数</div>
            <div class="text-right">
              <span> {{ entStat.total }}</span>
              个
            </div>
          </div>
          <div class="bottom">当月新增： {{ entStat.month_added }}</div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :lg="6">
        <div class="card-content yellow">
          <div class="top">
            <div class="text-left">订单总数</div>
            <div class="text-right">
              <span> {{ orderStat.total }}</span>
              个
            </div>
          </div>
          <div class="bottom">当月新增： {{ orderStat.month_added }}</div>
        </div>
      </el-col>
    </el-row>

    <!-- Echarts 图表 -->
    <!-- <el-row :gutter="10" class="mt-3">
      


    </el-row> -->
    <div class="chart mt-3">
      <BarChart
        id="barChart"
        width="100%"
        :chartData="amountStats"
        :gridTop="'20%'"
      />
    </div>
    <div class="chart mt-3">
      <LineChart
        id="lineChart"
        width="100%"
        :chartData="durationStats"
        :gridTop="'20%'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dashboard",
  inheritAttrs: false,
});

import { useUserStore } from "@/store/modules/user";
import { useTransition, TransitionPresets } from "@vueuse/core";
import { getDashboard } from "@/api/dashborad";
import { graphic } from "echarts";
const userStore = useUserStore();
const date: Date = new Date();

// const greetings = computed(() => {
//   const hours = date.getHours();
// });

const duration = 5000;

// 课程统计
const courseStat = reactive<any>({
  total: 0,
  month_added: 0,
});
const courseOutput = useTransition(courseStat.total, {
  duration: duration,
  transition: TransitionPresets.easeOutExpo,
});

// 学员统计
const userStat = reactive<any>({
  total: 0,
  month_added: 0,
});
const userStatOutput = useTransition(userStat.total, {
  duration: duration,
  transition: TransitionPresets.easeOutExpo,
});

// 企业统计
const entStat = reactive<any>({
  total: 0,
  month_added: 0,
});
const entStatOutput = useTransition(entStat.total, {
  duration: duration,
  transition: TransitionPresets.easeOutExpo,
});

// 订单统计
const orderStat = reactive<any>({
  total: 0,
  month_added: 0,
});
const orderStatOutput = useTransition(orderStat.total, {
  duration: duration,
  transition: TransitionPresets.easeOutExpo,
});

// 订单金额趋势
const amountStats = ref<any>([]);

// 学习时长趋势
const durationStats = ref<any>([]);

onMounted(() => {
  getData();
});

function getData() {
  getDashboard()
    .then((res: any) => {
      Object.assign(courseStat, {
        total: res.data.course_stat.total,
        month_added: res.data.course_stat.month_added,
      });
      Object.assign(userStat, {
        total: res.data.user_stat.total,
        month_added: res.data.user_stat.month_added,
      });
      Object.assign(entStat, {
        total: res.data.ent_stat.total,
        month_added: res.data.ent_stat.month_added,
      });
      Object.assign(orderStat, {
        total: res.data.order_stat.total,
        month_added: res.data.order_stat.month_added,
      });
      amountStats.value = res.data.amount_stats;
      durationStats.value = res.data.duration_stats;
    })
    .catch((e) => {
      // loading.value = false;
    });
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100%;
  width: 100%;
  position: relative;
  padding: 14px;
  .green {
    background: url("@/assets/images/green.png") no-repeat;
    background-size: 100% 100%;
    span {
      color: #0d8883;
    }
  }
  .blue {
    background: url("@/assets/images/blue.png") no-repeat;
    background-size: 100% 100%;
    span {
      color: #3587e6;
    }
  }
  .yellow {
    background: url("@/assets/images/yellow.png") no-repeat;
    background-size: 100% 100%;
    span {
      color: #b5a20e;
    }
  }
  .purple {
    background: url("@/assets/images/purple.png") no-repeat !important;
    background-size: 100% 100%;
    span {
      color: #3826bf;
    }
  }

  .card-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    box-shadow: 0px 0px 10px 1px rgba(116, 188, 169, 0.18);
    border-radius: 8px 8px 8px 8px;
    .top {
      padding: 10px 30px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .text-left {
      font-weight: 600;
      font-size: 24px;
      color: #3b4664;
    }
    .text-right {
      font-weight: normal;
      font-size: 15px;
      color: #3b4664;
      span {
        font-weight: 600;
        font-size: 40px;
      }
    }
    .bottom {
      padding: 10px 30px;
      font-weight: 400;
      font-size: 18px;
      color: #3b4664;
      width: 100%;
    }
  }

  .svg-icon {
    fill: currentcolor !important;
  }
  .card-container {
    width: 100%;
    height: 18%;
    .el-col {
      height: 100%;
    }
  }
  .chart {
    width: 100%;
    height: 39%;
  }
}
</style>
