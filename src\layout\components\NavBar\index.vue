<!--
 * @Author: czf <EMAIL>
 * @Date: 2024-10-28 17:01:07
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-10-30 10:42:49
 * @FilePath: \gkadmin\src\layout\components\NavBar\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";

const appStore = useAppStore();

/**
 * 左侧菜单栏显示/隐藏
 */
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <!-- 左侧面包屑 -->
    <div class="flex">
      <!-- <hamburger
        :is-active="appStore.sidebar.opened"
        @toggle-click="toggleSideBar"
      /> -->
      <!-- <breadcrumb class="ml-4 breadcrumb-title" /> -->
    </div>

    <!-- 右侧导航设置 -->
    <div class="flex">
      <NavRight />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  background-color: #fff;
  box-shadow: 0 0 1px #0003;
  // position: fixed;
}
</style>
