import { RouteRecordRaw } from "vue-router";
import { defineStore } from "pinia";
import { constantRoutes, asyncRoutes } from "@/router";
import { useUserStoreHook } from "@/store/modules/user";
import { store } from "@/store";
import { resourcesCollapse } from "@/utils";
// import { listRoutes } from "@/api/menu";
import router from "@/router";
// const modules = import.meta.glob("../../views/**/**.vue");
// const Layout = () => import("@/layout/index.vue");

/**
 * Use meta.role to determine if the current user has permission
 *
 * @param roles 用户角色集合
 * @param route 路由
 * @returns
 */
const hasPermission = (roles: string[], route: RouteRecordRaw) => {
  if (route.meta && route.meta.roles) {
    // 角色【超级管理员】拥有所有权限，忽略校验
    if (roles.includes("ROOT")) {
      return true;
    }
    return roles.some((role) => {
      if (route.meta?.roles) {
        return route.meta.roles.includes(role);
      }
    });
  }
  return false;
};

/**
 * 递归过滤有权限/或者目录的异步(动态)路由
 *
 * @param routes 接口返回的异步(动态)路由
 * @param roles 用户角色集合
 * @returns 返回用户有权限的异步(动态)路由
 */
const filterAsyncRoutes = (routes: RouteRecordRaw[], roles: string[]) => {
  const asyncRoutes: RouteRecordRaw[] = [];
  // const seqRoutes=resourcesCollapse(asyncRoutes,userMenus)    // 菜单排序和判断有无目录？

  routes.forEach((route) => {
    const tmpRoute = { ...route }; // ES6扩展运算符复制新对象
    if (!route.name) {
      tmpRoute.name = route.path;
    }
    // 判断用户(角色)是否有该路由的访问权限
    if (hasPermission(roles, tmpRoute)) {
      // if (tmpRoute.component?.toString() == "Layout") {
      //   tmpRoute.component = Layout;
      // } else {
      //   const component = modules[`../../views/${tmpRoute.component}.vue`];
      //   if (component) {
      //     tmpRoute.component = component;
      //   } else {
      //     tmpRoute.component = modules[`../../views/error-page/404.vue`];
      //   }
      // }
      if (tmpRoute.children) {
        tmpRoute.children = filterAsyncRoutes(tmpRoute.children, roles);
      }
      asyncRoutes.push(tmpRoute);
    }
  });

  return asyncRoutes;
};

export function routeAddLabel(routes: any, treeObj: any) {
  if (routes) {
    routes.forEach((item: any) => {
      const tempChildren: any = [];
      routeAddLabel(item.children, tempChildren);
      let tempItem = [];
      if (tempChildren.length == 0) {
        tempItem = Object.assign({}, item, {
          label: (item.meta && item.meta.title) || item.children[0]?.meta.title,
        });
      } else {
        tempItem = Object.assign({}, item, {
          label: item.meta && item.meta.title,
          children: tempChildren,
        });
      }
      treeObj.push(tempItem);
    });
  }
}
export function filterRoutesByMenus(routes: any, menus: any) {
  const temp: any = [];
  // menus = filterRepeatMenu(menus);
  if (routes && menus) {
    routes.forEach((route: any, index: any) => {
      // asyncRoutes避免空match循环已经匹配过一次，label和meta.title已经变更
      // meta.title和label已经变成menu.name
      // meta.title->name,label->meta.title=>name,label!=uni_name
      // 后续匹配导致部分目录不显示
      // 增加判断 || route.label === menu.name TODO:可继续优化？
      menus.forEach((menu: any) => {
        if (route.label === menu.uni_name || route.label === menu.name) {
          route.meta.title = menu.name;
          temp.push(
            Object.assign({}, route, {
              children: filterRoutesByMenus(route.children, menu.children),
            })
          );
        } else if (
          route.children &&
          route.children[0] &&
          route.children[0].label == menu.uni_name
        ) {
          route.children[0].meta.title = menu.name;
          temp.push(Object.assign({}, route));
        }
      });
    });
  }

  return temp;
}

// 过滤重复的路由
function filterRepeatMenu(menus: any) {
  if (menus) {
    const idSet = new Set();
    const pathSet = new Set();
    const tempMenu: any = [];
    menus.forEach((menu: any) => {
      if (menu.children && menu.children.length > 0) {
        tempMenu.children = filterRepeatMenu(menu.children);
      }
      if (pathSet.has(menu.path)) {
        return;
      } else {
        pathSet.add(menu.path);
        tempMenu.push(menu);
      }
    });

    return tempMenu;
  }
}

// setup
export const usePermissionStore = defineStore(
  "permission",
  () => {
    //根据返回的菜单权限或者目录匹配路由
    // state
    const routes = ref<RouteRecordRaw[]>([]);
    const menus = ref<[]>([]);
    const roleMenus = ref<[]>([]);

    // actions
    function setRoutes(newRoutes: RouteRecordRaw[]) {
      routes.value = constantRoutes.concat(newRoutes);
    }
    function setMenus(newMenus: []) {
      routes.value = constantRoutes.concat(newMenus);
    }
    /**
     * 生成动态路由
     *
     * @param roles 用户角色集合
     * @returns
     */
    function generateRoutes(menus: any) {
      return new Promise<RouteRecordRaw[]>((resolve, reject) => {
        routes.value = [];
        const tempRoute: any = [];
        routeAddLabel(asyncRoutes, tempRoute);
        const accessedRoutes = filterRoutesByMenus(tempRoute, menus);
        // const accessedRoutes = asyncRoutes;
        setRoutes(accessedRoutes);
        resolve(accessedRoutes);
      });
    }

    /**
     * 混合模式左侧菜单
     */
    const mixLeftMenu = ref<RouteRecordRaw[]>([]);
    function getMixLeftMenu(activeTop: string) {
      routes.value.forEach((item) => {
        console.log("mixLeftMenu", item);
        if (item.path === activeTop) {
          mixLeftMenu.value = item.children || [];
        }
      });
    }
    async function initRouter(menus: any) {
      // const userStore = useUserStoreHook();
      routes.value = [];
      const res = await generateRoutes(menus);
      console.log("second---getUserInfo的initRouter匹配路由", res);
      res.forEach((route: any) => {
        router.addRoute(route);
      });
    }
    return {
      routes,
      setRoutes,
      generateRoutes,
      getMixLeftMenu,
      initRouter,
      mixLeftMenu,
    };
  }
  // {
  //   persist: {
  //     enabled: true, //开启存储
  //     // storage:sessionStorage,还可以自定义存入对应的如下
  //     //不写以下也可以 默认是sessionStorage
  //     strategies: [{ storage: localStorage, paths: ["routes"] }],
  //   },
  // }
);

// 非setup
export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
