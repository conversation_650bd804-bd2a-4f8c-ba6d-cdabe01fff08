// mock/dict.ts
var dict_default = [
  {
    url: "/api/v1/dict/:code/options",
    method: "get",
    response: ({ url }) => {
      const typeCode = url.match(/\/api\/v1\/dict\/(\w+)\/options/)[1];
      let list = null;
      if (typeCode == "gender") {
        list = [
          {
            value: "1",
            label: "\u7537"
          },
          {
            value: "2",
            label: "\u5973"
          },
          {
            value: "0",
            label: "\u672A\u77E5"
          }
        ];
      }
      return {
        code: "00000",
        data: list,
        msg: "\u4E00\u5207ok"
      };
    }
  }
];
export {
  dict_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsibW9jay9kaWN0LnRzIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX2luamVjdGVkX2ZpbGVuYW1lX18gPSBcIlc6XFxcXFx1OEQ1Qlx1NUI4OVx1NzI2OVx1ODA1NFxcXFxna3dlYmRlbW9cXFxcbW9ja1xcXFxkaWN0LnRzXCI7Y29uc3QgX19pbmplY3RlZF9kaXJuYW1lX18gPSBcIlc6XFxcXFx1OEQ1Qlx1NUI4OVx1NzI2OVx1ODA1NFxcXFxna3dlYmRlbW9cXFxcbW9ja1wiO2NvbnN0IF9faW5qZWN0ZWRfaW1wb3J0X21ldGFfdXJsX18gPSBcImZpbGU6Ly8vVzovJUU4JUI1JTlCJUU1JUFFJTg5JUU3JTg5JUE5JUU4JTgxJTk0L2drd2ViZGVtby9tb2NrL2RpY3QudHNcIjtpbXBvcnQgeyBNb2NrTWV0aG9kIH0gZnJvbSBcInZpdGUtcGx1Z2luLW1vY2tcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFtcclxuICB7XHJcbiAgICB1cmw6IFwiL2FwaS92MS9kaWN0Lzpjb2RlL29wdGlvbnNcIixcclxuICAgIG1ldGhvZDogXCJnZXRcIixcclxuICAgIHJlc3BvbnNlOiAoeyB1cmwgfSkgPT4ge1xyXG4gICAgICBjb25zdCB0eXBlQ29kZSA9IHVybC5tYXRjaCgvXFwvYXBpXFwvdjFcXC9kaWN0XFwvKFxcdyspXFwvb3B0aW9ucy8pWzFdO1xyXG5cclxuICAgICAgbGV0IGxpc3QgPSBudWxsO1xyXG5cclxuICAgICAgaWYgKHR5cGVDb2RlID09IFwiZ2VuZGVyXCIpIHtcclxuICAgICAgICBsaXN0ID0gW1xyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICB2YWx1ZTogXCIxXCIsXHJcbiAgICAgICAgICAgIGxhYmVsOiBcIlx1NzUzN1wiLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICAgIHtcclxuICAgICAgICAgICAgdmFsdWU6IFwiMlwiLFxyXG4gICAgICAgICAgICBsYWJlbDogXCJcdTU5NzNcIixcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICB7XHJcbiAgICAgICAgICAgIHZhbHVlOiBcIjBcIixcclxuICAgICAgICAgICAgbGFiZWw6IFwiXHU2NzJBXHU3N0U1XCIsXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIF07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgY29kZTogXCIwMDAwMFwiLFxyXG4gICAgICAgIGRhdGE6IGxpc3QsXHJcbiAgICAgICAgbXNnOiBcIlx1NEUwMFx1NTIwN29rXCIsXHJcbiAgICAgIH07XHJcbiAgICB9LFxyXG4gIH0sXHJcbl0gYXMgTW9ja01ldGhvZFtdO1xyXG4iXSwKICAibWFwcGluZ3MiOiAiO0FBRUEsSUFBTyxlQUFRO0FBQUEsRUFDYjtBQUFBLElBQ0UsS0FBSztBQUFBLElBQ0wsUUFBUTtBQUFBLElBQ1IsVUFBVSxDQUFDLEVBQUUsSUFBSSxNQUFNO0FBQ3JCLFlBQU0sV0FBVyxJQUFJLE1BQU0saUNBQWlDLEVBQUUsQ0FBQztBQUUvRCxVQUFJLE9BQU87QUFFWCxVQUFJLFlBQVksVUFBVTtBQUN4QixlQUFPO0FBQUEsVUFDTDtBQUFBLFlBQ0UsT0FBTztBQUFBLFlBQ1AsT0FBTztBQUFBLFVBQ1Q7QUFBQSxVQUNBO0FBQUEsWUFDRSxPQUFPO0FBQUEsWUFDUCxPQUFPO0FBQUEsVUFDVDtBQUFBLFVBQ0E7QUFBQSxZQUNFLE9BQU87QUFBQSxZQUNQLE9BQU87QUFBQSxVQUNUO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFFQSxhQUFPO0FBQUEsUUFDTCxNQUFNO0FBQUEsUUFDTixNQUFNO0FBQUEsUUFDTixLQUFLO0FBQUEsTUFDUDtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQ0Y7IiwKICAibmFtZXMiOiBbXQp9Cg==
