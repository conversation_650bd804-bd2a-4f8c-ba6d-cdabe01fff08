import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * 上传文件
 *
 * @param file
 */
export function uploadFileApi(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/api/v1/files",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 删除文件
 *
 * @param filePath 文件完整路径
 */
export function deleteFileApi(filePath?: string) {
  return request({
    url: "/api/v1/files",
    method: "delete",
    params: { filePath: filePath },
  });
}
//以上为mock测试，待处理注释

//VOD获取临时权限
export function getVODTokens() {
  return request({
    url: "/gkapi/v1/hwtokens",
    method: "GET",
  });
}
//VOD媒资处理
export function postVodAsset(data: any) {
  // asset_id
  return request({
    url: "/gkapi/v1/asset_process",
    method: "POST",
    data,
  });
}
//媒资详情
export function getVodAsset(id: any) {
  // id
  return request({
    url: "/gkapi/v1/asset_info/" + id,
    method: "GET",
    // params,
  });
}

//OBS获取临时权限
export function getOBSTokens() {
  return request({
    url: "/gkapi/v1/hwsecuritytokens",
    method: "GET",
  });
}
