// 全局SCSS变量

:root {
  // --menuBg: #304156;
  // --menuText: #bfcbd9;
  // --menuActiveText: #409eff;
  // --menuHover: #263445;
  // --subMenuBg: #1f2d3d;
  // --subMenuActiveText: #f4f4f5;
  // --subMenuHover: #001528;
  --menuBg: #ffffff;
  --menuText: #3b4664;
  --menuActiveText: #ffffff;
  --menuHover: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
  --subMenuBg: #ffffff;
  --subMenuActiveText: #ffffff;
  --subMenuHover: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);

  // wang-editor textarea
  --w-e-textarea-slight-border-color: var(--el-color-primary);
  --w-e-textarea-slight-bg-color: rgb(var(--el-color-primary-rgb) 0.1);
  --w-e-textarea-selected-border-color: var(--el-color-primary);
}

$menuBg: var(--menuBg);
$menuText: var(--menuText);
$menuActiveText: var(--menuActiveText);
$menuHover: var(--menuHover);

$subMenuBg: var(--subMenuBg);
$subMenuActiveText: var(--subMenuActiveText);
$subMenuHover: var(--subMenuHover);

$sideBarWidth: 210px;
