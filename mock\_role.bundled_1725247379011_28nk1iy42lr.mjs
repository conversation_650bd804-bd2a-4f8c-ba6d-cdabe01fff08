// mock/role.ts
var role_default = [
  {
    url: "/api/v1/roles/options",
    method: "get",
    response: () => {
      return {
        code: "00000",
        data: [
          {
            value: 2,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            value: 4,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54581"
          },
          {
            value: 5,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54582"
          },
          {
            value: 6,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54583"
          },
          {
            value: 7,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54584"
          },
          {
            value: 8,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54585"
          },
          {
            value: 9,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54586"
          },
          {
            value: 10,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54587"
          },
          {
            value: 11,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54588"
          },
          {
            value: 12,
            label: "\u7CFB\u7EDF\u7BA1\u7406\u54589"
          },
          {
            value: 3,
            label: "\u8BBF\u95EE\u6E38\u5BA2"
          }
        ],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  {
    url: "/api/v1/roles/page",
    method: "get",
    response: () => {
      return {
        code: "00000",
        data: {
          list: [
            {
              id: 2,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
              code: "ADMIN",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 3,
              name: "\u8BBF\u95EE\u6E38\u5BA2",
              code: "GUEST",
              status: 1,
              sort: 3,
              createTime: "2021-05-26 15:49:05",
              updateTime: "2019-05-05 16:00:00"
            },
            {
              id: 4,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54581",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 5,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54582",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 6,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54583",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 7,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54584",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 8,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54585",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 9,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54586",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 10,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54587",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            },
            {
              id: 11,
              name: "\u7CFB\u7EDF\u7BA1\u7406\u54588",
              code: "ADMIN1",
              status: 1,
              sort: 2,
              createTime: "2021-03-25 12:39:54",
              updateTime: null
            }
          ],
          total: 11
        },
        msg: "\u4E00\u5207ok"
      };
    }
  }
];
export {
  role_default as default
};
//# sourceMappingURL=data:application/json;base64,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
