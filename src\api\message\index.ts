import request from "@/utils/request";
import { AxiosPromise } from "axios";

export function getMessages(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/messages",
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增
 *
 * @param data
 */
export function addMessages(data: any) {
  return request({
    url: "/gkadmin/v1/messages",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateMessages(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/messages/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteMessages(ids: string) {
  return request({
    url: "/gkadmin/v1/messages/" + ids,
    method: "delete",
  });
}
