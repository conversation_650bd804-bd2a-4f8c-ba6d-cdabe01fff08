<template>
  <div class="user-log-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn"><i-ep-search /> 搜索</div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column
          label="用户名"
          align="center"
          prop="name"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column
          label="手机号码"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="120"
        >
          <template #default="scope">
            <el-tag :type="scope.row.status == 1 ? 'success' : 'info'">{{
              scope.row.status == 1 ? "启用" : "禁用"
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="注册时间"
          align="center"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="220">
          <!-- fixed="right" -->
          <template #default="scope">
            <div class="option-btn">
              <div class="btn primary-btn" @click="onRowClick(scope.row)">
                详情
              </div>
              <div class="btn info-btn" @click="onRowClick(scope.row)">
                冻结
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="user-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { getUsers, updateUsers } from "@/api/user";
import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "UserLog",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const search = ref("");
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref([
  {
    name: "张三",
    id: 1,
    createTime: "2022-01-01 12:00:00",
    mobile: "12345678901",
    status: 1,
  },
]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "user-form",
  width: 800,
  title: "详情",
});

// 用户表单数据
const formData = reactive<any>({
  status: 1,
});

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
function getData() {}
function onRowClick(row: any) {
  dialog.visible = true;
  console.log(row);
}
function handleQuery() {}

function closeDialog() {
  dialog.visible = false;
}
function handleSubmit() {}
</script>

<style scoped lang="scss">
.user-log-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .left {
      display: flex;
      justify-content: space-between;
      // width: 40%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
    }
  }
  .content {
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
</style>
