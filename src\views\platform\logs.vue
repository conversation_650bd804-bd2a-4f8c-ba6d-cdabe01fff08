<template>
  <div class="logs-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="filter-row">
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="filter-row">
          <el-date-picker
            class="date-picker"
            size="large"
            v-model="queryParams.dateTimeRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            @change="handleQuery"
          />
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.method"
            placeholder="请选择操作类型"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <!-- <div class="right"></div> -->
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="用户" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.op_name || scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="手机号码" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column label="操作类型" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.http_method }}
          </template>
        </el-table-column>
        <el-table-column label="url" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.url }}
          </template>
        </el-table-column>

        <el-table-column label="操作结果" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.op_result || "- -" }}
          </template>
        </el-table-column>
        <el-table-column label="操作内容" align="center" min-width="120">
          <!--      show-overflow-tooltip
          :tooltip-options="{ disabled: true, visible: false }" -->
          <template #default="scope">
            <span class="ellipsis-text">{{ scope.row.body || "- -" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form label-width="6rem" :style="{ 'pointer-events': 'none' }">
          <el-form-item label="用户">
            <el-input v-model="formData.name" clearable size="large" />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="formData.mobile" clearable size="large" />
          </el-form-item>
          <el-form-item label="操作结果">
            <el-input v-model="formData.op_result" clearable size="large" />
          </el-form-item>
          <el-form-item label="操作类型">
            <el-input v-model="formData.http_method" clearable size="large" />
          </el-form-item>
          <el-form-item
            label="操作内容"
            :style="{
              'pointer-events': 'auto',
            }"
          >
            <el-input
              v-model="formData.body"
              clearable
              size="large"
              type="textarea"
              :rows="4"
              resize="none"
            />
          </el-form-item>
          <el-form-item label="ip地址">
            <el-input v-model="formData.ip_address" clearable size="large" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { getSysUsersLogs } from "@/api/system";
import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Logs",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const typeOptions = ref<any>([
  { value: "GET", label: "GET" },
  { value: "PUT", label: "PUT" },
  { value: "POST", label: "POST" },
  { value: "DELETE", label: "DELETE" },
]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  method: "",
  from: "",
  to: "",
  search: "",
  pageNum: 1,
  pageSize: 20,
  dateTimeRange: "",
});

// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref<any>([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "日志详情",
});

// 用户表单数据
const rowId = ref();
const formData = reactive<any>({
  op_name: "",
  mobile: "",
  op_result: "",
  op_type: "",
  content: "",
  ip: "",
  created_at: "",
});

watch(
  () => queryParams.dateTimeRange,
  (newVal) => {
    if (newVal) {
      queryParams.from = parseTime(newVal[0], "{y}{m}{d}000000");
      queryParams.to = parseTime(newVal[1], "{y}{m}{d}235959");
    }
  }
);
onMounted(() => {
  getData();
});
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    from: queryParams.from,
    to: queryParams.to,
    method: queryParams.method || undefined,
  };
  if (!queryParams.dateTimeRange) {
    delete params.from;
    delete params.to;
  }
  getSysUsersLogs(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.sys_user_logs.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function onRowClick(type: any, row: any) {
  if (type == "detail") {
    dialog.visible = true;
    Object.assign(formData, row);
  }
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closeDialog() {
  dialog.visible = false;
  Object.assign(formData, {
    op_name: "",
    mobile: "",
    op_result: "",
    op_type: "",
    content: "",
    ip: "",
    created_at: "",
  });
}
</script>

<style scoped lang="scss">
.logs-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .container-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 60%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
      &:nth-child(2) {
        margin-left: 0;
        width: 15%;
      }
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
  }
  .content {
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
    .ellipsis-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
</style>
