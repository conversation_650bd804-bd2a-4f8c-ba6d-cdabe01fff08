/**
 * Check if an element has a class
 * @param {HTMLElement} ele
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele: HTMLElement, cls: string) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function addClass(ele: HTMLElement, cls: string) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function removeClass(ele: HTMLElement, cls: string) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path: string) {
  const isExternal = /^(https?:|http?:|mailto:|tel:)/.test(path);
  return isExternal;
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time: any, cFormat: any) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = reactive<any>({
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  });
  const time_str = format.replace(
    /{(y|m|d|h|i|s|a)+}/g,
    (result: any, key: any) => {
      let value = formatObj[key];
      // Note: getDay() returns 0 on Sunday
      if (key === "a") {
        return ["日", "一", "二", "三", "四", "五", "六"][value];
      }
      if (result.length > 0 && value < 10) {
        value = "0" + value;
      }
      return value || 0;
    }
  );
  return time_str;
}

//14位纯字符串时间转换成日期
export function formatStringDate(date: any, format?: any) {
  const year = date.substring(0, 4);
  const month = date.substring(4, 6);
  const day = date.substring(6, 8);
  const hour = date.substring(8, 10);
  const minute = date.substring(10, 12);
  const second = date.substring(12, 14);
  if (format === "{y}-{m}-{d} {h}:{i}:{s}") {
    return (
      year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second
    );
  } else {
    return year + "-" + month + "-" + day;
  }
}

export function dateToString(date: any) {
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? "0" + m : m;
  let d = date.getDate();
  d = d < 10 ? "0" + d : d;

  return y + "" + m + "" + d + "";
}

/**
 * 深拷贝
 */
export function deepClone(obj: any) {
  const cloneObj: any = Array.isArray(obj) ? [] : {};
  for (const k in obj) {
    if (obj.hasOwnProperty(k)) {
      // 判定 obj 里是否有 k 这个属性。
      if (typeof obj[k] === "object") {
        // 判定 k 是不是对象（广义）
        cloneObj[k] = deepClone(obj[k]);
      } else {
        cloneObj[k] = obj[k];
      }
    }
  }
  return cloneObj;
}

//数字转中文
export function numberToChinese(num: any) {
  const chineseNum = [
    "一",
    "二",
    "三",
    "四",
    "五",
    "六",
    "七",
    "八",
    "九",
    "十",
    "十一",
    "十二",
    "十三",
    "十四",
    "十五",
  ];
  const numStr = num.toString();
  let chineseStr = "";

  for (let i = 0; i < numStr.length; i++) {
    const index = parseInt(numStr[i]);
    chineseStr += chineseNum[index];
  }

  return chineseStr;
}
//数字转中文--课时
export function numberToChinese2(fa_num: any, ch_num: any) {
  return fa_num + 1 + " - " + (ch_num * 1 + 1);
}

export function secondsToMinutes(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return (
    (minutes ? minutes + " 分钟 " : "") +
    (remainingSeconds ? remainingSeconds + " 秒" : "")
  );
}
export function secondsToHours(seconds) {
  const hours = Math.floor(seconds / 3600);
  if (hours == 0) {
    const mins = Math.floor(seconds / 60);
    return mins + "分钟 ";
  }
  return hours + "小时 ";
}
export function secondsToHoursAndMinutes(seconds) {
  const hours = Math.floor(seconds / 3600);
  const remain_seconds = seconds - hours * 3600;
  const minutes = Math.floor(remain_seconds / 60);
  // console.log(hours, remain_seconds, minutes);
  if (seconds == 0) return "0";
  if (hours == 0 && remain_seconds && remain_seconds < 60) return 1 + "分钟 ";
  if (minutes < 60 && hours == 0) return minutes + "分钟 ";
  return hours + "小时" + minutes + "分钟 ";
}

export function secondsToTime(seconds: any) {
  let h: any = Math.floor((seconds / 60 / 60) % 24);
  let m: any = Math.floor((seconds / 60) % 60);
  let s: any = Math.floor(seconds % 60);
  // 自动补零
  h = h > 10 ? h : "0" + h;
  m = m > 10 ? m : "0" + m;
  s = s > 10 ? s : "0" + s;

  const timestr: any = ` ${h}小时${m}分钟${s}秒`;

  return timestr;
}

export function phoneNumFilter(phone: any) {
  let str: any = "";
  // 1字符串转化成数组
  const phoneArr: any = [...phone];
  // 2.将数组中的4-8位变成*
  phoneArr.map((res, index) => {
    if (index > 2 && index < 8) {
      str += "*";
    } else {
      str += res;
    }
  });
  return str;
}

/**
 * 处理目录resources,将子菜单放进 children 数组中， 同时从 url 中提取出 route TODO:
 * @param resources
 * @returns {*[]|*}
 */
export function resourcesCollapse(resources: any, userRoutes: any) {
  const obj: any = {};
  resources = resources.reduce((prev: any, curr: any) => {
    if (!obj[curr.id]) {
      obj[curr.id] = true;
      prev.push(curr);
    }
    return prev;
  }, []);
  if (Array.isArray(resources)) {
    const base = resources
      .filter((resource) => !resource.pid)
      .sort((a, b) => a.seq - b.seq);
    base.forEach((item) => {
      item.children = resources
        .filter((menu) => menu.pid === item.id)
        .sort((a, b) => a.seq - b.seq);

      // item.children.forEach((menu: any) => {
      //   menu.route = "/" + menu.url.split(".")[0].split("/").slice(2).join("/");
      // });
    });
    return base;
  }
  return [];
}

/**
 *  判断前一个数组是否包含后一个数组的元素
 */
export function containsAllElements(arr1, arr2) {
  return arr2.every((item: any) => arr1.includes(item));
}

/**
 * 防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 等待时间，单位毫秒
 * @returns {(...args: any[]) => void}
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function (this: ThisParameterType<T>, ...args: Parameters<T>): void {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
}
// 防抖示例
// const debouncedFn = debounce((text: string) => {
//   console.log(text);
// }, 1000);

/**
 * 节流函数
 * @param {Function} func - 需要节流的函数
 * @param {number} limit - 时间间隔，单位毫秒
 * @returns {(...args: any[]) => void}
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  let lastRan = 0;

  return function (this: ThisParameterType<T>, ...args: Parameters<T>): void {
    if (!lastRan) {
      func.apply(this, args);
      lastRan = Date.now();
      return;
    }

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    const remaining = limit - (Date.now() - lastRan);

    timeoutId = setTimeout(
      () => {
        if (Date.now() - lastRan >= limit) {
          func.apply(this, args);
          lastRan = Date.now();
        }
      },
      remaining > 0 ? remaining : 0
    );
  };
}
// 节流示例
// const throttledFn = throttle((value: number) => {
//   console.log(value);
// }, 1000);

// 清空响应式对象数据
export function resetReactiveObject(obj: any) {
  Object.keys(obj).forEach((key) => {
    if (Array.isArray(obj[key])) {
      obj[key] = []; // 清空数组
    } else if (typeof obj[key] === "object" && obj[key] !== null) {
      obj[key] = {}; // 清空嵌套对象
    } else {
      obj[key] = ""; // 清空其他类型
    }
  });
}

//过期判断逻辑
export function isExpired(endAt: string | null): boolean {
  if (!endAt) return false; // 如果没有结束时间，直接返回 false
  const expTime = new Date(endAt).getTime();
  const nowTime = Date.now();
  return expTime < nowTime; // 判断是否过期
}
