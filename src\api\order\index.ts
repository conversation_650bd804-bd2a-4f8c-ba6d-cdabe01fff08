import request from "@/utils/request";
import { AxiosPromise } from "axios";

export function getOrdersDetail(ids: string) {
  return request({
    url: "/gkadmin/v1/orders/" + ids,
    method: "get",
  });
}

export function getOrders(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/orders",
    method: "get",
    params: queryParams,
  });
}

//获取赠送订单
export function getGiveOrders(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/bestowal_orders",
    method: "get",
    params: queryParams,
  });
}
//撤销赠送订单
export function cancelGiveOrder(orderNo?: any) {
  return request({
    url: "/gkadmin/v1/bestowal_orders/" + orderNo,
    method: "put",
  });
}
