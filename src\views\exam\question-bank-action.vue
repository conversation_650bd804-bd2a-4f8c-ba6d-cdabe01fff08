<template>
  <div class="question-bank-action-container">
    <div class="container-header">
      <div class="left">
        <div class="btn primary-btn" @click="handleBack">返回</div>
        <span>考试--{{ typeMap[type].label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleSubmit">保存</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar
        style="height: 100%; width: 100%"
        warp-style="overflow-x: hidden;"
        class="exam-scrollbar"
      >
        <div class="base-info">
          <div class="title">基本信息</div>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>关联主题</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="questionBankForm.topic_ids"
                  placeholder="请选择关联主题"
                  filterable
                  clearable
                  multiple
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in topicOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>题库名称</div>
              <div class="input">
                <el-input
                  v-model="questionBankForm.name"
                  placeholder="请输入题库名称"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="paper-info">
          <div class="title">题目</div>
          <div class="q-type-action">
            <div class="left">
              <div class="label">题目类型</div>
              <div
                class="q-type-btn"
                v-for="(item, index) in questionOptions"
                :key="index"
                @click="handleQuestionAdd(item.value)"
              >
                {{ item.label }}
              </div>
              <div class="q-type-tips">点击题型手动添加题目</div>
            </div>
            <div class="right">
              <el-upload
                class="upload-file question-uploader"
                ref="importRef"
                multiple
                :on-exceed="handleExceed"
                :auto-upload="false"
                :before-upload="handleBeforeUpload"
                :on-change="handleFileChange"
                :on-remove="handleRemove"
              >
                <!-- :limit="4" -->
                <template #trigger>
                  <div class="text-btn">
                    <i-ep-circle-plus style="margin-right: 5px" />
                    导入题目
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
          <div
            class="q-list"
            :class="{ dragging: isDragging }"
            v-if="questionBankForm.questions.length > 0"
          >
            <draggable
              v-model="questionBankForm.questions"
              group="questions"
              item-key="id"
              handle=".drag-handle"
              animation="300"
              ghost-class="ghost-item"
              chosen-class="chosen-item"
              drag-class="drag-item"
              @start="onDragStart"
              @end="onDragEnd"
            >
              <template #item="{ element, index }">
                <QuestionItem
                  :key="`question-${index}`"
                  :question-data="element"
                  :question-index="index"
                  :question-type-map="questionMap"
                  @save="handleQuestionSave"
                  @delete="handleQuestionDelete"
                  @cancel="handleQuestionCancel"
                  @update="handleQuestionUpdate"
                />
              </template>
            </draggable>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import type { UploadProps, UploadRawFile } from "element-plus";
import { genFileId } from "element-plus";
import {
  getQuestionBankDetail,
  addQuestionBank,
  updateQuestionBank,
} from "@/api/exam";

import QuestionItem from "./components/QuestionItem.vue";
import draggable from "vuedraggable";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "QuestionBankAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const questionMap = reactive<any>({
  10: {
    label: "单选题",
  },
  20: {
    label: "多选题",
  },
  30: {
    label: "填空题",
  },
  40: { label: "判断题" },
});
const questionOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "填空题", value: 30 },
  { label: "判断题", value: 40 },
]);

const type: any = route.query.type;
const questionBankId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

const topicOptions = ref<any>([]);
const questionBankDetail = reactive<any>({
  name: "储能",
});
const questionBankForm = reactive<any>({
  name: "",
  enabled: "",
  topic_ids: "",
  questions: [
    // {
    //   name: "",
    //   image_urls: "",
    //   remark: "",
    //   q_type: "",
    //   content: "",
    //   answers: "",
    //   seq: "",
    // },
  ],
});
const questionData = reactive<any>({
  name: "", //题目标题
  image_urls: "", //图片的路径列表，如：["/image/platform/xxx.jpg", "/image/platform/xxx.jpg"]
  remark: "", //题目解析
  q_type: "",
  content: "", // 当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
  answers: "", // 答案，有多个时用逗号隔开，如："A, B"
  seq: "",
});

//导入题目
const importRef = ref<any>(null);
const importFiles = ref<any>([]);
const paperData = ref<any>([]);

onBeforeMount(() => {});
onMounted(() => {});

function handleQuestionAdd(type: any, index?: any) {
  const newQuestion: any = {
    id: Date.now() + Math.random(), // 添加唯一ID
    name: "",
    image_urls: "",
    remark: "",
    q_type: type,
    content: "",
    answers: "",
    seq: questionBankForm.questions.length + 1,
    title: "",
    explanation: "",
    score: 5,
    isEditing: true,
  };

  // 根据题型初始化不同的数据结构
  if (type === 10) {
    // 单选题
    newQuestion.options = [
      { text: "" },
      { text: "" },
      { text: "" },
      { text: "" },
    ];
    newQuestion.correctAnswer = "";
  } else if (type === 20) {
    // 多选题
    newQuestion.options = [
      { text: "" },
      { text: "" },
      { text: "" },
      { text: "" },
    ];
    newQuestion.correctAnswers = [];
  } else if (type === 30) {
    // 填空题
    newQuestion.correctAnswer = "";
  } else if (type === 40) {
    // 判断题
    newQuestion.correctAnswer = "";
  }
  questionBankForm.questions.push(newQuestion);
}

// QuestionItem 组件事件处理
function handleQuestionSave(questionData: any) {
  console.log("题目保存:", questionData);
}

function handleQuestionCancel(questionData: any) {
  // 查找题目在数组中的索引
  const index = questionBankForm.questions.findIndex(
    (q: any) => q === questionData
  );
  if (index !== -1) {
    if (!questionData.title) {
      // 如果是新题目且没有标题，则删除
      questionBankForm.questions.splice(index, 1);
    } else {
      questionData.isEditing = false;
    }
  }
}

// 处理题目数据实时更新
function handleQuestionUpdate(updatedData: any) {
  // 查找题目在数组中的索引
  const index = questionBankForm.questions.findIndex(
    (q: any) => q.q_type === updatedData.q_type && q.seq === updatedData.seq
  );
  if (index !== -1) {
    // 实时更新题目数据
    Object.assign(questionBankForm.questions[index], updatedData);
  }
}

// 拖拽相关事件处理
const isDragging = ref(false);

function onDragStart(evt: any) {
  isDragging.value = true;
  console.log("开始拖拽:", evt);
}

function onDragEnd(evt: any) {
  isDragging.value = false;
  console.log("拖拽结束:", evt);
  // 更新题目序号
  questionBankForm.questions.forEach((question: any, index: number) => {
    question.seq = index + 1;
  });
  ElMessage.success("题目顺序已更新");
}

//导入题目
function handleFileChange(file: any, res: any) {
  // if (file.name.includes(".xls ") == -1) {
  //   ElMessage.warning("只能上传XLSX，XLS，XLSM文件");
  //   importRef.value?.clearFiles();
  //   return;
  // }
  const l = importFiles.value.length;
  importFiles.value.push(file);
}
function handleRemove(file: any, fileList: any) {
  importFiles.value = fileList;
  console.log("remove", importFiles.value, file, fileList);
}
const handleExceed: UploadProps["onExceed"] = (files) => {
  importRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  importRef.value!.handleStart(file);
};
function handleBeforeUpload(file: any) {}

function handleQuestionDelete(index: any, item: any) {
  // if (item.id) {
  // }
  questionBankForm.questions.splice(index, 1);
}

function handleBack() {
  router.go(-1);
}

function handleSubmit() {
  const data: any = {};
  ElMessageBox.confirm("是否确认操作?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    if (type === "create") {
      addQuestionBank(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "新增成功!",
          });
          router.go(-1);
        }
      });
    } else if (type === "edit") {
      updateQuestionBank(questionBankId, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "修改成功!",
          });
          router.go(-1);
        }
      });
    }
  });
}
</script>

<style scoped lang="scss">
.question-bank-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }
    }
    .left {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
        margin-right: 10px;
      }
    }
    .right {
      .btn {
        width: 96px;
        height: 42px;
        border-radius: 10px;
        font-weight: 400;
        font-size: 18px;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .exam-scrollbar,
    .el-scrollbar__wrap {
      width: 100%;
    }
    :deep(.el-scrollbar__view) {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .title {
      font-weight: 500;
      font-size: 18px;
      color: #3b4664;
      margin-left: 15px;
    }
    .el-row {
      margin: 20px 0;
    }
    .el-col {
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      font-size: 15px;
      font-weight: 400;
      color: #3b4664;

      span {
        color: red;
      }
    }

    .input {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;

      img {
        width: 50%;
        height: 160px;
      }

      span {
        margin-top: 10px;
        font-size: 13px;
        font-weight: 400;
        color: #8d9295;
        text-align: left;
      }
    }
  }
  .base-info {
    width: 100%;
    padding: 0px 20px 10px 20px;
    border-bottom: 6px solid #edeeee;
  }
  .paper-info {
    width: 100%;
    padding: 20px 20px 10px 20px;
    .q-type-action {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .q-type-tips {
        font-weight: 400;
        font-size: 14px;
        color: #52b0a9;
        margin-left: 20px;
      }
      .q-type-btn {
        width: 90px;
        height: 38px;
        background: #ffffff;
        box-shadow: 0px 3px 6px 1px rgba(107, 174, 175, 0.25);
        border-radius: 19px 19px 19px 19px;
        border: 1px solid #6aaeae;
        cursor: pointer;
        color: #6aaeae;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 15px;
        margin-left: 10px;
      }
      .left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .text-btn {
          font-size: 16px;
          font-weight: 400;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          svg {
            font-size: 28px;
          }
        }
      }
    }
  }
  .q-list {
    width: 100%;
    padding: 0 20px;

    // 拖拽相关样式
    .ghost-item {
      opacity: 0.5;
      background: #f5f7fa;
      border: 2px dashed #409eff;
      transform: rotate(2deg);
    }

    .chosen-item {
      opacity: 0.8;
      transform: scale(1.02);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
      border: 2px solid #409eff;
    }

    .drag-item {
      opacity: 0.9;
      transform: rotate(1deg);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    // 拖拽过程中的动画效果
    :deep(.question-item) {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }
    }

    // 拖拽时禁用指针事件
    // &.dragging {
    //   :deep(.question-item) {
    //     pointer-events: none;
    //   }
    // }
  }
}
</style>
