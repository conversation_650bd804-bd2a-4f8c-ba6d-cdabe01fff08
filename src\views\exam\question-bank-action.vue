<template>
  <div class="question-bank-action-container">
    <div class="container-header">
      <div class="left">
        <div class="btn primary-btn" @click="handleBack">返回</div>
        <span>考试--{{ typeMap[type].label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleSubmit">保存</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar
        style="height: 100%; width: 100%"
        warp-style="overflow-x: hidden;"
        class="exam-scrollbar"
      >
        <div class="base-info">
          <div class="title">基本信息</div>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>关联主题</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="questionBankForm.topic_ids"
                  placeholder="请选择关联主题"
                  filterable
                  clearable
                  multiple
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in topicOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>题库名称</div>
              <div class="input">
                <el-input
                  v-model="questionBankForm.name"
                  placeholder="请输入题库名称"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="paper-info">
          <div class="title">题目</div>
          <div class="q-type-action">
            <div class="left">
              <div class="label">题目类型</div>
              <div
                class="q-type-btn"
                v-for="(item, index) in questionOptions"
                :key="index"
                @click="handleQuestionAdd(item.value)"
              >
                {{ item.label }}
              </div>
              <div class="q-type-tips">点击题型手动添加题目</div>
            </div>
            <div class="right">
              <el-upload
                class="upload-file question-uploader"
                ref="importRef"
                multiple
                :on-exceed="handleExceed"
                :auto-upload="false"
                :before-upload="handleBeforeUpload"
                :on-change="handleFileChange"
                :on-remove="handleRemove"
              >
                <!-- :limit="4" -->
                <template #trigger>
                  <div class="text-btn">
                    <i-ep-circle-plus style="margin-right: 5px" />
                    导入题目
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
          <div class="q-list" v-if="questionBankForm.questions.length > 0">
            <div
              class="q-item"
              v-for="(item, index) in questionBankForm.questions"
              :key="index"
            >
              <div class="question-header">
                <div class="question-type-label">
                  {{ questionMap[item.q_type]?.label || "未知题型" }}
                </div>
                <div class="question-actions">
                  <el-button
                    type="text"
                    size="small"
                    @click="handleQuestionEdit(index, item)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    style="color: #f56c6c"
                    @click="handleQuestionDelete(index, item)"
                  >
                    删除
                  </el-button>
                </div>
              </div>

              <!-- 题目编辑表单 -->
              <div class="question-form" v-if="item.isEditing">
                <el-form :model="item" label-width="80px" size="default">
                  <el-form-item label="题目标题" required>
                    <el-input
                      v-model="item.title"
                      placeholder="请输入题目标题"
                      type="textarea"
                      :rows="2"
                    />
                  </el-form-item>

                  <!-- 单选题选项 -->
                  <template v-if="item.q_type === 10">
                    <el-form-item label="选项设置">
                      <div class="options-container">
                        <div
                          class="option-item"
                          v-for="(option, optIndex) in item.options"
                          :key="optIndex"
                        >
                          <span class="option-label"
                            >{{ String.fromCharCode(65 + optIndex) }}.</span
                          >
                          <el-input
                            v-model="option.text"
                            placeholder="请输入选项内容"
                            style="flex: 1; margin: 0 10px"
                          />
                          <el-button
                            type="text"
                            size="small"
                            @click="removeOption(item, optIndex)"
                            v-if="item.options.length > 2"
                          >
                            删除
                          </el-button>
                        </div>
                        <el-button
                          type="text"
                          size="small"
                          @click="addOption(item)"
                          v-if="item.options.length < 6"
                        >
                          + 添加选项
                        </el-button>
                      </div>
                    </el-form-item>
                    <el-form-item label="正确答案">
                      <el-radio-group v-model="item.correctAnswer">
                        <el-radio
                          v-for="(option, optIndex) in item.options"
                          :key="optIndex"
                          :label="String.fromCharCode(65 + optIndex)"
                        >
                          {{ String.fromCharCode(65 + optIndex) }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </template>

                  <!-- 多选题选项 -->
                  <template v-if="item.q_type === 20">
                    <el-form-item label="选项设置">
                      <div class="options-container">
                        <div
                          class="option-item"
                          v-for="(option, optIndex) in item.options"
                          :key="optIndex"
                        >
                          <span class="option-label"
                            >{{ String.fromCharCode(65 + optIndex) }}.</span
                          >
                          <el-input
                            v-model="option.text"
                            placeholder="请输入选项内容"
                            style="flex: 1; margin: 0 10px"
                          />
                          <el-button
                            type="text"
                            size="small"
                            @click="removeOption(item, optIndex)"
                            v-if="item.options.length > 2"
                          >
                            删除
                          </el-button>
                        </div>
                        <el-button
                          type="text"
                          size="small"
                          @click="addOption(item)"
                          v-if="item.options.length < 6"
                        >
                          + 添加选项
                        </el-button>
                      </div>
                    </el-form-item>
                    <el-form-item label="正确答案">
                      <el-checkbox-group v-model="item.correctAnswers">
                        <el-checkbox
                          v-for="(option, optIndex) in item.options"
                          :key="optIndex"
                          :label="String.fromCharCode(65 + optIndex)"
                        >
                          {{ String.fromCharCode(65 + optIndex) }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </template>

                  <!-- 填空题 -->
                  <template v-if="item.q_type === 30">
                    <el-form-item label="正确答案">
                      <el-input
                        v-model="item.correctAnswer"
                        placeholder="请输入正确答案"
                      />
                    </el-form-item>
                  </template>

                  <!-- 判断题 -->
                  <template v-if="item.q_type === 40">
                    <el-form-item label="正确答案">
                      <el-radio-group v-model="item.correctAnswer">
                        <el-radio label="A">A. 正确</el-radio>
                        <el-radio label="B">B. 错误</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </template>

                  <el-form-item label="解析">
                    <el-input
                      v-model="item.explanation"
                      placeholder="请输入题目解析（可选）"
                      type="textarea"
                      :rows="2"
                    />
                  </el-form-item>

                  <el-form-item label="分值">
                    <el-input-number
                      v-model="item.score"
                      :min="1"
                      :max="100"
                      placeholder="分值"
                    />
                  </el-form-item>

                  <div class="form-actions">
                    <el-button
                      type="primary"
                      @click="saveQuestion(index, item)"
                    >
                      保存
                    </el-button>
                    <el-button @click="cancelEdit(index, item)">
                      取消
                    </el-button>
                  </div>
                </el-form>
              </div>

              <!-- 题目预览 -->
              <div class="question-preview" v-else>
                <div class="preview-content">
                  <div class="question-title">
                    {{ index + 1 }}. {{ item.title || "请编辑题目内容" }}
                  </div>

                  <!-- 单选题预览 -->
                  <template v-if="item.q_type === 10 && item.options">
                    <div class="options-preview">
                      <div
                        class="option-preview"
                        v-for="(option, optIndex) in item.options"
                        :key="optIndex"
                      >
                        <span class="option-label"
                          >{{ String.fromCharCode(65 + optIndex) }}.</span
                        >
                        <span>{{ option.text }}</span>
                      </div>
                    </div>
                    <div class="answer-preview" v-if="item.correctAnswer">
                      <span class="answer-label">答案：</span>
                      <span class="answer-value">{{ item.correctAnswer }}</span>
                    </div>
                  </template>

                  <!-- 多选题预览 -->
                  <template v-if="item.q_type === 20 && item.options">
                    <div class="options-preview">
                      <div
                        class="option-preview"
                        v-for="(option, optIndex) in item.options"
                        :key="optIndex"
                      >
                        <span class="option-label"
                          >{{ String.fromCharCode(65 + optIndex) }}.</span
                        >
                        <span>{{ option.text }}</span>
                      </div>
                    </div>
                    <div
                      class="answer-preview"
                      v-if="item.correctAnswers && item.correctAnswers.length"
                    >
                      <span class="answer-label">答案：</span>
                      <span class="answer-value">{{
                        item.correctAnswers.join(", ")
                      }}</span>
                    </div>
                  </template>

                  <!-- 填空题预览 -->
                  <template v-if="item.q_type === 30">
                    <div class="fill-blank-preview">
                      <div class="blank-input">_____________</div>
                    </div>
                    <div class="answer-preview" v-if="item.correctAnswer">
                      <span class="answer-label">答案：</span>
                      <span class="answer-value">{{ item.correctAnswer }}</span>
                    </div>
                  </template>

                  <!-- 判断题预览 -->
                  <template v-if="item.q_type === 40">
                    <div class="judgment-preview">
                      <div class="judgment-options">
                        <span class="judgment-option">A. 正确</span>
                        <span class="judgment-option">B. 错误</span>
                      </div>
                    </div>
                    <div class="answer-preview" v-if="item.correctAnswer">
                      <span class="answer-label">答案：</span>
                      <span class="answer-value">{{
                        item.correctAnswer === "A" ? "A. 正确" : "B. 错误"
                      }}</span>
                    </div>
                  </template>

                  <div class="explanation-preview" v-if="item.explanation">
                    <span class="explanation-label">解析：</span>
                    <span class="explanation-value">{{
                      item.explanation
                    }}</span>
                  </div>

                  <div class="score-preview" v-if="item.score">
                    <span class="score-label">分值：</span>
                    <span class="score-value">{{ item.score }}分</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import type { UploadProps, UploadRawFile } from "element-plus";
import { genFileId } from "element-plus";
import {
  getQuestionBankDetail,
  addQuestionBank,
  updateQuestionBank,
} from "@/api/exam";

import FillBlank from "./components/FillBlank.vue";
import Judgement from "./components/Judgement.vue";
import MultipleChoice from "./components/MultipleChoice.vue";
import SingleChoice from "./components/SingleChoice.vue";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "QuestionBankAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const questionMap = reactive<any>({
  10: { component: SingleChoice, label: "单选题" },
  20: { component: MultipleChoice, label: "多选题" },
  30: { component: FillBlank, label: "填空题" },
  40: { component: Judgement, label: "判断题" },
});
const questionOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "填空题", value: 30 },
  { label: "判断题", value: 40 },
]);

const type: any = route.query.type;
const questionBankId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

const topicOptions = ref<any>([]);
const questionBankDetail = reactive<any>({
  name: "储能",
});
const questionBankForm = reactive<any>({
  name: "",
  enabled: "",
  topic_ids: "",
  questions: [
    // {
    //   name: "",
    //   image_urls: "",
    //   remark: "",
    //   q_type: "",
    //   content: "",
    //   answers: "",
    //   seq: "",
    // },
  ],
});
const questionData = reactive<any>({
  name: "",
  image_urls: "",
  remark: "",
  q_type: "",
  content: "",
  answers: "",
  seq: "",
});
//导入题目
const importRef = ref<any>(null);
const importFiles = ref<any>([]);
const paperData = ref<any>([]);

onBeforeMount(() => {});
onMounted(() => {});

function handleQuestionAdd(type: any, index?: any) {
  const newQuestion: any = {
    name: "",
    image_urls: "",
    remark: "",
    q_type: type,
    content: "",
    answers: "",
    seq: index || "",
    title: "",
    explanation: "",
    score: 5,
    isEditing: true,
  };

  // 根据题型初始化不同的数据结构
  if (type === 10) {
    // 单选题
    newQuestion.options = [
      { text: "" },
      { text: "" },
      { text: "" },
      { text: "" },
    ];
    newQuestion.correctAnswer = "";
  } else if (type === 20) {
    // 多选题
    newQuestion.options = [
      { text: "" },
      { text: "" },
      { text: "" },
      { text: "" },
    ];
    newQuestion.correctAnswers = [];
  } else if (type === 30) {
    // 填空题
    newQuestion.correctAnswer = "";
  } else if (type === 40) {
    // 判断题
    newQuestion.correctAnswer = "";
  }

  questionBankForm.questions.push(newQuestion);
}

// 编辑题目
function handleQuestionEdit(index: number, item: any) {
  item.isEditing = true;
}

// 保存题目
function saveQuestion(index: number, item: any) {
  // 验证必填字段
  if (!item.title.trim()) {
    ElMessage.warning("请输入题目标题");
    return;
  }

  if (item.q_type === 10 || item.q_type === 20) {
    // 验证选项
    const hasEmptyOption = item.options.some((opt: any) => !opt.text.trim());
    if (hasEmptyOption) {
      ElMessage.warning("请填写所有选项内容");
      return;
    }
  }

  if (item.q_type === 10 && !item.correctAnswer) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  if (
    item.q_type === 20 &&
    (!item.correctAnswers || item.correctAnswers.length === 0)
  ) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  if (item.q_type === 30 && !item.correctAnswer.trim()) {
    ElMessage.warning("请输入正确答案");
    return;
  }

  if (item.q_type === 40 && !item.correctAnswer) {
    ElMessage.warning("请选择正确答案");
    return;
  }

  item.isEditing = false;
  ElMessage.success("题目保存成功");
}

// 取消编辑
function cancelEdit(index: number, item: any) {
  if (!item.title) {
    // 如果是新题目且没有标题，则删除
    questionBankForm.questions.splice(index, 1);
  } else {
    item.isEditing = false;
  }
}

// 添加选项
function addOption(item: any) {
  if (item.options.length < 6) {
    item.options.push({ text: "" });
  }
}

// 删除选项
function removeOption(item: any, optIndex: number) {
  if (item.options.length > 2) {
    item.options.splice(optIndex, 1);

    // 如果删除的选项是正确答案，需要清空正确答案
    const deletedOptionKey = String.fromCharCode(65 + optIndex);
    if (item.q_type === 10 && item.correctAnswer === deletedOptionKey) {
      item.correctAnswer = "";
    } else if (
      item.q_type === 20 &&
      item.correctAnswers.includes(deletedOptionKey)
    ) {
      const answerIndex = item.correctAnswers.indexOf(deletedOptionKey);
      item.correctAnswers.splice(answerIndex, 1);
    }
  }
}

//导入题目
function handleFileChange(file: any, res: any) {
  // if (file.name.includes(".xls ") == -1) {
  //   ElMessage.warning("只能上传XLSX，XLS，XLSM文件");
  //   importRef.value?.clearFiles();
  //   return;
  // }
  const l = importFiles.value.length;
  importFiles.value.push(file);
}
function handleRemove(file: any, fileList: any) {
  importFiles.value = fileList;
  console.log("remove", importFiles.value, file, fileList);
}
const handleExceed: UploadProps["onExceed"] = (files) => {
  importRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  importRef.value!.handleStart(file);
};
function handleBeforeUpload(file: any) {}

function handleQuestionDelete(index: any, item: any) {
  // if (item.id) {
  // }
  questionBankForm.questions.splice(index, 1);
}

function handleBack() {
  router.go(-1);
}

function handleSubmit() {
  const data: any = {};
  ElMessageBox.confirm("是否确认操作?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    if (type === "create") {
      addQuestionBank(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "新增成功!",
          });
          router.go(-1);
        }
      });
    } else if (type === "edit") {
      updateQuestionBank(questionBankId, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "修改成功!",
          });
          router.go(-1);
        }
      });
    }
  });
}
</script>

<style scoped lang="scss">
.question-bank-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }
    }
    .left {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
        margin-right: 10px;
      }
    }
    .right {
      .btn {
        width: 96px;
        height: 42px;
        border-radius: 10px;
        font-weight: 400;
        font-size: 18px;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .exam-scrollbar,
    .el-scrollbar__wrap {
      width: 100%;
    }
    :deep(.el-scrollbar__view) {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    .title {
      font-weight: 500;
      font-size: 18px;
      color: #3b4664;
      margin-left: 15px;
    }
    .el-row {
      margin: 20px 0;
    }
    .el-col {
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    .label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      font-size: 15px;
      font-weight: 400;
      color: #3b4664;

      span {
        color: red;
      }
    }

    .input {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;

      img {
        width: 50%;
        height: 160px;
      }

      span {
        margin-top: 10px;
        font-size: 13px;
        font-weight: 400;
        color: #8d9295;
        text-align: left;
      }
    }
  }
  .base-info {
    width: 100%;
    padding: 0px 20px 10px 20px;
    border-bottom: 6px solid #edeeee;
  }
  .paper-info {
    width: 100%;
    padding: 20px 20px 10px 20px;
    .q-type-action {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .q-type-tips {
        font-weight: 400;
        font-size: 14px;
        color: #52b0a9;
        margin-left: 20px;
      }
      .q-type-btn {
        width: 90px;
        height: 38px;
        background: #ffffff;
        box-shadow: 0px 3px 6px 1px rgba(107, 174, 175, 0.25);
        border-radius: 19px 19px 19px 19px;
        border: 1px solid #6aaeae;
        cursor: pointer;
        color: #6aaeae;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 15px;
        margin-left: 10px;
      }
      .left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .text-btn {
          font-size: 16px;
          font-weight: 400;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          svg {
            font-size: 28px;
          }
        }
      }
    }
  }
  .q-list {
    width: 100%;
    padding: 0 20px;

    .q-item {
      width: 100%;
      padding: 20px;
      box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.25);
      border-radius: 8px;
      border: 1px solid #edeff4;
      margin-bottom: 20px;
      background: #fff;

      &:last-child {
        margin-bottom: 0;
      }

      .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;

        .question-type-label {
          font-size: 14px;
          font-weight: 500;
          color: #409eff;
          background: #ecf5ff;
          padding: 4px 12px;
          border-radius: 12px;
        }

        .question-actions {
          display: flex;
          gap: 8px;
        }
      }

      .question-form {
        .options-container {
          .option-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            .option-label {
              font-weight: 500;
              color: #333;
              min-width: 30px;
            }
          }
        }

        .form-actions {
          display: flex;
          gap: 12px;
          margin-top: 20px;
          padding-top: 15px;
          border-top: 1px solid #ebeef5;
        }
      }

      .question-preview {
        .preview-content {
          .question-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.5;
          }

          .options-preview {
            margin-bottom: 12px;

            .option-preview {
              display: flex;
              align-items: flex-start;
              margin-bottom: 8px;
              font-size: 14px;
              color: #666;

              .option-label {
                font-weight: 500;
                color: #333;
                min-width: 30px;
              }
            }
          }

          .fill-blank-preview {
            margin-bottom: 12px;

            .blank-input {
              display: inline-block;
              border-bottom: 2px solid #409eff;
              min-width: 120px;
              height: 24px;
              text-align: center;
              font-size: 14px;
            }
          }

          .judgment-preview {
            margin-bottom: 12px;

            .judgment-options {
              display: flex;
              gap: 20px;

              .judgment-option {
                font-size: 14px;
                color: #666;
              }
            }
          }

          .answer-preview {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;

            .answer-label {
              font-weight: 500;
              color: #409eff;
              margin-right: 8px;
            }

            .answer-value {
              color: #333;
              font-weight: 500;
            }
          }

          .explanation-preview {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 14px;

            .explanation-label {
              font-weight: 500;
              color: #909399;
              margin-right: 8px;
              min-width: 40px;
            }

            .explanation-value {
              color: #666;
              line-height: 1.5;
            }
          }

          .score-preview {
            display: flex;
            align-items: center;
            font-size: 14px;

            .score-label {
              font-weight: 500;
              color: #909399;
              margin-right: 8px;
            }

            .score-value {
              color: #f56c6c;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
</style>
