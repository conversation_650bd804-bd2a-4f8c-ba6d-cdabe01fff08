<template>
  <div class="banner-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.status"
            placeholder="请选择状态"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增banner
        </div>
      </div>
    </div>
    <!-- <div class="after-header">
      <el-radio-group
        v-model="activeUserType"
        class="header-radio-group"
        @change="handleQuery"
      >
        <el-radio-button
          size="large"
          v-for="(item, index) in userTypeOptions"
          :key="index"
          :value="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>
    </div> -->
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <!-- <el-table-column
          label="单节位置展示"
          align="center"
          prop="mobile"
          min-width="70"
        >
          <template #default="scope">
            {{ scope.row.}}
          </template>
        </el-table-column> -->
        <el-table-column
          label="banner名称"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column
          label="图片"
          align="center"
          prop="name"
          min-width="150"
        >
          <template #default="scope">
            <div class="cover-name">
              <img :src="scope.row.url" alt="" class="cover-img" />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="链接地址"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.link_url }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="120"
        >
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn"
                :class="[
                  scope.row.status == 10 || scope.row.status == 30
                    ? 'light-green-btn'
                    : 'info-btn',
                ]"
                @click="onRowClick(' shelf', scope.row)"
                v-show="enableList"
              >
                {{
                  scope.row.status == 10 || scope.row.status == 30
                    ? "上架"
                    : scope.row.status == 20
                    ? "下架"
                    : "--"
                }}
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
                v-show="enableDelete"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form label-width="6rem">
          <el-form-item label="banner名称">
            <el-input
              v-model="formData.name"
              placeholder="请输入banner名称"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="链接地址">
            <el-input
              v-model="formData.link_url"
              placeholder="请输入链接地址，为空时不跳转（选填）"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="上架时间">
            <el-date-picker
              class="date-picker"
              size="large"
              v-model="formData.dateTimeRange"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="展示图片">
            <UploadImg
              v-model:imgUrl="formData.url"
              v-model:gkResource="formData.gk_resource"
            />
            <span class="tips"> 注：上传banner时--建议尺寸为1920 x 380px </span>
          </el-form-item>
          <el-form-item label="排序">
            <el-input
              v-model="formData.seq"
              placeholder="请输入排序"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              placeholder="请输入备注（选填）"
              clearable
              size="large"
            />
          </el-form-item>

          <el-form-item label="用户类型">
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="formData.user_types"
              placeholder="请选择用户类型"
              filterable
              clearable
              multiple
            >
              <el-option
                v-for="item in userTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="formData.status"
              placeholder="请选择状态"
              filterable
              clearable
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import UploadImg from "@/components/Upload/UploadImg.vue";
import {
  getBanners,
  getBannersDetail,
  deleteBanners,
  addBanners,
  updateBanners,
} from "@/api/web-page";
import { parseTime } from "@/utils";
import { checkUserPermission } from "@/utils/auth";

const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Banner",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const statusOptions = ref<any[]>([
  {
    value: 10,
    label: "待上架",
  },
  {
    value: 20,
    label: "上架",
  },
  {
    value: 30,
    label: "下架",
  },
]);
const activeUserType = ref<any>("10");
const userTypeOptions = ref<any>([
  {
    value: "10",
    label: "游客",
  },
  {
    value: "20",
    label: "学员",
  },
  {
    value: "30",
    label: "企业员工",
  },
  {
    value: "40",
    label: "企业管理员",
  },
]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  status: "",
  pageNum: 1,
  pageSize: 20,
});
const statusMap = reactive<any>({
  10: { type: "primary", label: "待上架" },
  20: { type: "success", label: "上架中" },
  30: { type: "info", label: "下架中" },
  40: { type: "danger", label: "删除" },
  // add more status mappings as needed
});
// const dateTimeRange = ref("");
const total = ref(25); // 数据总数
const tableData = ref([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "35%",
  title: "banner",
});

const imgUrl = ref(new URL("@/assets/images/cover.jpg", import.meta.url).href);
// 用户表单数据
const rowId = ref();
const formData = reactive<any>({
  name: "",
  resource_id: "",
  seq: "",
  link_url: "",
  begin_at: "",
  end_at: "",
  remark: "",
  url: "",
  gk_resource: "",
  status: "",
  user_types: "",
});
const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 9);
});

const enableList = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 10);
});

watch(
  () => formData.dateTimeRange,
  (newVal) => {
    if (newVal && dialog.visible) {
      formData.begin_at = parseTime(newVal[0], "{y}{m}{d}00000");
      formData.end_at = parseTime(newVal[1], "{y}{m}{d}000000");
    }
  }
);
onMounted(() => {
  getData();
});
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    status: queryParams.status || undefined,
  };
  getBanners(params).then((res: any) => {
    if (res.status == 200) {
      tableData.value = res.data.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增banner";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  // router.push({ path: "banner-action", query: { type: type, id: row.id } });
  if (type == "edit") {
    dialog.visible = true;
    dialog.title = "修改banner";
    dialog.type = "edit";
    Object.assign(formData, row);
    rowId.value = row.id;
    formData.dateTimeRange = [
      parseTime(row.begin_at, "{y}-{m}-{d} {h}:{i}:{s}"),
      parseTime(row.end_at, "{y}-{m}-{d} {h}:{i}:{s}"),
    ];
    formData.user_types = row.user_types.split(",");
  }
  if (type == "detail") {
    dialog.visible = true;
    dialog.title = "banner详情";
    dialog.type = "detail";
  }
  if (type == "delete") {
    handelDelete(row);
  }
  if (type == " shelf") {
    const status =
      row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
    const message =
      row.status == 10 || row.status == 30
        ? "上架成功"
        : row.status == 20
        ? "下架成功"
        : "";

    const text = row.status == 20 ? "下架" : "上架";

    ElMessageBox.confirm("此操作将" + text + "该免费课程，是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      updateBanners(row.id, { status }).then((res: any) => {
        ElMessage.success({
          message: "操作成功!",
        });
        getData();
      });
    });
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除该banner，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteBanners(row.id).then(() => {
      getData();
      ElMessage.success({
        message: "删除成功!",
      });
    });
  });
}

function closeDialog() {
  dialog.visible = false;
  Object.keys(formData).forEach((key: any) => {
    formData[key] = "";
  });
  // Object.assign(formData, {
  //   name: "",
  //   resource_id: "",
  //   seq: "",
  //   link_url: "",
  //   begin_at: "",
  //   end_at: "",
  //   remark: "",
  //   status: "",
  //   gk_resource: "",
  // });
}
function handleSubmit() {
  const data: any = {
    name: formData.name,
    seq: formData.seq,
    link_url: formData.link_url,
    // url: formData.url,
    gk_resource: formData.gk_resource || undefined,
    begin_at: formData.begin_at,
    end_at: formData.end_at,
    remark: formData.remark || undefined,
    status: formData.status,
    user_types: formData.user_types.toString(),
  };

  if (dialog.type === "create") {
    addBanners(data).then((res: any) => {
      ElMessage.success({
        message: "新增成功!",
      });
      closeDialog();
      getData();
    });
  }
  if (dialog.type === "edit") {
    updateBanners(rowId.value, data).then((res: any) => {
      ElMessage.success({
        message: "修改成功!",
      });
      closeDialog();
      getData();
    });
  }
}

// 图片上传相关
function handlePreview() {
  dialog.visible = true;
}
function handleDelete() {
  imgUrl.value = "";
}
function handleBeforeUpload(file: any) {}
function uploadFile() {}
</script>

<style scoped lang="scss">
.banner-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  // .after-header {
  //   width: 100%;
  //   padding: 20px 20px 20px 20px;
  // }
  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px 20px 0;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 240px);
    height: calc(100% - 170px);
    // padding: 10px 20px 10px 20px;
    padding: 10px 20px;

    .cover-name {
      .cover-img {
        // width: 150px !important;
        // height: 100px !important;
        width: 160px !important;
        height: 110px !important;
        object-fit: cover;
        border-radius: 8px;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.dialog {
  img {
    width: 100%;
    height: 160px;
    object-fit: cover;
  }

  .single-uploader {
    position: relative;
    display: flex;
    justify-content: flex-start;

    .single-uploader__image {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .img-upload__overlay {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 30%;
    }
  }
}
</style>
