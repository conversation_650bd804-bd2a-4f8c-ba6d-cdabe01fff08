<template>
  <div class="course-sort-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增分类
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
        row-key="id"
        default-expand-all
      >
        <el-table-column label="ID" align="center" prop="id" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="分类名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.seq }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="展示方式" align="center" min-width="40">
          <template #default="scope">
            {{ displayMap[scope.row.display] }}
          </template>
        </el-table-column> -->

        <!-- <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <!--<div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div> -->
              <div
                class="btn delete-btn"
                @click="handelDelete(scope.row)"
                v-show="enableDelete"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form
          label-width="6rem"
          :model="formData"
          :rules="formDataRules"
          ref="formRef"
        >
          <el-form-item label="分类名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入分类名称"
              clearable
              size="large"
            />
          </el-form-item>
          <!-- <el-form-item label="上级分类">
            <el-select
              size="large"
              v-model="formData.father"
              placeholder="请选择上级分类"
              filterable
              clearable
              :suffix-icon="`CaretBottom`"
            >
              <el-option
                v-for="item in sortOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span class="tips"
              >*选择分类后，新增分类添加在上级分类的下一级，上级分类为无时，新增分类为一级分类</span
            >
          </el-form-item> -->
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";
import {
  getCategories,
  getCategoriesDetail,
  addCategories,
  deleteCategories,
  updateCategories,
} from "@/api/course";
import { parseTime } from "@/utils";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "CourseSort",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const sortOptions = ref<any>([]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const displayArr = ref<any>([
  { type: "list", label: "列表" },
  { type: "grid", label: "九宫格" },
]);
const displayMap = reactive<any>({
  list: { type: "list", label: "列表" },
  grid: { type: "grid", label: "九宫格" },
  // add more status mappings as needed
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "新增课程分类",
});

// 用户表单数据
const rowId = ref();
const formData = reactive<any>({
  name: "",
  parent: "",
  seq: "",
  display: "",
});

const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  name: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
});
const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 8);
});

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getCategories(params)
    .then((res: any) => {
      tableData.value = res.data.categories.map((item: any) => {
        // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      sortOptions.value = res.data.categories.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      total.value = res.total;
      loading.value = false;
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleCreate() {
  formData.parent = "";
  dialog.visible = true;
  dialog.title = "新增课程分类";
  dialog.type = "add";
}
function onRowClick(type: any, row: any) {
  if (type === "edit") {
    dialog.visible = true;
    dialog.title = "修改课程分类";
    dialog.type = "edit";
    rowId.value = row.id;
    formData.name = row.name;
    formData.seq = row.seq;
    formData.parent = row.parent;
  }
  if (type === "detail") {
    dialog.visible = true;
    dialog.title = "课程分类详情";
    dialog.type = "detail";
    formData.name = row.name;
    formData.seq = row.seq;
    formData.parent = row.parent;
  }
  if (type === "delete") {
    handelDelete(row);
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除该课程分类，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteCategories(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closeDialog() {
  dialog.visible = false;
  formRef.value.resetFields();
  formRef.value.clearValidate();
  Object.assign(formData, {
    name: "",
    parent: "",
    seq: "",
    display: "",
  });
}
function handleSubmit() {
  const data: any = {
    name: formData.name,
    seq: formData.seq || undefined,
    parent: formData.parent || undefined,
  };
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      if (dialog.type === "add") {
        addCategories(data).then((res: any) => {
          if (res.status === 200) {
            ElMessage.success({
              message: "新增成功!",
            });
            closeDialog();
            getData();
          }
        });
      }
      if (dialog.type === "edit") {
        updateCategories(rowId.value, data).then((res: any) => {
          if (res.status === 200) {
            ElMessage.success({
              message: "修改成功!",
            });
            closeDialog();
            getData();
          }
        });
      }
    }
  });
}
</script>

<style scoped lang="scss">
.course-sort-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
