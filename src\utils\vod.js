import { useStorage } from "@vueuse/core";
import { useUserStore } from "@/store/modules/user";
import { postVodAsset, getVodAsset } from "@/api/file";

const userStore = useUserStore();
const token = userStore.vodKey.token;

const projectId = "2cad9ad8221c41f89df572fd8fa09ecf";
const region = "cn-north-4";

let urlList;

let uploadProgress = 0;
//进度监听TODO：
export function uploadProgressCallBack(event) {
  return uploadProgress;
}
function initUrl(projectId) {
  const url = {
    // 创建媒资接口
    createAssetUrl:
      "https://vod." +
      region +
      ".myhuaweicloud.com/v1.0/" +
      projectId +
      "/asset",
    // 获取授权接口
    initAuthUrl:
      "https://vod." +
      region +
      ".myhuaweicloud.com/v1.0/" +
      projectId +
      "/asset/authority",
    // 确认媒资上传接口
    confirmUploadedUrl:
      "https://vod." +
      region +
      ".myhuaweicloud.com/v1.0/" +
      projectId +
      "/asset/status/uploaded",
  };
  return url;
}

// 文件分段大小1Mb，根据用户实际情况自行调整
const bufferSize = 1 * 1024 * 1024;

// 开始上传
export async function vodUpload(fileRes, uploadName = null) {
  const asyncOperation = () =>
    new Promise((resolve) => {
      urlList = initUrl(projectId);

      // const files = fileRes;
      const file = fileRes;

      // 此处仅以MP4文件示例，其他格式可参考官网说明
      const fileType = "MP4";
      const fileContentType = "video/mp4";

      // 1. 以token鉴权为例，设置请求头
      const headers = {
        "X-Auth-Token": token,
        "Content-Type": "application/json",
      };
      //文件名称
      const fileName = uploadName ? uploadName : file.name;
      // 2.创建点播媒资
      const assetRsp = createAsset(fileName, fileName, fileType, headers);

      // 3.获取初始化上传任务授权
      const initAuthResponse = getInitAuth(assetRsp, fileContentType, headers);

      // 4.初始化分段上传任务
      const uploadId = initPartUpload(
        initAuthResponse,
        assetRsp,
        fileContentType
      );

      // 文件分段计数
      const partNumber = 1;
      // 文件读取位置
      const position = 0;

      const blobSlice =
        File.prototype.mozSlice ||
        File.prototype.webkitSlice ||
        File.prototype.slice;
      let res_info = {};
      // 7.循环5-6上传分段
      readAndUpload(
        file,
        blobSlice,
        position,
        partNumber,
        assetRsp,
        fileContentType,
        uploadId,
        headers,
        function (assetRsp, uploadId, headers) {
          // 8.获取列举已上传段授权
          const listAuthResp = listUploadedPartAuthority(
            assetRsp,
            uploadId,
            headers
          );
          // 9.列举已上传段
          const partInfo = listUploadedPart(
            listAuthResp.sign_str,
            assetRsp,
            uploadId
          );
          // 10.获取合并段授权
          const mergeAuthResp = mergeUploadedPartAuthority(
            assetRsp,
            uploadId,
            headers
          );
          // 11.合并段
          mergeUploadedPart(
            mergeAuthResp.sign_str,
            partInfo,
            assetRsp,
            uploadId
          );
          // 12.确认上传
          confirmUploaded(assetRsp.asset_id, headers);
          res_info.asset_id = assetRsp.asset_id;
          // 13.媒资转码处理
          postVodAsset({ asset_id: assetRsp.asset_id }).then((res) => {
            // getVodAsset(assetRsp.asset_id).then((res) => {
            //   res_info = res;
            // });
          });
          resolve(res_info);
        }
      );
    });
  const data = await asyncOperation();
  return data;
}

// 2. 创建点播媒资
function createAsset(title, videoName, videoType, headers) {
  const body = {
    title: title,
    video_name: videoName,
    video_type: videoType,
  };
  const resp = sendRequest(
    "POST",
    urlList.createAssetUrl,
    JSON.stringify(body),
    headers
  );
  return JSON.parse(resp);
}

// 3.获取上传初始化任务授权
function getInitAuth(assetRsp, fileContentType, headers) {
  const params = {
    http_verb: "POST",
    content_type: fileContentType,
    bucket: assetRsp.target.bucket,
    object_key: assetRsp.target.object,
  };
  let temp = "?";
  for (const e in params) {
    temp += e + "=" + params[e] + "&";
  }
  const resp = sendRequest("GET", urlList.initAuthUrl + temp, null, headers);
  return JSON.parse(resp);
}

// 4.初始化分段上传, 返回uploadId
function initPartUpload(signStr, assetRsp, contentType) {
  const initUrl =
    "https://" +
    assetRsp.target.bucket +
    ".obs." +
    region +
    ".myhuaweicloud.com/" +
    assetRsp.target.object +
    "?uploads&" +
    signStr.sign_str;
  const resp = sendRequest("POST", initUrl, null, {
    "Content-Type": contentType,
  });
  const domParser = new DOMParser();
  const dom = domParser.parseFromString(resp, "text/xml");
  return dom.getElementsByTagName("UploadId")[0].firstChild.nodeValue;
}

// 5.获取分段上传授权
function getPartUploadAuthority(
  assetRsp,
  fileContentType,
  uploadId,
  contentMd5,
  partNumber,
  headers
) {
  const params = {
    http_verb: "PUT",
    content_type: fileContentType,
    bucket: assetRsp.target.bucket,
    object_key: assetRsp.target.object,
    content_md5: encodeURIComponent(contentMd5), // 有特殊符号，此处加转义
    upload_id: uploadId,
    part_number: partNumber,
  };
  let temp = "?";
  for (const e in params) {
    temp += e + "=" + params[e] + "&";
  }
  const resp = sendRequest("GET", urlList.initAuthUrl + temp, null, headers);
  return JSON.parse(resp);
}

// 6.上传分段
function uploadPartFile(
  uploadAuth,
  assetRsp,
  contentMd5,
  partNumber,
  uploadId,
  content
) {
  const url =
    "https://" +
    assetRsp.target.bucket +
    ".obs." +
    region +
    ".myhuaweicloud.com/" +
    assetRsp.target.object +
    "?partNumber=" +
    partNumber +
    "&uploadId=" +
    uploadId +
    "&" +
    uploadAuth.sign_str;
  const headers = {
    "Content-Type": "application/octet-stream",
    "Content-MD5": contentMd5,
  };
  const resp = sendRequest("PUT", url, content, headers);
}

// 7.循环5-6上传分段
function readAndUpload(
  file,
  blobSlice,
  position,
  partNum,
  assetRsp,
  fileContentType,
  uploadId,
  headers,
  afterUpload
) {
  const fileReader = new FileReader();
  fileReader.onload = function (e) {
    // 先文件分段md5
    const md5 = md5_min(e.target.result);
    // md5转字节数组
    const bmd5 = Str2Bytes(md5);
    // base64 encode md5字节数组
    const content_md5 = base64js_min.fromByteArray(bmd5);

    // 5.获取分段上传授权
    const uploadAuth = getPartUploadAuthority(
      assetRsp,
      fileContentType,
      uploadId,
      content_md5,
      partNum,
      headers
    );
    // 6.上传分段
    uploadPartFile(
      uploadAuth,
      assetRsp,
      content_md5,
      partNum,
      uploadId,
      e.target.result
    );

    partNum++;

    // 判断是否读完，未读完继续上传
    if (position + bufferSize < file.size) {
      // console.log(
      //   "上传的进度？",
      //   position + bufferSize,
      //   file.size,
      //   (((position + bufferSize) / file.size) * 100).toFixed(0)
      // );
      uploadProgress = (((position + bufferSize) / file.size) * 100).toFixed(0); //上传进度

      readAndUpload(
        file,
        blobSlice,
        position + bufferSize,
        partNum,
        assetRsp,
        fileContentType,
        uploadId,
        headers,
        afterUpload
      );
    } else {
      // 全部上传完成后执行后续步骤
      afterUpload(assetRsp, uploadId, headers);
    }
  };
  fileReader.readAsArrayBuffer(
    blobSlice.call(file, position, position + bufferSize)
  );
}

// 8.获取列举分段上传授权
function listUploadedPartAuthority(assetRsp, uploadId, headers) {
  const params = {
    http_verb: "GET",
    bucket: assetRsp.target.bucket,
    object_key: assetRsp.target.object,
    upload_id: uploadId,
  };
  let temp = "?";
  for (const e in params) {
    temp += e + "=" + params[e] + "&";
  }
  const resp = sendRequest("GET", urlList.initAuthUrl + temp, null, headers);
  return JSON.parse(resp);
}

// 9.查询已上传的分段
function listUploadedPart(signStr, assetRsp, uploadId) {
  const url =
    "https://" +
    assetRsp.target.bucket +
    ".obs." +
    region +
    ".myhuaweicloud.com/" +
    assetRsp.target.object +
    "?" +
    signStr +
    "&uploadId=" +
    uploadId;
  let nextPartNumberMarker = 0;

  let result = "<CompleteMultipartUpload>";
  const domParser = new DOMParser();
  while (true) {
    const resp = sendRequest(
      "GET",
      url + "&part-number-marker=" + nextPartNumberMarker,
      null,
      {}
    );
    const dom = domParser.parseFromString(resp, "text/xml");
    const part = dom.getElementsByTagName("Part");

    // 构建合并段的参数
    for (let i = 0; i < part.length; i++) {
      const ele = part[i];
      const num =
        ele.getElementsByTagName("PartNumber")[0].firstChild.nodeValue;
      const tag = ele.getElementsByTagName("ETag")[0].firstChild.nodeValue;
      result +=
        "<Part>" +
        "<PartNumber>" +
        num +
        "</PartNumber>" +
        "<ETag>" +
        tag +
        "</ETag>" +
        "</Part>";
    }

    nextPartNumberMarker = Number(
      dom.getElementsByTagName("NextPartNumberMarker")[0].firstChild.nodeValue
    );

    if (nextPartNumberMarker % 1000 != 0) {
      break;
    }
  }
  result += "</CompleteMultipartUpload>";
  return result;
}

// 10.获取合并段授权
function mergeUploadedPartAuthority(assetRsp, uploadId, headers) {
  const params = {
    http_verb: "POST",
    bucket: assetRsp.target.bucket,
    object_key: assetRsp.target.object,
    upload_id: uploadId,
  };
  let temp = "?";
  for (const e in params) {
    temp += e + "=" + params[e] + "&";
  }
  const resp = sendRequest("GET", urlList.initAuthUrl + temp, null, headers);
  return JSON.parse(resp);
}

// 11.合并段
function mergeUploadedPart(signStr, partInfo, assetRsp, uploadId) {
  const url =
    "https://" +
    assetRsp.target.bucket +
    ".obs." +
    region +
    ".myhuaweicloud.com/" +
    assetRsp.target.object +
    "?" +
    signStr +
    "&uploadId=" +
    uploadId;
  const resp = sendRequest("POST", url, partInfo, {
    "Content-Type": "application/xml",
  });
}

// 12.确认上传
async function confirmUploaded(assetId, headers) {
  const body = {
    asset_id: assetId,
    status: "CREATED",
  };
  const resp = sendRequest(
    "POST",
    urlList.confirmUploadedUrl,
    JSON.stringify(body),
    headers
  );
  // console.log(resp)
  return resp;
}

// 发送请求并返回结果，可以实际框架情况进行替换
function sendRequest(method, url, data, headers) {
  const xhr = new XMLHttpRequest();
  xhr.open(method, url, false);
  for (const i in headers) {
    xhr.setRequestHeader(i, headers[i]);
  }
  xhr.send(data);
  return xhr.responseText;
}

// base64，可以根据实际使用的前端框架进行替换
var base64js_min = createCommonjsModule(function (e, t) {
    e.exports = (function o(n, i, a) {
      function s(t, e) {
        if (!i[t]) {
          if (!n[t]) {
            var r = "function" == typeof commonjsRequire && commonjsRequire;
            if (!e && r) return r(t, !0);
            if (l) return l(t, !0);
            throw (
              (((e = new Error("Cannot find module '" + t + "'")).code =
                "MODULE_NOT_FOUND"),
              e)
            );
          }
          (r = i[t] =
            {
              exports: {},
            }),
            n[t][0].call(
              r.exports,
              function (e) {
                return s(n[t][1][e] || e);
              },
              r,
              r.exports,
              o,
              n,
              i,
              a
            );
        }
        return i[t].exports;
      }
      for (
        var l = "function" == typeof commonjsRequire && commonjsRequire, e = 0;
        e < a.length;
        e++
      )
        s(a[e]);
      return s;
    })(
      {
        "/": [
          function (e, t, r) {
            (r.byteLength = function (e) {
              var e = d(e),
                t = e[0],
                e = e[1];
              return (3 * (t + e)) / 4 - e;
            }),
              (r.toByteArray = function (e) {
                for (
                  var t,
                    r = d(e),
                    o = r[0],
                    r = r[1],
                    n = new u(
                      (function (e, t) {
                        return (3 * (e + t)) / 4 - t;
                      })(o, r)
                    ),
                    i = 0,
                    a = 0 < r ? o - 4 : o,
                    s = 0;
                  s < a;
                  s += 4
                )
                  (t =
                    (l[e.charCodeAt(s)] << 18) |
                    (l[e.charCodeAt(s + 1)] << 12) |
                    (l[e.charCodeAt(s + 2)] << 6) |
                    l[e.charCodeAt(s + 3)]),
                    (n[i++] = (t >> 16) & 255),
                    (n[i++] = (t >> 8) & 255),
                    (n[i++] = 255 & t);
                2 === r &&
                  ((t =
                    (l[e.charCodeAt(s)] << 2) | (l[e.charCodeAt(s + 1)] >> 4)),
                  (n[i++] = 255 & t));
                1 === r &&
                  ((t =
                    (l[e.charCodeAt(s)] << 10) |
                    (l[e.charCodeAt(s + 1)] << 4) |
                    (l[e.charCodeAt(s + 2)] >> 2)),
                  (n[i++] = (t >> 8) & 255),
                  (n[i++] = 255 & t));
                return n;
              }),
              (r.fromByteArray = function (e) {
                for (
                  var t, r = e.length, o = r % 3, n = [], i = 0, a = r - o;
                  i < a;
                  i += 16383
                )
                  n.push(
                    (function (e, t, r) {
                      for (var o, n = [], i = t; i < r; i += 3)
                        (o =
                          ((e[i] << 16) & 16711680) +
                          ((e[i + 1] << 8) & 65280) +
                          (255 & e[i + 2])),
                          n.push(
                            (function (e) {
                              return (
                                s[(e >> 18) & 63] +
                                s[(e >> 12) & 63] +
                                s[(e >> 6) & 63] +
                                s[63 & e]
                              );
                            })(o)
                          );
                      return n.join("");
                    })(e, i, a < i + 16383 ? a : i + 16383)
                  );
                1 == o
                  ? ((t = e[r - 1]),
                    n.push(s[t >> 2] + s[(t << 4) & 63] + "=="))
                  : 2 == o &&
                    ((t = (e[r - 2] << 8) + e[r - 1]),
                    n.push(
                      s[t >> 10] + s[(t >> 4) & 63] + s[(t << 2) & 63] + "="
                    ));
                return n.join("");
              });
            for (
              var s = [],
                l = [],
                u = "undefined" != typeof Uint8Array ? Uint8Array : Array,
                o =
                  "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
                n = 0,
                i = o.length;
              n < i;
              ++n
            )
              (s[n] = o[n]), (l[o.charCodeAt(n)] = n);
            function d(e) {
              let t = e.length;
              if (0 < t % 4)
                throw new Error(
                  "Invalid string. Length must be a multiple of 4"
                );
              (e = e.indexOf("=")),
                (t = (e = -1 === e ? t : e) === t ? 0 : 4 - (e % 4));
              return [e, t];
            }
            (l["-".charCodeAt(0)] = 62), (l["_".charCodeAt(0)] = 63);
          },
          {},
        ],
      },
      {},
      []
    )("/");
  }),
  // md5，可以根据实际使用的前端框架进行替换
  md5_min = createCommonjsModule(function (module) {
    !(function () {
      function t(e) {
        e
          ? ((d[0] =
              d[16] =
              d[1] =
              d[2] =
              d[3] =
              d[4] =
              d[5] =
              d[6] =
              d[7] =
              d[8] =
              d[9] =
              d[10] =
              d[11] =
              d[12] =
              d[13] =
              d[14] =
              d[15] =
                0),
            (this.blocks = d),
            (this.buffer8 = l))
          : a
          ? ((e = new ArrayBuffer(68)),
            (this.buffer8 = new Uint8Array(e)),
            (this.blocks = new Uint32Array(e)))
          : (this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]),
          (this.h0 =
            this.h1 =
            this.h2 =
            this.h3 =
            this.start =
            this.bytes =
            this.hBytes =
              0),
          (this.finalized = this.hashed = !1),
          (this.first = !0);
      }
      var r = "input is invalid type",
        e =
          "object" ==
          ("undefined" == typeof window ? "undefined" : _typeof(window)),
        i = e ? window : {},
        s =
          (i.JS_MD5_NO_WINDOW && (e = !1),
          !e &&
            "object" ==
              ("undefined" == typeof self ? "undefined" : _typeof(self))),
        h =
          !i.JS_MD5_NO_NODE_JS &&
          "object" ==
            ("undefined" == typeof process ? "undefined" : _typeof(process)) &&
          process.versions &&
          process.versions.node,
        f =
          (h ? (i = commonjsGlobal) : s && (i = self),
          !i.JS_MD5_NO_COMMON_JS && module.exports),
        o = !1,
        a = !i.JS_MD5_NO_ARRAY_BUFFER && "undefined" != typeof ArrayBuffer,
        n = "0123456789abcdef".split(""),
        u = [128, 32768, 8388608, -2147483648],
        y = [0, 8, 16, 24],
        c = ["hex", "array", "digest", "buffer", "arrayBuffer", "base64"],
        p =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(
            ""
          ),
        d = [],
        l,
        A,
        l,
        d,
        b =
          (a &&
            ((A = new ArrayBuffer(68)),
            (l = new Uint8Array(A)),
            (d = new Uint32Array(A))),
          (!i.JS_MD5_NO_NODE_JS && Array.isArray) ||
            (Array.isArray = function (e) {
              return "[object Array]" === Object.prototype.toString.call(e);
            }),
          !a ||
            (!i.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW && ArrayBuffer.isView) ||
            (ArrayBuffer.isView = function (e) {
              return (
                "object" == _typeof(e) &&
                e.buffer &&
                e.buffer.constructor === ArrayBuffer
              );
            }),
          function (r) {
            return function (e) {
              return new t(!0).update(e)[r]();
            };
          }),
        v = function () {
          let r = b("hex");
          ((r = h ? w(r) : r).create = function () {
            return new t();
          }),
            (r.update = function (e) {
              return r.create().update(e);
            });
          for (let e = 0; e < c.length; ++e) {
            const o = c[e];
            r[o] = b(o);
          }
          return r;
        },
        w = function w(t) {
          const e = eval("require('crypto')"),
            i = eval("require('buffer').Buffer"),
            s = function (o) {
              if ("string" == typeof o)
                return e.createHash("md5").update(o, "utf8").digest("hex");
              if (null == o) throw r;
              return (
                o.constructor === ArrayBuffer && (o = new Uint8Array(o)),
                Array.isArray(o) || ArrayBuffer.isView(o) || o.constructor === i
                  ? e.createHash("md5").update(new i(o)).digest("hex")
                  : t(o)
              );
            };
          return s;
        },
        _ =
          ((t.prototype.update = function (e) {
            if (!this.finalized) {
              let t,
                o = _typeof(e);
              if ("string" !== o) {
                if ("object" !== o) throw r;
                if (null === e) throw r;
                if (a && e.constructor === ArrayBuffer) e = new Uint8Array(e);
                else if (!(Array.isArray(e) || (a && ArrayBuffer.isView(e))))
                  throw r;
                t = !0;
              }
              for (
                var n,
                  i,
                  s = 0,
                  l = e.length,
                  u = this.blocks,
                  d = this.buffer8;
                s < l;

              ) {
                if (
                  (this.hashed &&
                    ((this.hashed = !1),
                    (u[0] = u[16]),
                    (u[16] =
                      u[1] =
                      u[2] =
                      u[3] =
                      u[4] =
                      u[5] =
                      u[6] =
                      u[7] =
                      u[8] =
                      u[9] =
                      u[10] =
                      u[11] =
                      u[12] =
                      u[13] =
                      u[14] =
                      u[15] =
                        0)),
                  t)
                )
                  if (a)
                    for (i = this.start; s < l && i < 64; ++s) d[i++] = e[s];
                  else
                    for (i = this.start; s < l && i < 64; ++s)
                      u[i >> 2] |= e[s] << y[3 & i++];
                else if (a)
                  for (i = this.start; s < l && i < 64; ++s)
                    (n = e.charCodeAt(s)) < 128
                      ? (d[i++] = n)
                      : (n < 2048
                          ? (d[i++] = 192 | (n >> 6))
                          : (n < 55296 || 57344 <= n
                              ? (d[i++] = 224 | (n >> 12))
                              : ((n =
                                  65536 +
                                  (((1023 & n) << 10) |
                                    (1023 & e.charCodeAt(++s)))),
                                (d[i++] = 240 | (n >> 18)),
                                (d[i++] = 128 | ((n >> 12) & 63))),
                            (d[i++] = 128 | ((n >> 6) & 63))),
                        (d[i++] = 128 | (63 & n)));
                else
                  for (i = this.start; s < l && i < 64; ++s)
                    (n = e.charCodeAt(s)) < 128
                      ? (u[i >> 2] |= n << y[3 & i++])
                      : (n < 2048
                          ? (u[i >> 2] |= (192 | (n >> 6)) << y[3 & i++])
                          : (n < 55296 || 57344 <= n
                              ? (u[i >> 2] |= (224 | (n >> 12)) << y[3 & i++])
                              : ((n =
                                  65536 +
                                  (((1023 & n) << 10) |
                                    (1023 & e.charCodeAt(++s)))),
                                (u[i >> 2] |= (240 | (n >> 18)) << y[3 & i++]),
                                (u[i >> 2] |=
                                  (128 | ((n >> 12) & 63)) << y[3 & i++])),
                            (u[i >> 2] |=
                              (128 | ((n >> 6) & 63)) << y[3 & i++])),
                        (u[i >> 2] |= (128 | (63 & n)) << y[3 & i++]));
                (this.lastByteIndex = i),
                  (this.bytes += i - this.start),
                  64 <= i
                    ? ((this.start = i - 64), this.hash(), (this.hashed = !0))
                    : (this.start = i);
              }
              return (
                4294967295 < this.bytes &&
                  ((this.hBytes += (this.bytes / 4294967296) << 0),
                  (this.bytes = this.bytes % 4294967296)),
                this
              );
            }
          }),
          (t.prototype.finalize = function () {
            let e, t;
            this.finalized ||
              ((this.finalized = !0),
              ((e = this.blocks)[(t = this.lastByteIndex) >> 2] |= u[3 & t]),
              56 <= t &&
                (this.hashed || this.hash(),
                (e[0] = e[16]),
                (e[16] =
                  e[1] =
                  e[2] =
                  e[3] =
                  e[4] =
                  e[5] =
                  e[6] =
                  e[7] =
                  e[8] =
                  e[9] =
                  e[10] =
                  e[11] =
                  e[12] =
                  e[13] =
                  e[14] =
                  e[15] =
                    0)),
              (e[14] = this.bytes << 3),
              (e[15] = (this.hBytes << 3) | (this.bytes >>> 29)),
              this.hash());
          }),
          (t.prototype.hash = function () {
            var e,
              t,
              r,
              o,
              n,
              i = this.blocks,
              a = this.first
                ? ((((a =
                    ((e =
                      ((((e = i[0] - 680876937) << 7) | (e >>> 25)) -
                        271733879) <<
                      0) ^
                      ((t =
                        ((((t =
                          (-271733879 ^
                            ((r =
                              ((((r =
                                (-1732584194 ^ (2004318071 & e)) +
                                i[1] -
                                117830708) <<
                                12) |
                                (r >>> 20)) +
                                e) <<
                              0) &
                              (-271733879 ^ e))) +
                          i[2] -
                          1126478375) <<
                          17) |
                          (t >>> 15)) +
                          r) <<
                        0) &
                        (r ^ e))) +
                    i[3] -
                    1316259209) <<
                    22) |
                    (a >>> 10)) +
                    t) <<
                  0
                : ((e = this.h0),
                  (a = this.h1),
                  (t = this.h2),
                  ((((a +=
                    ((e =
                      ((((e +=
                        ((r = this.h3) ^ (a & (t ^ r))) + i[0] - 680876936) <<
                        7) |
                        (e >>> 25)) +
                        a) <<
                      0) ^
                      ((t =
                        ((((t +=
                          (a ^
                            ((r =
                              ((((r +=
                                (t ^ (e & (a ^ t))) + i[1] - 389564586) <<
                                12) |
                                (r >>> 20)) +
                                e) <<
                              0) &
                              (e ^ a))) +
                          i[2] +
                          606105819) <<
                          17) |
                          (t >>> 15)) +
                          r) <<
                        0) &
                        (r ^ e))) +
                    i[3] -
                    1044525330) <<
                    22) |
                    (a >>> 10)) +
                    t) <<
                    0);
            (a =
              ((((a +=
                ((e =
                  ((((e += (r ^ (a & (t ^ r))) + i[4] - 176418897) << 7) |
                    (e >>> 25)) +
                    a) <<
                  0) ^
                  ((t =
                    ((((t +=
                      (a ^
                        ((r =
                          ((((r += (t ^ (e & (a ^ t))) + i[5] + 1200080426) <<
                            12) |
                            (r >>> 20)) +
                            e) <<
                          0) &
                          (e ^ a))) +
                      i[6] -
                      1473231341) <<
                      17) |
                      (t >>> 15)) +
                      r) <<
                    0) &
                    (r ^ e))) +
                i[7] -
                45705983) <<
                22) |
                (a >>> 10)) +
                t) <<
              0),
              (a =
                ((((a +=
                  ((e =
                    ((((e += (r ^ (a & (t ^ r))) + i[8] + 1770035416) << 7) |
                      (e >>> 25)) +
                      a) <<
                    0) ^
                    ((t =
                      ((((t +=
                        (a ^
                          ((r =
                            ((((r += (t ^ (e & (a ^ t))) + i[9] - 1958414417) <<
                              12) |
                              (r >>> 20)) +
                              e) <<
                            0) &
                            (e ^ a))) +
                        i[10] -
                        42063) <<
                        17) |
                        (t >>> 15)) +
                        r) <<
                      0) &
                      (r ^ e))) +
                  i[11] -
                  1990404162) <<
                  22) |
                  (a >>> 10)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((e =
                    ((((e += (r ^ (a & (t ^ r))) + i[12] + 1804603682) << 7) |
                      (e >>> 25)) +
                      a) <<
                    0) ^
                    ((t =
                      ((((t +=
                        (a ^
                          ((r =
                            ((((r += (t ^ (e & (a ^ t))) + i[13] - 40341101) <<
                              12) |
                              (r >>> 20)) +
                              e) <<
                            0) &
                            (e ^ a))) +
                        i[14] -
                        1502002290) <<
                        17) |
                        (t >>> 15)) +
                        r) <<
                      0) &
                      (r ^ e))) +
                  i[15] +
                  1236535329) <<
                  22) |
                  (a >>> 10)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        (t &
                          ((e =
                            ((((e += (t ^ (r & (a ^ t))) + i[1] - 165796510) <<
                              5) |
                              (e >>> 27)) +
                              a) <<
                            0) ^
                            a))) +
                      i[6] -
                      1069501632) <<
                      9) |
                      (r >>> 23)) +
                      e) <<
                    0) ^
                    (e &
                      ((t =
                        ((((t += (e ^ (a & (r ^ e))) + i[11] + 643717713) <<
                          14) |
                          (t >>> 18)) +
                          r) <<
                        0) ^
                        r))) +
                  i[0] -
                  373897302) <<
                  20) |
                  (a >>> 12)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        (t &
                          ((e =
                            ((((e += (t ^ (r & (a ^ t))) + i[5] - 701558691) <<
                              5) |
                              (e >>> 27)) +
                              a) <<
                            0) ^
                            a))) +
                      i[10] +
                      38016083) <<
                      9) |
                      (r >>> 23)) +
                      e) <<
                    0) ^
                    (e &
                      ((t =
                        ((((t += (e ^ (a & (r ^ e))) + i[15] - 660478335) <<
                          14) |
                          (t >>> 18)) +
                          r) <<
                        0) ^
                        r))) +
                  i[4] -
                  405537848) <<
                  20) |
                  (a >>> 12)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        (t &
                          ((e =
                            ((((e += (t ^ (r & (a ^ t))) + i[9] + 568446438) <<
                              5) |
                              (e >>> 27)) +
                              a) <<
                            0) ^
                            a))) +
                      i[14] -
                      1019803690) <<
                      9) |
                      (r >>> 23)) +
                      e) <<
                    0) ^
                    (e &
                      ((t =
                        ((((t += (e ^ (a & (r ^ e))) + i[3] - 187363961) <<
                          14) |
                          (t >>> 18)) +
                          r) <<
                        0) ^
                        r))) +
                  i[8] +
                  1163531501) <<
                  20) |
                  (a >>> 12)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        (t &
                          ((e =
                            ((((e +=
                              (t ^ (r & (a ^ t))) + i[13] - 1444681467) <<
                              5) |
                              (e >>> 27)) +
                              a) <<
                            0) ^
                            a))) +
                      i[2] -
                      51403784) <<
                      9) |
                      (r >>> 23)) +
                      e) <<
                    0) ^
                    (e &
                      ((t =
                        ((((t += (e ^ (a & (r ^ e))) + i[7] + 1735328473) <<
                          14) |
                          (t >>> 18)) +
                          r) <<
                        0) ^
                        r))) +
                  i[12] -
                  1926607734) <<
                  20) |
                  (a >>> 12)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((n =
                    (r =
                      ((((r +=
                        ((o = a ^ t) ^
                          (e =
                            ((((e += (o ^ r) + i[5] - 378558) << 4) |
                              (e >>> 28)) +
                              a) <<
                            0)) +
                        i[8] -
                        2022574463) <<
                        11) |
                        (r >>> 21)) +
                        e) <<
                      0) ^ e) ^
                    (t =
                      ((((t += (n ^ a) + i[11] + 1839030562) << 16) |
                        (t >>> 16)) +
                        r) <<
                      0)) +
                  i[14] -
                  35309556) <<
                  23) |
                  (a >>> 9)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((n =
                    (r =
                      ((((r +=
                        ((o = a ^ t) ^
                          (e =
                            ((((e += (o ^ r) + i[1] - 1530992060) << 4) |
                              (e >>> 28)) +
                              a) <<
                            0)) +
                        i[4] +
                        1272893353) <<
                        11) |
                        (r >>> 21)) +
                        e) <<
                      0) ^ e) ^
                    (t =
                      ((((t += (n ^ a) + i[7] - 155497632) << 16) |
                        (t >>> 16)) +
                        r) <<
                      0)) +
                  i[10] -
                  1094730640) <<
                  23) |
                  (a >>> 9)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((n =
                    (r =
                      ((((r +=
                        ((o = a ^ t) ^
                          (e =
                            ((((e += (o ^ r) + i[13] + 681279174) << 4) |
                              (e >>> 28)) +
                              a) <<
                            0)) +
                        i[0] -
                        358537222) <<
                        11) |
                        (r >>> 21)) +
                        e) <<
                      0) ^ e) ^
                    (t =
                      ((((t += (n ^ a) + i[3] - 722521979) << 16) |
                        (t >>> 16)) +
                        r) <<
                      0)) +
                  i[6] +
                  76029189) <<
                  23) |
                  (a >>> 9)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((n =
                    (r =
                      ((((r +=
                        ((o = a ^ t) ^
                          (e =
                            ((((e += (o ^ r) + i[9] - 640364487) << 4) |
                              (e >>> 28)) +
                              a) <<
                            0)) +
                        i[12] -
                        421815835) <<
                        11) |
                        (r >>> 21)) +
                        e) <<
                      0) ^ e) ^
                    (t =
                      ((((t += (n ^ a) + i[15] + 530742520) << 16) |
                        (t >>> 16)) +
                        r) <<
                      0)) +
                  i[2] -
                  995338651) <<
                  23) |
                  (a >>> 9)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        ((e =
                          ((((e += (t ^ (a | ~r)) + i[0] - 198630844) << 6) |
                            (e >>> 26)) +
                            a) <<
                          0) |
                          ~t)) +
                      i[7] +
                      1126891415) <<
                      10) |
                      (r >>> 22)) +
                      e) <<
                    0) ^
                    ((t =
                      ((((t += (e ^ (r | ~a)) + i[14] - 1416354905) << 15) |
                        (t >>> 17)) +
                        r) <<
                      0) |
                      ~e)) +
                  i[5] -
                  57434055) <<
                  21) |
                  (a >>> 11)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        ((e =
                          ((((e += (t ^ (a | ~r)) + i[12] + 1700485571) << 6) |
                            (e >>> 26)) +
                            a) <<
                          0) |
                          ~t)) +
                      i[3] -
                      1894986606) <<
                      10) |
                      (r >>> 22)) +
                      e) <<
                    0) ^
                    ((t =
                      ((((t += (e ^ (r | ~a)) + i[10] - 1051523) << 15) |
                        (t >>> 17)) +
                        r) <<
                      0) |
                      ~e)) +
                  i[1] -
                  2054922799) <<
                  21) |
                  (a >>> 11)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        ((e =
                          ((((e += (t ^ (a | ~r)) + i[8] + 1873313359) << 6) |
                            (e >>> 26)) +
                            a) <<
                          0) |
                          ~t)) +
                      i[15] -
                      30611744) <<
                      10) |
                      (r >>> 22)) +
                      e) <<
                    0) ^
                    ((t =
                      ((((t += (e ^ (r | ~a)) + i[6] - 1560198380) << 15) |
                        (t >>> 17)) +
                        r) <<
                      0) |
                      ~e)) +
                  i[13] +
                  1309151649) <<
                  21) |
                  (a >>> 11)) +
                  t) <<
                0),
              (a =
                ((((a +=
                  ((r =
                    ((((r +=
                      (a ^
                        ((e =
                          ((((e += (t ^ (a | ~r)) + i[4] - 145523070) << 6) |
                            (e >>> 26)) +
                            a) <<
                          0) |
                          ~t)) +
                      i[11] -
                      1120210379) <<
                      10) |
                      (r >>> 22)) +
                      e) <<
                    0) ^
                    ((t =
                      ((((t += (e ^ (r | ~a)) + i[2] + 718787259) << 15) |
                        (t >>> 17)) +
                        r) <<
                      0) |
                      ~e)) +
                  i[9] -
                  343485551) <<
                  21) |
                  (a >>> 11)) +
                  t) <<
                0),
              this.first
                ? ((this.h0 = (e + 1732584193) << 0),
                  (this.h1 = (a - 271733879) << 0),
                  (this.h2 = (t - 1732584194) << 0),
                  (this.h3 = (r + 271733878) << 0),
                  (this.first = !1))
                : ((this.h0 = (this.h0 + e) << 0),
                  (this.h1 = (this.h1 + a) << 0),
                  (this.h2 = (this.h2 + t) << 0),
                  (this.h3 = (this.h3 + r) << 0));
          }),
          (t.prototype.hex = function () {
            this.finalize();
            const e = this.h0,
              t = this.h1,
              r = this.h2,
              o = this.h3;
            return (
              n[(e >> 4) & 15] +
              n[15 & e] +
              n[(e >> 12) & 15] +
              n[(e >> 8) & 15] +
              n[(e >> 20) & 15] +
              n[(e >> 16) & 15] +
              n[(e >> 28) & 15] +
              n[(e >> 24) & 15] +
              n[(t >> 4) & 15] +
              n[15 & t] +
              n[(t >> 12) & 15] +
              n[(t >> 8) & 15] +
              n[(t >> 20) & 15] +
              n[(t >> 16) & 15] +
              n[(t >> 28) & 15] +
              n[(t >> 24) & 15] +
              n[(r >> 4) & 15] +
              n[15 & r] +
              n[(r >> 12) & 15] +
              n[(r >> 8) & 15] +
              n[(r >> 20) & 15] +
              n[(r >> 16) & 15] +
              n[(r >> 28) & 15] +
              n[(r >> 24) & 15] +
              n[(o >> 4) & 15] +
              n[15 & o] +
              n[(o >> 12) & 15] +
              n[(o >> 8) & 15] +
              n[(o >> 20) & 15] +
              n[(o >> 16) & 15] +
              n[(o >> 28) & 15] +
              n[(o >> 24) & 15]
            );
          }),
          (t.prototype.toString = t.prototype.hex),
          (t.prototype.digest = function () {
            this.finalize();
            const e = this.h0,
              t = this.h1,
              r = this.h2,
              o = this.h3;
            return [
              255 & e,
              (e >> 8) & 255,
              (e >> 16) & 255,
              (e >> 24) & 255,
              255 & t,
              (t >> 8) & 255,
              (t >> 16) & 255,
              (t >> 24) & 255,
              255 & r,
              (r >> 8) & 255,
              (r >> 16) & 255,
              (r >> 24) & 255,
              255 & o,
              (o >> 8) & 255,
              (o >> 16) & 255,
              (o >> 24) & 255,
            ];
          }),
          (t.prototype.array = t.prototype.digest),
          (t.prototype.arrayBuffer = function () {
            this.finalize();
            const e = new ArrayBuffer(16),
              t = new Uint32Array(e);
            return (
              (t[0] = this.h0),
              (t[1] = this.h1),
              (t[2] = this.h2),
              (t[3] = this.h3),
              e
            );
          }),
          (t.prototype.buffer = t.prototype.arrayBuffer),
          (t.prototype.base64 = function () {
            for (var e, t, r, o = "", n = this.array(), i = 0; i < 15; )
              (e = n[i++]),
                (t = n[i++]),
                (r = n[i++]),
                (o +=
                  p[e >>> 2] +
                  p[63 & ((e << 4) | (t >>> 4))] +
                  p[63 & ((t << 2) | (r >>> 6))] +
                  p[63 & r]);
            return (e = n[i]), o + (p[e >>> 2] + p[(e << 4) & 63] + "==");
          }),
          v());
      f ? (module.exports = _) : (i.md5 = _);
    })();
  });

function _typeof(e) {
  return (_typeof =
    "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
      ? function (e) {
          return typeof e;
        }
      : function (e) {
          return e &&
            "function" == typeof Symbol &&
            e.constructor === Symbol &&
            e !== Symbol.prototype
            ? "symbol"
            : typeof e;
        })(e);
}

function createCommonjsModule(e, t) {
  return (
    e(
      (t = {
        exports: {},
      }),
      t.exports
    ),
    t.exports
  );
}

function Str2Bytes(e) {
  let t = 0,
    r = e.length;
  if (r % 2 != 0) return null;
  r /= 2;
  for (var o = [], n = 0; n < r; n++) {
    var i = e.substr(t, 2),
      i = parseInt(i, 16);
    o.push(i), (t += 2);
  }
  return o;
}
