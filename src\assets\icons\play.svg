<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <style>
      .a { fill: #fff; }
      .b { clip-path: url(#a); }
      .c { fill: url(#b); }
      .d { filter: url(#e); }
      .e { filter: url(#c); }
    </style>
    <clipPath id="a">
      <rect class="a" width="32" height="32" />
    </clipPath>
    <linearGradient id="b" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#84a395" />
      <stop offset="1" stop-color="#a6bfb0" />
    </linearGradient>
    <filter id="c" x="3" y="2" width="26.647" height="28.94" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha" />
      <feGaussianBlur stdDeviation="1" result="d" />
      <feFlood flood-color="#238306" flood-opacity="0.192" />
      <feComposite operator="in" in2="d" />
      <feComposite in="SourceGraphic" />
    </filter>
    <filter id="e" x="3" y="2" width="26.647" height="28.94" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha" />
      <feGaussianBlur stdDeviation="1" result="f" />
      <feFlood flood-color="#428930" flood-opacity="0.482" result="g" />
      <feComposite operator="out" in="SourceGraphic" in2="f" />
      <feComposite operator="in" in="g" />
      <feComposite operator="in" in2="SourceGraphic" />
    </filter>
  </defs>
  <g class="b">
    <g data-type="innerShadowGroup">
      <g class="e">
        <path class="c" d="M6 6.437c0-1.27.9-1.792 1.994-1.171l17.828 10.081c1.1.623 1.1 1.635 0 2.255L7.994 27.683c-1.1.623-1.994.1-1.994-1.171z" />
      </g>
      <g class="d">
        <path class="a" d="M6 6.437c0-1.27.9-1.792 1.994-1.171l17.828 10.081c1.1.623 1.1 1.635 0 2.255L7.994 27.683c-1.1.623-1.994.1-1.994-1.171z" />
      </g>
    </g>
  </g>
</svg>