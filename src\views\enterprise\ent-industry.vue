<!--
 * @Author: czf <EMAIL>
 * @Date: 2024-10-30 09:51:45
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-10-30 10:15:17
 * @FilePath: \gkadmin\src\views\enterprise\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class=""></div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  toRefs,
  onBeforeMount,
  onMounted,
  watchEffect,
  computed,
  getCurrentInstance,
} from "vue";
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
</script>

<style scoped lang="scss">
.class_name {
}
</style>
