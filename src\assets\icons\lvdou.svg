<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="52" height="52" viewBox="0 0 52 52"><defs><style>.a{stroke:#707070;}.a,.c{fill:url(#a);}.b{clip-path:url(#b);}.d{fill:#fff;}.e{filter:url(#h);}.f{filter:url(#d);}</style><linearGradient id="a" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#0ebc72"/><stop offset="1" stop-color="#20c27c"/></linearGradient><clipPath id="b"><rect class="a" width="52" height="52" transform="translate(-0.487 0.272)"/></clipPath><filter id="d"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="e"/><feFlood flood-color="#a1f464" flood-opacity="0.565" result="f"/><feComposite operator="out" in="SourceGraphic" in2="e"/><feComposite operator="in" in="f"/><feComposite operator="in" in2="SourceGraphic"/></filter><filter id="h"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="i"/><feFlood flood-color="#a3f453" flood-opacity="0.584" result="j"/><feComposite operator="out" in="SourceGraphic" in2="i"/><feComposite operator="in" in="j"/><feComposite operator="in" in2="SourceGraphic"/></filter></defs><g class="b" transform="translate(0.487 -0.272)"><g transform="translate(0.782 5.475)"><g data-type="innerShadowGroup"><path class="c" d="M742.945-40.472a9.768,9.768,0,0,1,.1-9.781c1.415-2.519,3.575-4.022,5.779-4.022a4.8,4.8,0,0,1,.72.055,5.641,5.641,0,0,0,.836.078h.031c.034-.283.05-.685.065-1a13.315,13.315,0,0,1,.625-3.91c-7.711.818-.96-7-7.812-13.6-7.049-6.792-18.614-3.386-20.644,2.368-3.723,6.431,1.693,16.921,5.076,22.674,3.292,5.8,10.523,8.854,17.266,9.272A7.418,7.418,0,0,1,742.945-40.472ZM742.7-61.321a28.22,28.22,0,0,0,2.835,3.165,10.733,10.733,0,0,0,1.467,1.2,6.58,6.58,0,0,0,1.958.883,2.833,2.833,0,0,1-2.5.623,4.72,4.72,0,0,1-2.332-1.213,5.351,5.351,0,0,1-1.376-2.145A3.767,3.767,0,0,1,742.7-61.321Zm-12.756,8.166a10.519,10.519,0,0,1-2.978-4.354,15.361,15.361,0,0,1-.942-5.058,10.752,10.752,0,0,1,.9-4.99,31.617,31.617,0,0,0,.68,4.814,28.964,28.964,0,0,0,1.275,4.513,13.661,13.661,0,0,0,2.184,3.934,9.781,9.781,0,0,0,3.765,2.734A6.624,6.624,0,0,1,729.939-53.155Z" transform="translate(-721.387 76.416)"/><g class="f" transform="matrix(1, 0, 0, 1, -1.27, -5.2)"><path class="d" d="M742.945-40.472a9.768,9.768,0,0,1,.1-9.781c1.415-2.519,3.575-4.022,5.779-4.022a4.8,4.8,0,0,1,.72.055,5.641,5.641,0,0,0,.836.078h.031c.034-.283.05-.685.065-1a13.315,13.315,0,0,1,.625-3.91c-7.711.818-.96-7-7.812-13.6-7.049-6.792-18.614-3.386-20.644,2.368-3.723,6.431,1.693,16.921,5.076,22.674,3.292,5.8,10.523,8.854,17.266,9.272A7.418,7.418,0,0,1,742.945-40.472ZM742.7-61.321a28.22,28.22,0,0,0,2.835,3.165,10.733,10.733,0,0,0,1.467,1.2,6.58,6.58,0,0,0,1.958.883,2.833,2.833,0,0,1-2.5.623,4.72,4.72,0,0,1-2.332-1.213,5.351,5.351,0,0,1-1.376-2.145A3.767,3.767,0,0,1,742.7-61.321Zm-12.756,8.166a10.519,10.519,0,0,1-2.978-4.354,15.361,15.361,0,0,1-.942-5.058,10.752,10.752,0,0,1,.9-4.99,31.617,31.617,0,0,0,.68,4.814,28.964,28.964,0,0,0,1.275,4.513,13.661,13.661,0,0,0,2.184,3.934,9.781,9.781,0,0,0,3.765,2.734A6.624,6.624,0,0,1,729.939-53.155Z" transform="translate(-720.12 81.62)"/></g></g><g data-type="innerShadowGroup"><path class="c" d="M730.961-49.3c3.175,5.214,16.513,4.307,20.634-2.947,2.267-3.856,5.9-10.884,3.4-15.194-1.361-3.853-9.111-6.135-13.831-1.586s.226,9.978-5.671,9.069C731.995-60.5,728.149-53.926,730.961-49.3ZM746.833-54.9a6.582,6.582,0,0,0,2.523-1.833,9.218,9.218,0,0,0,1.462-2.635,19.149,19.149,0,0,0,.854-3.025,21.16,21.16,0,0,0,.457-3.225,7.217,7.217,0,0,1,.6,3.344,10.258,10.258,0,0,1-.634,3.388,7.012,7.012,0,0,1-1.994,2.918A4.45,4.45,0,0,1,746.833-54.9Zm-9.467-3.025a4.407,4.407,0,0,0,1.311-.592,7.066,7.066,0,0,0,.984-.8,19.144,19.144,0,0,0,1.9-2.119,2.528,2.528,0,0,1-.039,1.683,3.593,3.593,0,0,1-.922,1.436,3.156,3.156,0,0,1-1.56.813A1.9,1.9,0,0,1,737.366-57.922Z" transform="translate(-707.654 84.184)"/><g class="e" transform="matrix(1, 0, 0, 1, -1.27, -5.2)"><path class="d" d="M730.961-49.3c3.175,5.214,16.513,4.307,20.634-2.947,2.267-3.856,5.9-10.884,3.4-15.194-1.361-3.853-9.111-6.135-13.831-1.586s.226,9.978-5.671,9.069C731.995-60.5,728.149-53.926,730.961-49.3ZM746.833-54.9a6.582,6.582,0,0,0,2.523-1.833,9.218,9.218,0,0,0,1.462-2.635,19.149,19.149,0,0,0,.854-3.025,21.16,21.16,0,0,0,.457-3.225,7.217,7.217,0,0,1,.6,3.344,10.258,10.258,0,0,1-.634,3.388,7.012,7.012,0,0,1-1.994,2.918A4.45,4.45,0,0,1,746.833-54.9Zm-9.467-3.025a4.407,4.407,0,0,0,1.311-.592,7.066,7.066,0,0,0,.984-.8,19.144,19.144,0,0,0,1.9-2.119,2.528,2.528,0,0,1-.039,1.683,3.593,3.593,0,0,1-.922,1.436,3.156,3.156,0,0,1-1.56.813A1.9,1.9,0,0,1,737.366-57.922Z" transform="translate(-706.38 89.39)"/></g></g></g></g></svg>