<!--
 * @Author: czf <EMAIL>
 * @Date: 2024-10-30 15:08:17
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-12-06 17:28:59
 * @FilePath: \management-platform\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";

const appStore = useAppStore();
</script>

<template>
  <el-config-provider :locale="appStore.locale">
    <!-- :size="appStore.size" -->
    <router-view />
  </el-config-provider>
</template>
