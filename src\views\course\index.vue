<template>
  <div class="course-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.cat_id"
            placeholder="请选择分类"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in cat_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.sec_id"
            placeholder="请选择栏目"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in sec_options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增课程
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="ID" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="名称" align="center" min-width="140">
          <template #default="scope">
            <div class="cover-name">
              <img :src="scope.row.thumb" alt="" class="cover-img" />
              <!-- + '?x-image-process=style/style-270' -->
              <span>{{ scope.row.title }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="单节位置展示"
          align="center"
          prop="mobile"
          min-width="60"
        >
          <template #default="scope">
            {{ scope.row.mobile.substring(0, 4) }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="课程介绍"
          align="center"
          prop="status"
          width="120"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.short_desc }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          label="关联导师"
          align="center"
          prop="createTime"
          min-width="60"
        >
          <template #default="scope">
            {{ scope.row.createTime }}
          </template>
        </el-table-column> -->
        <el-table-column
          label="所属分类"
          align="center"
          min-width="60"
          show-overflow-tooltip
        >
          <template #default="scope">
            <!-- {{ scope.row.categories?.[0]?.name }} -->
            {{ filterCategoriesName(scope.row.categories) }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属栏目"
          align="center"
          min-width="60"
          show-overflow-tooltip
        >
          <template #default="scope">
            <!-- {{ scope.row.sections?.[0]?.name }} -->
            {{ filterSectionsName(scope.row.sections) }}
          </template>
        </el-table-column>
        <el-table-column label="价格" align="center" min-width="70">
          <template #default="scope">
            <div class="price">
              <span
                class="origin-price"
                :class="{ 'price-line': scope.row.discount || scope.row.free }"
              >
                ￥ {{ scope.row.price / 100 }} / {{ scope.row.bean_price }}绿豆
              </span>
              <span class="discount-price" v-if="scope.row.discount">
                ￥ {{ scope.row.discount.discount_price / 100 }} /
                {{ scope.row.discount.discount_bean }}绿豆
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="优惠活动" align="center" min-width="50">
          <template #default="scope">
            {{
              scope.row.discount
                ? "限时优惠"
                : scope.row.free
                ? "限时免费"
                : "--"
            }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="50">
          <template #default="scope">
            <!-- 10-编辑中，20-上架，30-下架，40-删除 -->
            <el-tag :type="statusMap[scope.row.status]?.type">
              {{ statusMap[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="学习人数" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.watched_count }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="上架时间" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.published_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-dropdown">
              <el-dropdown
                class="custom-el-dropdown"
                trigger="click"
                @command="
                  (command) => handleOperationChange(command, scope.row)
                "
              >
                <div class="dropdown-btn">
                  请选择操作
                  <div class="icon-down">
                    <i-ep-caret-bottom />
                  </div>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-if="scope.row.status !== 40"
                      command="edit"
                    >
                      修改
                    </el-dropdown-item>

                    <el-dropdown-item
                      v-if="scope.row.status !== 40 && enableList"
                      command="shelf"
                    >
                      {{
                        scope.row.status == 10 || scope.row.status == 30
                          ? "上架"
                          : scope.row.status == 20
                          ? "下架"
                          : "--"
                      }}
                    </el-dropdown-item>
                    <el-dropdown-item v-if="enableDelete" command="delete">
                      删除
                    </el-dropdown-item>
                    <el-dropdown-item command="price">
                      价格管理
                    </el-dropdown-item>
                    <el-dropdown-item command="courseData">
                      课程数据
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <!-- <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
                v-if="scope.row.status != 40"
              >
                修改
              </div>
              <div
                class="btn"
                v-if="scope.row.status != 40 && enableList"
                :class="[
                  scope.row.status == 10 || scope.row.status == 30
                    ? 'light-green-btn'
                    : 'info-btn',
                ]"
                @click="onRowClick('shelf', scope.row)"
              >
                {{
                  scope.row.status == 10 || scope.row.status == 30
                    ? "上架"
                    : scope.row.status == 20
                    ? "下架"
                    : "--"
                }}
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
                v-show="enableDelete"
                :style="{
                  pointerEvents: enableDelete ? 'auto' : 'none',
                  opacity: enableDelete ? '1' : '0.5',
                }"
              >
                删除
              </div>
            </div> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 侧边抽屉控件 -->
    <div class="drawer-container">
      <el-drawer
        class="price-drawer"
        :title="priceDrawer.title"
        v-model="priceDrawer.visible"
        :width="priceDrawer.width"
        :direction="'rtl'"
        :before-close="closeDrawer"
        :with-header="false"
        :modal="false"
        :append-to-body="false"
        :wrapperClosable="true"
      >
        <div class="drawer-header">
          <div class="course-title">
            {{ courseDetail.title }}
          </div>
          <div class="course-img">
            <img :src="courseDetail.thumb" alt="" />
          </div>
        </div>
        <div class="drawer-content">
          <div class="input-block">
            <div class="text">课程价格</div>
            <div class="input-value">
              <el-input
                v-model="priceFormData.bean_price"
                size="large"
                placeholder="请输入课程价格"
              >
                <template #append>
                  <svg-icon icon-class="lvdou" />
                </template>
              </el-input>
            </div>
          </div>
          <div class="component-tab">
            <el-tabs
              v-model="activeTabName"
              @tab-change="handleDrawerTabChange"
            >
              <el-tab-pane
                v-for="(item, index) in drawerTabList"
                :key="index"
                :label="item.label"
                :name="item.value"
              />
            </el-tabs>
          </div>
          <div class="tab-content">
            <div class="setting-tab" v-if="activeTabName == 'setting'">
              <div
                class="setting-discount"
                :class="{
                  'active-setting-discount': priceFormData.is_discount,
                }"
              >
                <div class="setting-title">
                  {{ priceFormData.is_discount ? "开启" : "是否" }}优惠
                </div>
                <div class="setting-switch custom-el-switch">
                  <el-switch v-model="priceFormData.is_discount" size="large" />
                </div>
              </div>
              <div v-if="priceFormData.is_discount" class="setting-list">
                <div class="discount-radio custom-el-radio">
                  <el-radio-group
                    v-model="priceFormData.discount_type"
                    @change="handleDiscountTypeChange"
                  >
                    <el-radio :value="'free'">限时免费</el-radio>
                    <el-radio :value="'discount'">限时优惠</el-radio>
                  </el-radio-group>
                </div>
                <div
                  class="discount-item"
                  v-if="priceFormData.discount_type == 'free'"
                >
                  <div class="input-block">
                    <div class="text">免费时间</div>
                    <div class="input-value">
                      <el-date-picker
                        v-model="priceFormData.dateTimeRange"
                        type="daterange"
                        size="large"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="discount-item"
                  v-if="priceFormData.discount_type == 'discount'"
                >
                  <div class="input-block">
                    <div class="text">优惠价格</div>
                    <div class="input-value">
                      <el-input
                        v-model="priceFormData.discount_bean"
                        size="large"
                        placeholder="请输入优惠价格"
                      >
                        <template #append>
                          <svg-icon icon-class="lvdou" />
                        </template>
                      </el-input>
                    </div>
                  </div>
                  <div class="input-block">
                    <div class="text">优惠时间</div>
                    <div class="input-value">
                      <el-date-picker
                        v-model="priceFormData.dateTimeRange"
                        type="daterange"
                        size="large"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="log-tab" v-if="activeTabName == 'log'">
              <div class="log-table">
                <el-table
                  :data="priceFormData.log"
                  height="100%"
                  border
                  fit
                  highlight-current-row
                >
                  <el-table-column
                    label="生效时间"
                    align="center"
                    min-width="100"
                  >
                    <template #default="scope">
                      {{ parseTime(scope.row.created_at, "{y}-{m}-{d}") }}
                    </template>
                  </el-table-column>
                  <el-table-column label="类型" align="center" min-width="50">
                    <template #default="scope">
                      {{ scope.row.type }}
                    </template>
                  </el-table-column>
                  <el-table-column label="价格" align="center" min-width="50">
                    <template #default="scope">
                      {{ scope.row.price }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
        <div class="drawer-footer">
          <div class="btn cancel-btn" @click="closeDrawer">取 消</div>
          <div class="btn primary-btn" @click="handleSubmitDrawer">确 定</div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  getCategories,
  getCourses,
  getCoursesDetail,
  deleteCourses,
  updateCourses,
  updateFreeCourses,
  addFreeCourses, //配置限时免费
  getDiscountCourse,
  deleteFreeCourses,
  createDiscountCourse, //配置限时优惠
  updateDiscountCourse,
  deleteDiscountCourse,
  getCoursePricing,
  updateCoursePricing,
} from "@/api/course";
import { getSections } from "@/api/web-page";
import { parseTime, resetReactiveObject } from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import { promises } from "dns";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Course",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const sortOptions = reactive<any>([]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  cat_id: "",
  sec_id: "",
});
const sec_options = ref<any>([]);
const cat_options = ref<any>([]);
// const dateTimeRange = ref("");
const total = ref<any>(0); // 数据总数
const tableData = ref([]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "编辑中", color: "#409eff" },
  20: { type: "success", label: "上架", color: "#2ab7b0" },
  30: { type: "info", label: "下架", color: "#909399" },
  40: { type: "danger", label: "删除", color: "#f56c6c" },
  50: { type: "warning", label: "过期", color: "#e6a23c" },
  // add more status mappings as needed
});

const operationOptions = ref<any>([
  {
    label: "编辑",
    value: "edit",
    visible: true,
  },
]);

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 0);
});
const enableList = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 5);
});

const courseDetail = reactive<any>({});
// 侧边抽屉对象
const priceDrawer = reactive({
  visible: false,
  type: "price-drawer",
  width: "30%",
  title: "价格管理",
});
const activeTabName = ref<any>("setting");
const drawerTabList = ref<any>([
  { label: "配置", value: "setting" },
  // { label: "变更记录", value: "log" },
]);
// 侧边抽屉表单数据
const priceFormData = reactive<any>({
  discount_type: "free",
  dateTimeRange: [],
  course_id: "",
  status: 10, //第一次创建就是10，初始化
  discount_bean: 0, //优惠后的绿豆
  // discount_price:'',//优惠后的价格
  // begin_at: "",
  // end_at: "",
});
const discountCourseId = ref<any>(""); //优惠课程id
const discountConfigs = ref<any>([]);
const freeCourseId = ref<any>(""); //免费课程id
const freeConfigs = ref<any>([]);

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getSearchOptions();
  getData();
});

//课程分类、栏目显示
function filterCategoriesName(value: any) {
  let res = "--";
  res = value?.map((item: any) => item.name).join(",");
  return res;
}
function filterSectionsName(value: any) {
  let res = "--";
  res = value?.map((item: any) => item.name).join(",");
  return res;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function getSearchOptions() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getCategories(params).then((res: any) => {
    cat_options.value = res.data.categories.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
  getSections(params).then((res: any) => {
    sec_options.value = res.data.sections.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
}
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    cat_id: queryParams.cat_id || undefined,
    sec_id: queryParams.sec_id || undefined,
  };
  getCourses(params)
    .then((res: any) => {
      tableData.value = res.data.courses.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.published_at = parseTime(
          item.published_at,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
        return item;
      });
      total.value = res.total;
      loading.value = false;
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleCreate() {
  router.push({ path: "course-action", query: { type: "create" } });
}

function handleOperationChange(command: string, row: any) {
  switch (command) {
    case "edit":
      onRowClick("edit", row);
      break;
    case "detail":
      onRowClick("detail", row);
      break;
    case "shelf":
      onRowClick("shelf", row);
      break;
    case "delete":
      onRowClick("delete", row);
      break;
    case "price":
      onRowClick("price", row);
      break;
    case "courseData":
      onRowClick("courseData", row);
      break;
    default:
      console.warn(`未知的操作类型: ${command}`);
  }
}
function onRowClick(type: string, row: any) {
  if (type == "detail") {
    router.push({ path: "course-action", query: { type, id: row.id } });
  }
  if (type == "edit") {
    router.push({ path: "course-action", query: { type, id: row.id } });
  }
  if (type == "recommend") {
  }
  if (type == "add") {
    router.push({ path: "course-action", query: { type, id: row.id } });
  }
  if (type == "delete") {
    handelDelete(row);
  }
  if (type == "shelf") {
    let text = "上架";
    if (row.status == 20) {
      text = "下架";
    }
    ElMessageBox.confirm("此操作将" + text + "该课程，是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      const status =
        row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
      const message =
        row.status == 10 || row.status == 30
          ? "上架成功"
          : row.status == 20
          ? "下架成功"
          : "";
      updateCourses(row.id, { status }).then((res) => {
        if (res.status == 200) {
          ElMessage.success(message);
          getData();
        }
      });
    });
  }
  if (type == "price") {
    handlePriceManage(row);
  }
  if (type == "courseData") {
    router.push({ path: "course-data", query: { id: row.id } });
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除该课程，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteCourses(row.id).then((res: any) => {
      if (res.code === 200) {
        ElMessage.success({
          message: "删除成功!",
        });
      }
      getData();
    });
  });
}

function handlePriceManage(row: any = null) {
  priceDrawer.visible = true;
  Object.assign(courseDetail, row);

  getCoursePricing(row.id).then((res: any) => {
    if (res.status === 200) {
      const priceConfig: any = res.data.course;
      setActivityConfigs(priceConfig);
      setDiscountTypeAndDateRange();
      priceDrawer.type =
        !discountCourseId.value && !freeCourseId.value ? "create" : "update";
      handleDiscountTypeChange();
    }
  });
}

// 设置活动配置
function setActivityConfigs(priceConfig: any) {
  discountConfigs.value = priceConfig?.discount_configs || [];
  freeConfigs.value = priceConfig?.free_configs || [];
  priceFormData.bean_price = priceConfig.bean_price || 0;
  priceFormData.is_discount =
    discountConfigs.value.length > 0 || freeConfigs.value.length > 0;
  priceFormData.discount_bean =
    discountConfigs.value.length > 0
      ? discountConfigs.value[0].discount_bean
      : 0;
  discountCourseId.value =
    discountConfigs.value.length > 0 ? discountConfigs.value[0].id : null;
  freeCourseId.value =
    freeConfigs.value.length > 0 ? freeConfigs.value[0].id : null;
}

// 设置优惠类型和时间范围
function setDiscountTypeAndDateRange() {
  if (freeCourseId.value || courseDetail.free) {
    priceFormData.discount_type = "free";
    const { from, till } = freeConfigs.value[0];
    setDateTimeRange(from, till);
  } else if (discountCourseId.value || courseDetail.discount) {
    priceFormData.discount_type = "discount";
    const { begin_at, end_at } = discountConfigs.value[0];
    setDateTimeRange(begin_at, end_at);
  } else {
    priceFormData.discount_type = "free";
    priceFormData.dateTimeRange = [];
  }
}

// 设置时间范围
function setDateTimeRange(start: string, end: string) {
  priceFormData.dateTimeRange[0] = new Date(start);
  priceFormData.dateTimeRange[1] = new Date(end);
}

// 触发优惠类型变更
function handleDiscountTypeChange() {
  priceFormData.dateTimeRange = [];
  // 根据优惠类型设置时间范围
  switch (priceFormData.discount_type) {
    case "free":
      if (freeConfigs.value.length > 0) {
        const { from, till } = freeConfigs.value[0];
        setDateTimeRange(from, till);
      }
      break;

    case "discount":
      if (discountConfigs.value.length > 0) {
        const { begin_at, end_at } = discountConfigs.value[0];
        setDateTimeRange(begin_at, end_at);
      }
      break;

    default:
      priceFormData.dateTimeRange = [];
      break;
  }
}
function handleDrawerTabChange(tab: any) {}
function closeDrawer() {
  priceDrawer.visible = false;
  resetReactiveObject(courseDetail);
  resetReactiveObject(priceFormData);
  discountConfigs.value = [];
  freeConfigs.value = [];
  discountCourseId.value = "";
  freeCourseId.value = "";
  priceFormData.is_discount = false;
  priceFormData.discount_bean = 0;
  priceFormData.discount_price = 0;
  priceFormData.discount_type = "free";
  priceFormData.dateTimeRange = [];
}
async function handleSubmitDrawer() {
  const data: any = {
    course_id: courseDetail.id,
    status: 20, // 10-初始化，20-启用，30-停用
    discount_bean: priceFormData.discount_bean * 1,
    discount_price: priceFormData.discount_bean * 10, // 单位为分
  };

  if (!priceFormData.bean_price) {
    ElMessage.error("请输入课程价格");
    return;
  }

  try {
    // 更新课程价格
    await updateCoursePricing(courseDetail.id, {
      bean_price: priceFormData.bean_price * 1,
      price: priceFormData.bean_price * 10, // 单位为分
    });

    // 如果未开启优惠，删除相关活动
    if (!priceFormData.is_discount) {
      await handleCancelDiscountOrFree();
      closeDrawerAndRefresh();
      return;
    }

    // 检查时间范围
    if (
      !priceFormData.dateTimeRange ||
      priceFormData.dateTimeRange.length === 0
    ) {
      ElMessage.warning("请选择时间");
      return;
    }

    // 设置时间范围
    data.begin_at = parseTime(
      priceFormData.dateTimeRange[0],
      "{y}{m}{d}000000"
    );
    data.end_at = parseTime(priceFormData.dateTimeRange[1], "{y}{m}{d}235959");

    // 根据抽屉类型处理创建或更新逻辑
    if (priceDrawer.type === "create") {
      await handleCreateDiscountOrFree(data);
    } else if (priceDrawer.type === "update") {
      await handleUpdateDiscountOrFree(data);
    }

    closeDrawerAndRefresh();
  } catch (error) {
    console.log("提交失败", error);
  }
}

// 处理取消优惠或免费活动
async function handleCancelDiscountOrFree() {
  const promises: Promise<any>[] = [];
  if (discountCourseId.value) {
    promises.push(deleteDiscountCourse(discountCourseId.value));
  }
  if (freeCourseId.value) {
    promises.push(deleteFreeCourses(freeCourseId.value));
  }

  const results = await Promise.all(promises);
  results.forEach((res, index) => {
    if (res.status === 200) {
      ElMessage.success(
        index === 0 ? "取消限时优惠成功!" : "取消限时免费成功!"
      );
    }
  });
}

// 处理创建优惠或免费活动
async function handleCreateDiscountOrFree(data: any) {
  const params = {
    course_id: courseDetail.id,
    status: 20,
    time_range: {
      from: data.begin_at,
      till: data.end_at,
    },
  };

  if (priceFormData.discount_type === "discount") {
    if (!data.discount_bean) {
      ElMessage.warning("优惠价格不能为0");
      throw new Error("优惠价格不能为0");
    }
    if (data.discount_bean >= priceFormData.bean_price) {
      ElMessage.warning("优惠价格只能小于课程价格");
      throw new Error("优惠价格只能小于课程价格");
    }
    await createDiscountCourse(data);
    ElMessage.success("创建限时优惠成功!");
  } else if (priceFormData.discount_type === "free") {
    await addFreeCourses(params);
    ElMessage.success("创建限时免费成功!");
  }
}

// 处理更新优惠或免费活动
async function handleUpdateDiscountOrFree(data: any) {
  const params = {
    course_id: courseDetail.id,
    status: 20,
    time_range: {
      from: data.begin_at,
      till: data.end_at,
    },
  };

  if (priceFormData.discount_type === "discount") {
    if (!data.discount_bean) {
      ElMessage.warning("优惠价格不能为0");
      throw new Error("优惠价格不能为0");
    }
    if (data.discount_bean >= priceFormData.bean_price) {
      ElMessage.warning("优惠价格只能小于课程价格");
      throw new Error("优惠价格只能小于课程价格");
    }
    if (freeCourseId.value) {
      await deleteFreeCourses(freeCourseId.value);
    }
    if (discountCourseId.value) {
      await updateDiscountCourse(discountCourseId.value, data);
    } else {
      await createDiscountCourse(data);
    }
    ElMessage.success("更新限时优惠成功!");
  } else if (priceFormData.discount_type === "free") {
    if (discountCourseId.value) {
      await deleteDiscountCourse(discountCourseId.value);
    }
    if (freeCourseId.value) {
      await updateFreeCourses(freeCourseId.value, params);
    } else {
      await addFreeCourses(params);
    }
    ElMessage.success("更新限时免费成功!");
  }
}

// 关闭抽屉并刷新数据
function closeDrawerAndRefresh() {
  closeDrawer();
  getData();
}
</script>

<style scoped lang="scss">
.course-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      display: flex;
      align-items: center;

      .cover-img {
        // width: 150px !important;
        // height: 100px !important;
        width: 162px !important;
        height: 86px !important;
        object-fit: cover;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }

    .price,
    .origin-price {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .price-line {
      font-size: 14px;
      color: #becec4;
    }

    .price-line::after {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 1px;
      content: "";
      background: currentcolor;
      transform: translateY(-50%);
    }
    // .discount-price {
    // }
    .option-dropdown {
      display: flex;
      align-items: center;
      justify-content: center;

      .dropdown-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 146px;
        height: 44px;
        cursor: pointer;
        border: 1px solid #c0c7d6;
        border-radius: 8px;
        box-shadow: inset 0 0 8px 1px rgb(136 159 155 / 69%);

        .icon-down {
          margin-left: 15px;

          i {
            width: 24px !important;
          }

          svg {
            font-size: 18px !important;
            color: #00918c !important;
          }
        }
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.drawer-container {
  position: relative;
  height: 100%;
  overflow: hidden;

  :deep(.el-drawer__body) {
    position: relative !important;
  }
}

.price-drawer {
  position: absolute !important;
  top: 0;
  right: 0;
  z-index: 10;
  height: 100%;

  .drawer-header {
    display: flex;
    flex-direction: column;
    // padding: 20px;

    .course-title {
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: 500;
      color: #3b4664;
    }

    .course-img {
      width: 243px;
      height: 129px;
      border-radius: 8px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
      }
    }
  }

  .drawer-content {
    padding-top: 20px;

    .input-block {
      padding: 10px 0;

      .text {
        margin-bottom: 10px;
        font-size: 17px;
        font-weight: 400;
        color: #3b4664;
      }

      .input-value {
        :deep(.el-input__wrapper) {
          border: 1px solid #edeff4 !important;
          border-right: none !important;
          border-radius: 8px 0 0 8px !important;
          box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%) !important;

          .el-input__inner {
            font-size: 20px !important;
            font-weight: 500 !important;
            color: #00918c !important;

            &::placeholder {
              font-size: 16px !important;
              font-weight: 400 !important;
            }
          }
        }

        :deep(.el-date-editor) {
          width: 100% !important;

          .el-range-input {
            width: 100% !important;
            font-size: 20px !important;
            font-weight: 500 !important;
            color: #00918c !important;

            &::placeholder {
              font-size: 16px !important;
              font-weight: 400 !important;
            }
          }
        }

        :deep(.el-input-group__append) {
          background: #f5f5f513 !important;
          border: 1px solid #edeff4 !important;
          border-left: none !important;
          border-radius: 0 8px 8px 0 !important;
          box-shadow: inset 0 3px 6px 1px rgb(188 198 214 / 14%) !important;

          svg {
            font-size: 28px !important;
          }
        }
      }
    }

    .component-tab {
      padding: 15px 0;
    }

    .tab-content {
      width: 100%;

      .setting-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .setting-discount {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          padding: 5px 20px;
          font-size: 17px;
          font-weight: 400;
          color: #3b4664;
          border: 1px solid #edeff4;
          border-radius: 8px;
          box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);
        }

        .active-setting-discount {
          font-weight: 500;
          color: #00918c;
          background: linear-gradient(180deg, #cdddd6 0%, #becec4 100%);
          border: 1px solid #edeff4;
          box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);
        }

        .setting-list {
          display: flex;
          // align-items: center;
          flex-direction: column;
          width: 100%;
          padding: 20px 0;

          .discount-radio {
            margin-bottom: 20px;
          }
        }
      }

      .log-tab {
        width: 100%;

        .log-table {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 22rem;
        }
      }
    }
  }

  .drawer-footer {
    position: absolute;
    right: 40px;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding: 20px;

    .btn {
      width: 96px;
      height: 42px;
      border-radius: 10px;

      &:first-child {
        margin-right: 20px;
      }
    }
  }
}
</style>
