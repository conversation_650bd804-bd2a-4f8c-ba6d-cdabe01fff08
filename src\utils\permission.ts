/**
 * 将权限字符串转换为权限数组
 * @param permissionsString
 * @returns {*[]}
 */
export function permissionsS2A(permissionsString: any) {
  const result: any = [];
  permissionsString = permissionsString || "";
  permissionsString.split("").forEach((permission: any, idx: any) => {
    if (+permission) {
      result.push(idx);
    }
  });

  return result;
}

/**
 * 将权限数组转换为权限字符串
 * @param permissionsArray
 * @returns {string}
 */
export function permissionsA2S(permissionsArray: any) {
  const result = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
  permissionsArray.forEach((permission: any) => {
    result[permission] = 1;
  });
  return result.reduce((prev, curr) => prev + curr, "");
}
