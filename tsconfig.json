{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "noLib": false,
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "allowJs": true,
    "paths": {
      "@/*": ["src/*"]
    },
    "types": ["vite/client", "unplugin-icons/types/vue", "element-plus/global"],
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "allowSyntheticDefaultImports": true /* 允许默认导入 */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,

    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment",
    "noImplicitAny": false,
    "outDir": "dist" /* 优化-待处理TODO: */
  },

  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "src/typings/**/*.d.ts",
    "mock/**/*.ts",
    "vite.config.ts",
    "src/**/*.js"
  ],
  "exclude": ["node_modules", "dist"] //, "**/*.js"
}
