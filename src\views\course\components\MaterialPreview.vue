<template>
  <div class="material-preview-container">
    <div class="content">
      <div
        id="course-video"
        class="course-video"
        style="padding-top: 0"
        v-if="resInfo.res_type == 10"
      ></div>
      <div id="picture" class="picture" v-else-if="resInfo.res_type == 20">
        <img :src="resInfo.url" alt="" />
      </div>
    </div>
    <div class="footer">
      <div class="title">描述</div>
      <div class="block">
        <div class="left">
          <div class="text">
            类型：{{ typeMap[resInfo.res_type]?.label || typeMap[type]?.label }}
          </div>
          <div class="text">创建时间：{{ resInfo.created_at }}</div>
        </div>
        <div class="right">
          <div class="text">大小：{{ resInfo.size }} Mb</div>
          <div class="text" v-if="resInfo.res_type == 10">
            时长：{{ resInfo.duration }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { getEntResourcesDetail } from "@/api/enterprise";
// import MyPlugin from "my-xgplayer-plugin";
import "xgplayer/dist/index.min.css";
import LivePreset from "xgplayer/es/presets/live";
import Player from "xgplayer";
import HlsPlugin from "xgplayer-hls";
import { parseTime, secondsToMinutes } from "@/utils";

import payIcon from "@/assets/icons/play-video.svg";
import pauseIcon from "@/assets/icons/pause-video.svg";
import startPlayIcon from "@/assets/icons/start-play.svg";
import getFullScreenIcon from "@/assets/icons/getFullScreen.svg";
import exitFullscreenIcon from "@/assets/icons/exitFullScreen.svg";
import volumeIcon from "@/assets/icons/volume.svg";
import volumeMuteIcon from "@/assets/icons/volumeMuted.svg";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MaterialPreview",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  id: {
    type: String,
    default: "",
  },
  previewResInfo: {
    type: Object,
    default: () => {},
  },
  type: {
    type: Number,
    default: 0,
  },
  visible: {
    type: Boolean,
    default: false,
  },
});

const type: any = computed(() => props.previewResInfo.res_type);
const player = ref<any>(null);
const typeMap = reactive<any>({
  10: { value: 10, label: "视频" },
  20: { value: 20, label: "图片" },
  30: { value: 30, label: "文档" },
  40: { value: 40, label: "音频" },
  50: { value: 50, label: "其他" },
  // add more status mappings as needed
});
const resourceReadyMap = reactive<any>({
  1080: { name: "超清", url: "" },
  720: { name: "高清", url: "" },
  480: { name: "标清", url: "" },
});
// const detailData = reactive<any>({});
const resInfo = props.previewResInfo;
const playerConfig = reactive<any>({
  id: "course-video",
  lang: "zh", //设置中文
  volume: 0, // 默认音量0~1
  autoplay: false, //自动播放
  url: "",
  // height: "100%",
  // width: "100%",
  pip: false, //是否支持画中画
  replay: false,
  poster: "",
  enableContextmenu: false, //播放器范围内禁止右键菜单
  allowSeekAfterEnded: true, ///支持进度条播放结束后拖动
  // seekedStatus: "pause", //拖动进度条后是否自动播放
  playbackRate: [0.5, 0.75, 1, 1.25, 1.5, 2], //传入倍速可选数组
  start: {
    isShowPause: true,
    isShowEnd: true,
  },
  //根据进度播放
  startTime: 0, //视频起播时间（单位：秒）
  commonStyle: {
    // 进度条底色
    progressColor: "",
    // 播放完成部分进度条底色
    playedColor: "#2ab7b0",
    // 缓存部分进度条底色
    cachedColor: "",
    // 进度条滑块样式
    sliderBtnStyle: {},
    // 音量颜色
    volumeColor: "#2ab7b0",
  },
  miniprogress: true,
  ignores: ["cssfullscreen"],
  icons: {
    play: payIcon,
    pause: pauseIcon,
    // startPause: startPlayIcon,
    // startPlay: startPlayIcon,
    fullscreen: getFullScreenIcon,
    exitFullscreen: exitFullscreenIcon,
    volumeLarge: volumeIcon,
    volumeSmall: volumeIcon,
    volumeMuted: volumeMuteIcon,
  },

  fluid: true, //填满屏幕 （流式布局）， 是否流式布局（宽高优先于流失布局，默认16:9）注掉上方宽高看效果
  fitVideoSize: "fixed", // 保持容器宽/高，不做适配，按照容器来
  videoFillMode: "auto",
  useScreenOrientation: true,
  marginControls: false, // 是否开启画面和控制栏分离模式，不开启空间多一些
});
const hlsConfig = reactive<any>({
  plugins: [HlsPlugin],
  isLive: false, //不是直播流
  hls: {
    retryCount: 3, // 重试 3 次，默认值
    retryDelay: 1000, // 每次重试间隔 1 秒，默认值
    loadTimeout: 10000, // 请求超时时间为 10 秒，默认值
    fetchOptions: {
      // 该参数会透传给 fetch，默认值为 undefined
      mode: "cors",
    },
    preloadTime: 30, // 指定允许的预加载buffer的最大长度(单位s) 默认值
  },
});
//视频url要拼接用户token的前16位反转字符串
const token = computed(() => {
  return userStore.token.slice(0, 16).split("").reverse().join("");
});
// onMounted(() => {
//   if (resInfo.res_type == 10) init();
// });
watch(
  () => props.visible,
  (val) => {
    // console.log("resInfo", resInfo);
    if (resInfo.res_type == 10 && val) {
      // init();
    } else if (!val) {
      player.value?.destroy();
    }
    // player.value.destroy();
  },
  { deep: true, immediate: true }
);
onBeforeUnmount(() => {
  if (resInfo.res_type == 10) player.value.destroy();
});

watch(
  () => resInfo,
  (val) => {
    //视频素材的渲染
    if (resInfo.res_type == 10 && props.visible) {
      const play_info = resInfo.play_info;
      if (play_info && play_info.length > 0) {
        play_info.forEach((item: any) => {
          if (resourceReadyMap[item.hight]) {
            resourceReadyMap[item.hight].url = item.url;
          }
        });
        // const url = play_info[0]?.url || "";
        // const poster = val ? resInfo.cover_info[0].cover_url : "";
        // playerConfig.url = url;
        const urlOriginal: any = play_info[0]?.url || "";
        const poster = val ? resInfo.cover_info[0].cover_url : "";
        playerConfig.url = urlOriginal + "?token=" + token.value;
        playerConfig.poster = poster;
        setTimeout(() => {
          videoConfigSetting();
        }, 100);
      } else {
        init();
      }
      // nextTick(() => {
      //   videoConfigSetting();
      // });
    }
    if (resInfo.res_type == 20 && props.visible) {
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
function init() {
  player.value = new Player({
    id: "course-video",
    lang: "zh", //设置中文
    volume: 0, // 默认静音
    autoplay: false, //自动播放
  });
}

function videoConfigSetting() {
  if (player.value) player.value.reset();
  // player.value = new Player(playerConfig);
  if (playerConfig.url.includes(".m3u8")) {
    player.value = new Player({ ...playerConfig, ...hlsConfig });
  } else player.value = new Player(playerConfig);
}
</script>

<style scoped lang="scss">
.material-preview-container {
  height: 40rem;
  padding: 20px;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .course-video {
      width: 100% !important;
      height: 100% !important;
      padding-top: 0 !important;
    }

    .picture {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100% !important;
      height: 100% !important;
      padding-top: 0 !important;

      img {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain;
      }
    }
  }

  .footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 180px;
    // align-items: center;
    margin-top: 15px;
    color: #3b4664;

    .title {
      font-size: 20px;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      text-align: left;
    }

    .block {
      display: flex;
      // align-items: center;
      justify-content: space-between;
      width: 439px;
      height: 120px;
      padding: 15px 20px 0;
      font-size: 15px;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 400;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);

      .text {
        padding: 5px;
      }
    }
  }
}
</style>
<style lang="scss">
.xgplayer {
  svg path {
    // linear-gradient(180deg, #33cec9 0%, #1c8d84 100%) !important;
    fill: #2ab7b0 !important;
  }

  .xgplayer-loading {
    svg path {
      fill: none !important;
    }
  }

  //进度条圆点颜色
  .xgplayer-progress-btn {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;

    &::before {
      background: radial-gradient(
        ellipse closest-side,
        #b5faf2 0%,
        #27aca5 100%
      ) !important;
      box-shadow: 0 2px 4px 1px rgb(12 239 91 / 50%) !important;
    }
  }

  //进度条颜色
  .xgplayer-progress-played {
    background: linear-gradient(
      270deg,
      #27aca5 0%,
      rgb(54 185 150 / 2%) 100%
    ) !important;
  }
  // .xgplayer-progress-point {

  // }

  .xgplayer-start {
    transform: translate(-50%, -50%) scale(1.5) !important;

    .xg-icon-play,
    .xg-icon-pause {
      background: linear-gradient(
        180deg,
        #fff 0%,
        rgb(255 255 255 / 48%) 100%
      ) !important;
      box-shadow: 0 3px 20px 1px rgb(18 172 136 / 65%),
        inset 0 3px 12px 1px rgb(7 83 69 / 41%) !important;
      // background: #fff !important;
      // box-shadow: 0 3px 20px 1px rgb(18 172 136 / 65%),
      //   inset 0 3px 12px 1px rgb(13 168 138 / 48%) !important;
    }
  }

  .xgplayer-play {
    .xgplayer-icon {
      .xg-icon-play,
      .xg-icon-pause {
        transform: scale(0.8) !important;
      }
    }
  }

  .xg-left-grid {
    span {
      color: #2ab7b0 !important;
    }
  }

  .xg-options-list {
    .option-item {
      &:hover {
        color: #2ab7b0 !important;
      }
    }

    .selected {
      color: #2ab7b0 !important;
    }
  }

  .btn-text {
    span {
      color: #000 !important;
      background: #fff !important;
      border-radius: 10px !important;
      box-shadow: inset 0 3px 6px 1px rgb(14 134 28 / 16%) !important;
    }
  }

  .xgplayer-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #283333 !important;

    .xg-right-grid {
      display: flex;
      align-items: center;

      .btn-text {
        .icon-text {
          font-size: 16px !important;
          font-weight: 500;
          color: #2ab7b0 !important;
          background: transparent !important;
        }
      }
    }
  }

  .xgplayer-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 89px;
    height: 28px;
    font-size: 13px;
    cursor: pointer;
    background: #fff;
    border-radius: 10px;
    box-shadow: inset 0 3px 6px 1px rgb(14 134 28 / 16%);

    .menu-text {
      font-size: 14px;
      color: #3d4040;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
