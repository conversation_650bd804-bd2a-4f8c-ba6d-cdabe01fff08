import request from "@/utils/request";
import { AxiosPromise } from "axios";

export function uploadFileApi(file: File) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/api/v1/files",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
//素材分组
export function getResGroups(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/res_groups",
    method: "get",
    params: queryParams,
  });
}
export function getResGroupsDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1res_groups/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param dat哦·                                       */
export function addResGroups(data: any) {
  return request({
    url: "/gkadmin/v1/res_groups",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateResGroups(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/res_groups/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteResGroups(ids: string) {
  return request({
    url: "/gkadmin/v1/res_groups/" + ids,
    method: "delete",
  });
}

//素材管理
export function getResources(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/resources",
    method: "get",
    params: queryParams,
  });
}
export function getResourcesDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/resources/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param dat哦·                                       */
export function addResources(data: any) {
  return request({
    url: "/gkadmin/v1/resources",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateResources(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/resources/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteResources(ids: string) {
  return request({
    url: "/gkadmin/v1/resources/" + ids,
    method: "delete",
  });
}
