import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * 获取验证码
 */
export function getVerifyCodes(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/verify_codes",
    method: "post",
    params: queryParams,
  });
}

/**
 * 登录
 */
export function login(id: number) {
  return request({
    url: "gkadmin/v1/sessons",
    method: "post",
  });
}

// 获取管理员列表
export function getSysUsers(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/sys_users",
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增管理员
 *
 * @param data
 */
export function addSysUsers(data: any) {
  return request({
    url: "/gkadmin/v1/sys_users",
    method: "post",
    data: data,
  });
}

/**
 *  修改管理员
 *
 * @param id
 * @param data
 */
export function updateSysUsers(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/sys_users/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除管理员
 *
 * @param ids
 */
export function deleteSysUsers(ids: string) {
  return request({
    url: "/gkadmin/v1/sys_users/" + ids,
    method: "delete",
  });
}

// 查询操作日志
export function getSysUsersLogs(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/sys_user_logs",
    method: "get",
    params: queryParams,
  });
}

// 获取角色列表
export function getSysRoles(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/sys_roles",
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增角色
 *
 * @param data
 */
export function addSysRoles(data: any) {
  return request({
    url: "/gkadmin/v1/sys_roles",
    method: "post",
    data: data,
  });
}

/**
 *  修改角色
 *
 * @param id
 * @param data
 */
export function updateSysRoles(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/sys_roles/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除角色
 *
 * @param ids
 */
export function deleteSysRoles(ids: string) {
  return request({
    url: "/gkadmin/v1/sys_roles/" + ids,
    method: "delete",
  });
}

// 获取目录列表
export function getSysMenus(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/sys_menus",
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增目录
 *
 * @param data
 */
export function addSysMenus(data: any) {
  return request({
    url: "/gkadmin/v1/sys_menus",
    method: "post",
    data: data,
  });
}

/**
 *  修改目录
 *
 * @param id
 * @param data
 */
export function updateSysMenus(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/sys_menus/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除目录
 *
 * @param ids
 */
export function deleteSysMenus(ids: string) {
  return request({
    url: "/gkadmin/v1/sys_menus/" + ids,
    method: "delete",
  });
}
