<template>
  <div class="admin-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增管理员
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <!-- <el-table-column label="ID" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column> -->
        <el-table-column label="序号" align="center" min-width="40">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="手机号码" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="120">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">
              {{ statusMap[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="角色" align="center" min-width="60">
          <template #default="scope">
            {{ scope.row.roles?.[0]?.name }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div class="btn delete-btn" @click="handelDelete(scope.row)">
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-scrollbar max-height="30rem" warp-style="overflow-x: hidden;">
          <el-form
            :model="formData"
            :rules="formDataRules"
            ref="formRef"
            label-width="6rem"
            :style="{
              pointerEvents: dialog.type == 'detail' ? 'none' : 'auto',
            }"
          >
            <el-form-item label="姓名" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入姓名"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input
                v-model="formData.mobile"
                placeholder="请输入手机号码"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="角色" prop="role_id">
              <el-select
                :suffix-icon="`CaretBottom`"
                size="large"
                v-model="formData.role_id"
                placeholder="请选择角色"
                filterable
                clearable
              >
                <el-option
                  v-for="item in roles"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="超级管理员">
              <el-switch v-model="formData.is_super" size="large" />
            </el-form-item> -->
            <el-form-item label="备注信息">
              <el-input
                v-model="formData.remark"
                placeholder="备注信息"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="状态" v-if="dialog.type == 'edit'">
              <el-select
                :suffix-icon="`CaretBottom`"
                size="large"
                v-model="formData.status"
                placeholder="请选择状态"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="dialog.type != 'detail'">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
        <div class="dialog-footer" v-if="dialog.type == 'detail'">
          <div class="btn cancel-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  getSysUsers,
  deleteSysUsers,
  addSysUsers,
  updateSysUsers,
  getSysRoles,
} from "@/api/system";
import { parseTime } from "@/utils";
import { validateMobile, validEmail } from "@/utils/validate";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Admin",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const userInfo = computed(() => {
  return userStore.userInfo;
});
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref([]);

const statusOptions = ref<any>([
  { value: 10, label: "正常" },
  { value: 20, label: "冻结" },
]);
const statusMap = reactive<any>({
  // 10: { type: "primary", label: "正常" },
  10: { type: "success", label: "正常" },
  20: { type: "info", label: "冻结" },
  // 30: { type: "danger", label: "删除" },
  // add more status mappings as needed
});

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "",
});

const roles = ref<any>([]);
// 表单数据
const rowId = ref();
const formData = reactive<any>({
  mobile: "",
  name: "",
  role_id: "",
  is_super: true,
  remark: "",
  status: "",
});
const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  mobile: [
    {
      required: true,
      trigger: "blur",
      validator: validateMobile,
    },
  ],
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  role_id: [
    { required: true, message: "请选择角色", trigger: ["change", "blur"] },
  ],
});
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getRoles();
  getData();
});

function getRoles() {
  const params = {
    page: 1,
    per_page: 999,
  };
  getSysRoles(params).then((res: any) => {
    roles.value = res.data.sys_roles.map((item: any) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
    // 判断是否是超级管理员，只有超级管理员才可以添加超级管理员的角色到用户
    if (userInfo.value.role !== "超级管理员") {
      roles.value = roles.value.filter((item: any) => item.value !== 4);
    }
  });
}
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getSysUsers(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.users.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}

function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增管理员";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  if (type === "edit") {
    rowId.value = row.id;
    dialog.visible = true;
    dialog.title = "修改管理员";
    dialog.type = "edit";
    Object.assign(formData, row);
    formData.is_super = row.is_super ? true : false;
    formData.role_id = row.roles[0].id;
  }
  if (type === "delete") {
    handelDelete(row);
  }
  if (type === "detail") {
    dialog.visible = true;
    dialog.title = "管理员详情";
    dialog.type = "detail";
    Object.assign(formData, row);
    formData.is_super = row.is_super ? true : false;
    formData.role_id = row.roles[0].id;
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除管理员，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteSysUsers(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closeDialog() {
  dialog.visible = false;
  formRef.value.clearValidate();
  Object.keys(formData).forEach((key) => {
    formData[key] = "";
  });
  // formData.is_super = true;
}
function handleSubmit() {
  const data: any = {
    mobile: formData.mobile,
    name: formData.name,
    role_id: formData.role_id,
    // is_super: formData.is_super,
    remark: formData.remark || undefined,
  };
  if (formData.role_id == 4) {
    data.is_super = true; //目前写死超级管理员，角色id为4
  }
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      if (dialog.type === "create") {
        data.status = 10;
        addSysUsers(data).then((res: any) => {
          if (res.status == 200) {
            ElMessage.success({
              message: "创建成功!",
            });
            closeDialog();
            getData();
          }
        });
      }
      if (dialog.type === "edit") {
        data.status = formData.status;
        updateSysUsers(rowId.value, data).then((res: any) => {
          if (res.status == 200) {
            ElMessage.success({
              message: "修改成功!",
            });
            closeDialog();
            getData();
          }
        });
      }
    }
  });
}
</script>

<style scoped lang="scss">
.admin-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
