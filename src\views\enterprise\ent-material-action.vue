<template>
  <div class="ent-material-action-container">
    <div class="container-header">
      <div class="left">素材详情--{{ typeMap[type].label }}</div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>

    <div class="content">
      <div
        id="course-video"
        class="course-video"
        style="padding-top: 0"
        v-if="type == 10"
      ></div>
      <div id="picture" class="picture" v-else-if="type == 20">
        <img :src="resInfo.url" alt="" />
      </div>
    </div>
    <div class="footer">
      <div class="title">描述</div>
      <div class="block">
        <div class="left">
          <div class="text">类型：{{ typeMap[type].label }}</div>
          <div class="text">所属企业：{{ detailData.enterprise?.name }}</div>
          <div class="text">创建时间：{{ detailData.created_at }}</div>
        </div>
        <div class="right">
          <div class="text">大小：{{ resInfo.size }} Mb</div>
          <div class="text" v-if="type == 10">时长：{{ resInfo.duration }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { getEntResourcesDetail } from "@/api/enterprise";
// import MyPlugin from "my-xgplayer-plugin";
import "xgplayer/dist/index.min.css";
import LivePreset from "xgplayer/es/presets/live";
import Player from "xgplayer";
import HlsPlugin from "xgplayer-hls";
import { parseTime, secondsToMinutes } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "EntMaterialAction",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
const type: any = route.query.type;
const id: any = route.query.id;
const player = ref<any>(null);
const typeMap = reactive<any>({
  10: { value: 10, label: "视频" },
  20: { value: 20, label: "图片" },
  30: { value: 30, label: "文档" },
  40: { value: 40, label: "音频" },
  50: { value: 50, label: "其他" },
  // add more status mappings as needed
});
const resourceReadyMap = reactive<any>({
  1080: { name: "超清", url: "" },
  720: { name: "高清", url: "" },
  480: { name: "标清", url: "" },
});
const detailData = reactive<any>({});
const resInfo = reactive<any>({});
const playerConfig = reactive<any>({
  id: "course-video",
  lang: "zh", //设置中文
  volume: 0, // 默认静音
  autoplay: false, //自动播放
  url: "",
  height: "100%",
  width: "100%",
  pip: true, //是否支持画中画
  poster: "",
  fluid: true, // 填满屏幕 （流式布局）
  playbackRate: [0.5, 0.75, 1, 1.5, 2], //传入倍速可选数组
  allowSeekPlayed: true, //支持进度条只能拖动到已播过部分（播放器被接管或劫持时无效，比如iOS或安卓部分环境下）
  enableContextmenu: false, //播放器范围内禁止右键菜单
  allowSeekAfterEnded: true, ///支持进度条播放结束后拖到
  // disableProgress: true, //禁止进度条拖动交互
  commonStyle: {
    // 进度条底色
    progressColor: "",
    // 播放完成部分进度条底色
    playedColor: "#0EB53B",
    // 缓存部分进度条底色
    cachedColor: "",
    // 进度条滑块样式
    sliderBtnStyle: {},
    // 音量颜色
    volumeColor: "#0EB53B",
  },
  // plugins: [HlsPlugin],
  // isLive: false, //不是直播流
  // hls: {
  //   retryCount: 3, // 重试 3 次，默认值
  //   retryDelay: 1000, // 每次重试间隔 1 秒，默认值
  //   loadTimeout: 10000, // 请求超时时间为 10 秒，默认值
  //   fetchOptions: {
  //     // 该参数会透传给 fetch，默认值为 undefined
  //     mode: "cors",
  //   },
  //   preloadTime: 30, // 指定允许的预加载buffer的最大长度(单位s) 默认值
  // },
});
const hlsConfig = reactive<any>({
  plugins: [HlsPlugin],
  isLive: false, //不是直播流
  hls: {
    retryCount: 3, // 重试 3 次，默认值
    retryDelay: 1000, // 每次重试间隔 1 秒，默认值
    loadTimeout: 10000, // 请求超时时间为 10 秒，默认值
    fetchOptions: {
      // 该参数会透传给 fetch，默认值为 undefined
      mode: "cors",
    },
    preloadTime: 30, // 指定允许的预加载buffer的最大长度(单位s) 默认值
  },
});
//视频url要拼接用户token的前16位反转字符串
const token = computed(() => {
  return userStore.token.slice(0, 16).split("").reverse().join("");
});
onMounted(() => {
  getData();
  if (type == 10) init();
});
onBeforeUnmount(() => {
  if (type == 10) player.value.destroy();
});

// watch(
//   () => detailData,
//   (val) => {
//     // 判断val显示什么
//     if (type == 10) {
//       init();
//     }
//   },
//   {
//     deep: true,
//     // immediate: true,
//   }
// );
watch(
  () => resInfo,
  (val) => {
    //视频素材的渲染
    if (type == 10) {
      const play_info = val.play_info;
      console.log("val", val);
      play_info.forEach((item: any) => {
        if (resourceReadyMap[item.hight]) {
          resourceReadyMap[item.hight].url = item.url;
        }
      });
      // const url = play_info[0]?.url || "";
      // const poster = val ? val.cover_info[0].cover_url : "";
      // playerConfig.url = url;
      const urlOriginal: any = play_info[0]?.url || "";
      const poster = val ? val.cover_info[0].cover_url : "";
      playerConfig.url = urlOriginal + "?token=" + token.value;
      playerConfig.poster = poster;
      nextTick(() => {
        videoConfigSetting();
      });
    }
    if (type == 20) {
    }
  },
  {
    deep: true,
    // immediate: true,
  }
);
function init() {
  player.value = new Player({
    id: "course-video",
    lang: "zh", //设置中文
    volume: 0, // 默认静音
    autoplay: false, //自动播放
  });
}

function getData() {
  loading.value = true;
  getEntResourcesDetail(id).then((res: any) => {
    if (res.status === 200) {
      Object.assign(detailData, res.data);
      detailData.created_at = parseTime(
        detailData.created_at,
        "{y}-{m}-{d} {h}:{i}:{s}"
      );

      Object.assign(resInfo, res.data.res_info);
      const size = resInfo.size || resInfo.video_size || 0;
      resInfo.size = (size / 1024 / 1024).toFixed(2);
      if (type == 10) {
        resInfo.duration = secondsToMinutes(resInfo.duration);
      }
    }
  });
}
function videoConfigSetting() {
  if (player.value) player.value.reset();
  // player.value = new Player(playerConfig);
  if (playerConfig.url.includes(".m3u8")) {
    player.value = new Player({ ...playerConfig, ...hlsConfig });
  } else player.value = new Player(playerConfig);
}

function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.ent-material-action-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    .left {
      display: flex;
      justify-content: space-between;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      font-size: 20px;
      color: #3b4664;
    }
    .right {
      .btn {
        border-radius: 13px;
        width: 52px;
        height: 28px;
        //font-family: 苹方-简, 苹方-简;
        font-weight: 400;
        font-size: 13px;
      }
    }
  }
  .content {
    height: calc(100% - 260px);
    width: 100%;
    padding: 10px 20px 10px 20px;
    .course-video {
      width: 100% !important;
      height: 100% !important;
      padding-top: 0 !important;
    }
    .picture {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100% !important;
      img {
        height: 100% !important;
      }
      padding-top: 0 !important;
    }
  }
  .footer {
    height: 180px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    // align-items: center;
    margin-top: 15px;
    color: #3b4664;
    .title {
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      font-size: 20px;

      text-align: left;
    }
    .block {
      width: 439px;
      height: 120px;
      padding: 15px 20px 0 20px;
      box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.14);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #edeff4;
      display: flex;
      // align-items: center;
      justify-content: space-between;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 400;
      font-size: 15px;
      .text {
        padding: 5px;
      }
    }
  }
}
</style>
<style lang="scss">
.xgplayer {
  svg path {
    fill: #0eb53b !important;
  }
  .xgplayer-loading {
    svg path {
      fill: none !important;
    }
  }
  .xgplayer-start {
    .xg-icon-play,
    .xg-icon-pause {
      background: #ffffff !important;
      box-shadow: 0px 3px 20px 1px rgba(18, 172, 136, 0.65),
        inset 0px 3px 12px 1px rgba(13, 168, 138, 0.48) !important;
    }
  }
  .xg-left-grid {
    span {
      color: #0eb53b !important;
    }
  }
  .btn-text {
    span {
      color: #000 !important;
      background: #ffffff !important;
      box-shadow: inset 0px 3px 6px 1px rgba(14, 134, 28, 0.16) !important;
      border-radius: 10px 10px 10px 10px !important;
    }
  }
  .xgplayer-controls {
    background: #283333 !important;
  }
}
</style>
