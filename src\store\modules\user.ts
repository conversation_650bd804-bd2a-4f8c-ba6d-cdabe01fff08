import { UserInfo } from "./../../api/user/types";
import { defineStore } from "pinia";

import { userLogin, getMyInfo, userLogout } from "@/api/user";
import { resetRouter } from "@/router";
import { store } from "@/store";

import { useStorage } from "@vueuse/core";
import { getVODTokens, getOBSTokens } from "@/api/file";
import { parseTime } from "@/utils";
import { usePermissionStore } from "@/store/modules/permission";

export const useUserStore = defineStore("user", () => {
  const defaultAvatar = ref(
    // "https://gkr.obs.cn-north-4.myhuaweicloud.com/image/default/defalut-avatar1.png"
    "http://obs.gknowledge.cn/image/default/defalut-avatar1.png"
  );
  const userInfo = useStorage<any>("userInfoByAdmin", {
    avatar: defaultAvatar.value, //默认头像
    id: "",
    auth_code: "",
    // "Z2VCT2J5djVhb1N5U15rRUk4PnllZFBSWDRBd2A9cl5XTDZQbnA1QXpTYVMwXFFLeD1MM0dkOW5kMl48b2x0dQ==", //	登录的token
    // id: "",
    // auth_token: "",
    permissions: "",
    menus: [],
    role: "",
  });
  const userMap = reactive<any>({
    10: "游客",
    20: "学员",
    30: "企业员工",
    40: "企业管理员",
    50: "绿知管理员",
  });

  const token: any = useStorage(
    "accessTokenByAdmin",
    computed(() => {
      return userInfo.value.auth_code || "";
    })
  );
  const menus: any = useStorage(
    "menusByAdmin",
    computed(() => {
      return userInfo.value.menus || "";
    })
  );
  const role: any = useStorage(
    "roleByAdmin",
    computed(() => {
      return userInfo.value.role || null;
    })
  );
  const identity: any = useStorage(
    "identityByAdmin",
    computed(() => {
      return userInfo.value.id || null;
    })
  );
  const isLogin = computed(() => {
    return !!userInfo.value.auth_code;
  });

  const obsKey: any = useStorage("obsKey", {
    ak: "",
    sk: "",
    token: "",
    expired_at: "",
  });
  const vodKey: any = useStorage("vodKey", {
    token: "",
    expired_at: "",
  });

  /**
   * 登录
   *
   * @param {LoginData}
   * @returns
   */
  function login(loginData: any) {
    return new Promise<void>((resolve, reject) => {
      userLogin(loginData)
        .then((res: any) => {
          if (res.status == 200) {
            Object.keys(res.data).forEach((key) => {
              if (res.data[key]) {
                userInfo.value[key] = res.data[key];
              }
            });
            userInfo.value.menus = res.data.menus;
            userInfo.value.permissions = res.data.roles?.[0].permissions;
            userInfo.value.role = res.data.roles?.[0].name;
          }
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 获取信息(用户昵称、头像、角色集合、权限集合)
  async function getUserInfo() {
    return new Promise<void>((resolve, reject) => {
      getMyInfo()
        .then(async (res: any) => {
          // console.log("getUserInfo再跳转页面", res);

          Object.keys(res.data).forEach((key) => {
            userInfo.value[key] = res.data[key];
          });
          // userInfo.value.menus = res.data.menus;
          userInfo.value.permissions = res.data.roles?.[0].permissions;
          userInfo.value.role = res.data.roles?.[0].name;
          const permissionStore = usePermissionStore();
          await permissionStore.initRouter(res.data.menus);
          // console.log("目前route", permissionStore.routes);
          //判断obs、vod的token是否过期,应该做个定时器
          const now = parseTime(new Date().getTime(), "{y}{m}{d}{h}{i}{s}") * 1;
          const obs_expired_at = obsKey.value.expired_at
            ? parseTime(obsKey.value.expired_at, "{y}{m}{d}{h}{i}{s}") * 1
            : 0;
          const vod_expired_at = vodKey.value.expired_at
            ? parseTime(vodKey.value.expired_at, "{y}{m}{d}{h}{i}{s}") * 1
            : 0;
          if (now > obs_expired_at) {
            getOBS();
          }
          if (now > vod_expired_at) {
            getVOD();
          }

          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  async function judgeObsAndVodExpired() {
    return new Promise<void>((resolve, reject) => {
      //判断obs、vod的token是否过期,在相应操作前判断
      const now = parseTime(new Date().getTime(), "{y}{m}{d}{h}{i}{s}") * 1;
      const obs_expired_at = obsKey.value.expired_at
        ? parseTime(obsKey.value.expired_at, "{y}{m}{d}{h}{i}{s}") * 1
        : 0;
      const vod_expired_at = vodKey.value.expired_at
        ? parseTime(vodKey.value.expired_at, "{y}{m}{d}{h}{i}{s}") * 1
        : 0;
      if (now > obs_expired_at) {
        getOBS();
      }
      if (now > vod_expired_at) {
        getVOD();
      }
      resolve();
    });
  }

  // user logout
  function logout() {
    return new Promise<void>((resolve, reject) => {
      userLogout(identity.value)
        .then(() => {
          resetToken();
          location.reload(); // 清空路由
          resolve();
        })
        .catch((error) => {
          resetToken();
          location.reload(); // 清空路由
          reject(error);
        });
    });
  }

  // remove token
  function resetToken() {
    return new Promise<void>((resolve) => {
      resetRouter();
      localStorage.clear();
      sessionStorage.clear();
      resolve();
    });
  }

  function getOBS() {
    return new Promise<void>((resolve, reject) => {
      getOBSTokens()
        .then((res: any) => {
          if (res.status == 200) {
            obsKey.value.ak = res.data.ak;
            obsKey.value.sk = res.data.sk;
            obsKey.value.token = res.data.token;
            obsKey.value.expired_at = res.data.expired_at;
          }
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  function getVOD() {
    return new Promise<void>((resolve, reject) => {
      getVODTokens()
        .then((res: any) => {
          if (res.status == 200) {
            vodKey.value.token = res.data.token;
            vodKey.value.expired_at = res.data.expired_at;
          }
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  return {
    token,
    userInfo,
    identity,
    role,
    menus,
    isLogin,
    obsKey,
    vodKey,
    defaultAvatar,
    login,
    getUserInfo,
    logout,
    resetToken,
    judgeObsAndVodExpired,
  };
});

// 非setup
export function useUserStoreHook() {
  return useUserStore(store);
}
