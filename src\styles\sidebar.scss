.app {
  .main-container {
    position: relative;
    min-height: 100%;
    margin-left: $sideBarWidth;
    transition: margin-left 0.28s;
  }

  .sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: $sideBarWidth !important;
    height: 100%;
    overflow: hidden;
    background-color: $menuBg;
    transition: width 0.28s;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 80px);
      }
    }

    .is-horizontal {
      display: none;
    }

    .svg-icon {
      margin-right: 12px;
      color: #00918c !important;
      fill: #00918c !important;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      width: 100% !important;
      height: 100%;
      border: none;
    }

    .el-menu-item {
      //font-family: PingFang SC, PingFang SC !important;
      font-weight: 400 !important;
      font-size: 17px !important;
      color: #3b4664 !important;
    }
    // menu hover
    .submenu-title-noDropdown,
    .el-sub-menu__title {
      //font-family: PingFang SC, PingFang SC !important;
      font-weight: 400 !important;
      font-size: 17px !important;
      color: #3b4664 !important;
      &:hover {
        // background: $subMenuHover !important;
        // color: $subMenuActiveText !important;
        background: #f5fafa !important;
        color: #3b4664 !important;
        position: relative;
        &::before {
          position: absolute;
          content: "";
          width: 4px;
          height: 100%;
          top: 0;
          left: 0;
          background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
        }
        .svg-icon {
          color: #00918c !important;
        }
      }
    }
    .submenu-title-dropdown,
    .el-sub-menu__title {
      &:hover {
        background: #f5fafa !important;
        // background: #fff !important;
        color: $menuText !important;
        position: relative;
        &::before {
          position: absolute;
          content: "";
          width: 4px;
          height: 100%;
          top: 0;
          left: 0;
          background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
        }
        .svg-icon {
          color: #00918c !important;
        }
      }
    }

    .is-active > .el-sub-menu__title {
      color: $subMenuActiveText !important;
    }
    .is-active {
      background: $menuHover !important;
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $sideBarWidth !important;
      // background: $subMenuBg !important;
      background: #f5fafa !important;

      &:hover {
        // background: $subMenuHover !important;
        // color: $subMenuActiveText !important;
        background: #f5fafa !important;
        color: #3b4664 !important;
        position: relative;
        &::before {
          position: absolute;
          content: "";
          width: 4px;
          height: 100%;
          top: 0;
          left: 0;
          background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
        }
      }
    }
  }

  .hideSidebar {
    .mix-wrapper__left {
      width: 54px;
    }

    .sidebar-container {
      width: 54px !important;

      .header {
        .logo-wrap {
          width: 54px !important;
          transition: transform 0.28s;
        }
      }
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      position: relative;
      padding: 0 !important;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }

      // TODO
      & > .svg-icon {
        margin-left: 20px;
      }

      & > span {
        display: inline-block;
        width: 0;
        height: 0;
        overflow: hidden;
        visibility: hidden;
      }
    }

    .el-sub-menu {
      overflow: hidden;

      & > .el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-sub-menu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        & > .el-sub-menu__title {
          & > span {
            display: inline-block;
            width: 0;
            height: 0;
            overflow: hidden;
            visibility: hidden;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0;
    }

    .sidebar-container {
      width: $sideBarWidth !important;
      transition: transform 0.28s;
    }

    &.hideSidebar:not(.isMix, .isTop) {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }

  .sidebar-container .svg-icon {
    color: #00918c;
  }

  .sidebar-container .el-sub-menu.is-active {
    background: #fff !important;
    .el-sub-menu__title {
      color: #3b4664 !important;
      .svg-icon {
        color: #ffffff !important;
      }
    }
  }
  .sidebar-container .el-menu-item.is-active {
    color: $subMenuActiveText !important;
    background: $subMenuHover !important;
    .svg-icon {
      fill: #fff !important;
      color: #fff !important;
    }
  }

  .sidebar-container .el-submenu .el-menu-item,
  .sidebar-container .nest-menu .el-submenu > .el-submenu__title {
    background: #f5fafa !important;
    fill: #fff !important;
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 12px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // background: $subMenuHover !important;
      background: #f5fafa !important;
      color: $subMenuActiveText !important;
      position: relative;
      &::before {
        position: absolute;
        content: "";
        width: 4px;
        height: 100%;
        top: 0;
        left: 0;
        background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
      }
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
