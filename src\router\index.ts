import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/",
    redirect: "/dashboard",
  },

  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },
  // {
  //   path: "/",
  //   component: Layout,
  // //   redirect: "/dashboard",
  // // },
  // {
  //   path: "/:catchAll(.*)",
  //   redirect: "/404",
  // },
  {
    path: "/401",
    component: () => import("@/views/error-page/401.vue"),
    meta: { hidden: true },
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404.vue"),
    meta: { hidden: true },
  },
];

//动态路由
export const asyncRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        // name: "Dashboard", // 用于 keep-alive, 必须与SFC自动推导或者显示声明的组件name一致
        meta: {
          title: "数据总览",
          icon: "zonglang",
          // affix: true,
          // keepAlive: true,
          alwaysShow: false,
        },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    redirect: "/user/user-record",
    // meta: {
    //   title: "用户管理",
    //   icon: "yonghuguanli",
    //   hidden: false,
    //   seq: 0,
    //   keepAlive: true,
    // },
    children: [
      {
        path: "user-record",
        component: () => import("@/views/user/user-record.vue"),
        name: "UserRecord",
        meta: {
          icon: "yonghuguanli",
          title: "用户管理",
          hidden: false,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/course",
    component: Layout,
    redirect: "/course/index",
    meta: {
      title: "课程管理",
      icon: "kechengguanli",
      hidden: false,

      keepAlive: true,
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/course/index.vue"),
        name: "CourseIndex",
        meta: {
          title: "课程",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "free-course",
        component: () => import("@/views/course/free-course.vue"),
        name: "FreeCourse",
        meta: {
          title: "限免课程",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "course-action",
        component: () => import("@/views/course/course-action.vue"),
        name: "CourseAction",
        meta: {
          title: "课程",
          hidden: true,
          keepAlive: false,
        },
      },
      {
        path: "course-data",
        component: () => import("@/views/course/course-data.vue"),
        name: "CourseData",
        meta: {
          title: "课程",
          remark: "课程数据",
          hidden: true,
          keepAlive: false,
        },
      },
      {
        path: "material-library-group",
        component: () => import("@/views/course/material-library-group.vue"),
        name: "MaterialLibraryGroup",
        meta: {
          title: "素材分组",
          hidden: false,

          keepAlive: true,
        },
      },
      {
        path: "material-library",
        component: () => import("@/views/course/material-library.vue"),
        name: "MaterialLibrary",
        meta: {
          title: "素材库",
          hidden: false,

          keepAlive: true,
        },
      },
      // {
      //   path: "course",
      //   component: () => import("@/views/course/course.vue"),
      //   name: "Course",
      //   meta: {
      //     title: "课程",
      //     hidden: false,
      //
      //     keepAlive: true,
      //   },
      // },
      {
        path: "course-sort",
        component: () => import("@/views/course/course-sort.vue"),
        name: "CourseSort",
        meta: {
          title: "分类管理",
          hidden: false,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/exam",
    component: Layout,
    redirect: "/exam/index",
    meta: {
      title: "考试管理",
      icon: "kechengguanli",
      hidden: false,
      keepAlive: true,
    },
    children: [
      {
        path: "question-bank",
        component: () => import("@/views/exam/question-bank.vue"),
        name: "QuestionBank",
        meta: {
          title: "题库",
          remark: "题库管理",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "question-bank-action",
        component: () => import("@/views/exam/question-bank-action.vue"),
        name: "QuestionBankAction",
        meta: {
          title: "题库",
          remark: "题库操作",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "question-bank-detail",
        component: () => import("@/views/exam/question-bank-detail.vue"),
        name: "QuestionBankDetail",
        meta: {
          title: "题库",
          remark: "题库详情",
          hidden: true,
          keepAlive: true,
        },
      },

      {
        path: "index",
        component: () => import("@/views/exam/index.vue"),
        name: "ExamIndex",
        meta: {
          title: "考试",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "exam-action",
        component: () => import("@/views/exam/exam-action.vue"),
        name: "ExamAction",
        meta: {
          title: "考试",
          remark: "考试操作",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "exam-detail",
        component: () => import("@/views/exam/exam-detail.vue"),
        name: "ExamDetail",
        meta: {
          title: "考试",
          remark: "考试详情",
          hidden: true,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/web-page",
    component: Layout,
    redirect: "/web-page/banner",
    meta: {
      title: "页面管理",
      icon: "yemianguanli",
      hidden: false,
      keepAlive: true,
    },
    children: [
      {
        path: "banner",
        component: () => import("@/views/web-page/banner.vue"),
        name: "Banner",
        meta: {
          title: "banner管理",
          hidden: false,

          keepAlive: true,
        },
      },
      {
        path: "banner-action",
        component: () => import("@/views/web-page/banner-action.vue"),
        name: "BannerAction",
        meta: {
          title: "banner管理",
          hidden: true,

          // keepAlive: true,
        },
      },
      {
        path: "column",
        component: () => import("@/views/web-page/column.vue"),
        name: "Column",
        meta: {
          title: "栏目管理",
          hidden: false,

          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/enterprise",
    component: Layout,
    redirect: "/enterprise/index",
    meta: {
      title: "企业管理",
      icon: "qiyeguanli",
      hidden: false,
      keepAlive: true,
    },
    children: [
      {
        path: "ent-consulting",
        component: () => import("@/views/enterprise/ent-consulting.vue"),
        name: "EnterpriseConsulting",
        meta: {
          title: "咨询",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "ent-index",
        component: () => import("@/views/enterprise/ent-index.vue"),
        name: "EnterpriseIndex",
        meta: {
          title: "企业信息",
          hidden: false,

          keepAlive: true,
        },
      },
      {
        path: "ent-detail",
        component: () => import("@/views/enterprise/ent-detail.vue"),
        name: "EnterpriseDetail",
        meta: {
          title: "企业信息",
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "ent-course",
        component: () => import("@/views/enterprise/ent-course.vue"),
        name: "EnterpriseCourse",
        meta: {
          title: "企业课程",
          hidden: false,

          keepAlive: true,
        },
      },
      {
        path: "ent-combo",
        component: () => import("@/views/enterprise/ent-combo.vue"),
        name: "EnterpriseCombo",
        meta: {
          title: "企业套餐",
          hidden: false,

          keepAlive: true,
        },
      },
      {
        path: "ent-combo-action",
        component: () => import("@/views/enterprise/ent-combo-action.vue"),
        name: "EnterpriseComboAction",
        meta: {
          title: "企业套餐",
          hidden: true,

          keepAlive: true,
        },
      },
      {
        path: "ent-menus",
        component: () => import("@/views/enterprise/ent-menus.vue"),
        name: "EnterpriseMenus",
        meta: {
          title: "企业菜单管理",
          hidden: false,
          keepAlive: true,
        },
      },
      // {
      //   path: "ent-material",
      //   component: () => import("@/views/enterprise/ent-material.vue"),
      //   name: "EnterpriseMaterial",
      //   meta: {
      //     title: "企业素材",
      //     hidden: false,

      //     keepAlive: true,
      //   },
      // },
      {
        path: "ent-material-action",
        component: () => import("@/views/enterprise/ent-material-action.vue"),
        name: "EnterpriseMaterialAction",
        meta: {
          title: "企业素材",
          hidden: true,

          keepAlive: true,
        },
      },
      // {
      //   path: "ent-industry",
      //   component: () => import("@/views/enterprise/ent-industry.vue"),
      //   name: "EnterpriseIndustry",
      //   meta: {
      //     title: "行业管理",
      //     hidden: false,
      //
      //     keepAlive: true,
      //   },
      // },
    ],
  },
  {
    path: "/mung-bean",
    component: Layout,
    redirect: "/mung-bean/index",
    // meta: {
    //   title: "绿豆管理",
    //   icon: "lvdouguanli",
    //   hidden: false,

    //   keepAlive: true,
    // },
    children: [
      {
        path: "index",
        component: () => import("@/views/mung-bean/index.vue"),
        name: "BeanIndex",
        meta: {
          title: "绿豆管理",
          hidden: false,
          icon: "lvdouguanli",
          keepAlive: true,
        },
      },
      {
        path: "bean-reconciliation",
        component: () => import("@/views/mung-bean/mung-bean-action.vue"),
        name: "BeanReconciliation",
        meta: {
          title: "绿豆管理",
          hidden: true,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/order",
    component: Layout,
    redirect: "/order/personal-order",

    meta: {
      title: "订单管理",
      icon: "dingdanguanli",
      hidden: false,

      keepAlive: true,
    },
    children: [
      {
        path: "personal-order",
        component: () => import("@/views/order/personal-order.vue"),
        name: "personalOrder",
        meta: {
          title: "个人订单",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "donate-order",
        component: () => import("@/views/order/donate-order.vue"),
        name: "donate-order",
        meta: {
          title: "赠予订单",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "ent-order",
        component: () => import("@/views/order/ent-order.vue"),
        name: "entOrder",
        meta: {
          title: "企业订单",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "ent-order-detail",
        component: () => import("@/views/order/ent-order-detail.vue"),
        name: "entOrderDetail",
        meta: {
          title: "企业订单",
          hidden: true,
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/message",
    component: Layout,
    redirect: "/message/index",
    // meta: {
    //   title: "消息管理",
    //   icon: "xiaoxiguanli",
    //   hidden: false,

    //   keepAlive: true,
    // },
    children: [
      {
        path: "index",
        component: () => import("@/views/message/index.vue"),
        name: "MessageIndex",
        meta: {
          title: "消息管理",
          hidden: false,
          icon: "xiaoxiguanli",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/platform",
    component: Layout,
    redirect: "/platform/roles",
    meta: {
      title: "平台管理",
      icon: "pingtaiguanli",
      hidden: false,
      keepAlive: true,
    },
    children: [
      {
        path: "roles",
        component: () => import("@/views/platform/roles.vue"),
        name: "Roles",
        meta: {
          title: "角色管理",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "admin",
        component: () => import("@/views/platform/admin.vue"),
        name: "Admin",
        meta: {
          title: "管理员管理",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "menus",
        component: () => import("@/views/platform/menus.vue"),
        name: "Menus",
        meta: {
          title: "菜单管理",
          hidden: false,
          keepAlive: true,
        },
      },
      {
        path: "logs",
        component: () => import("@/views/platform/logs.vue"),
        name: "Logs",
        meta: {
          title: "操作日志",
          hidden: false,
          keepAlive: true,
        },
      },
    ],
  },
];
/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes as RouteRecordRaw[],
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: "/login" });
}

export default router;
