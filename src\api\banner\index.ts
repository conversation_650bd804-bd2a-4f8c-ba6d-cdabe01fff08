import request from "@/utils/request";
import { AxiosPromise } from "axios";

export function getBanners(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/banners",
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增
 *
 * @param data
 */
export function addBanner(data: any) {
  return request({
    url: "/gkadmin/v1/banners",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateBanner(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/banners/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteBanner(ids: string) {
  return request({
    url: "/gkadmin/v1/banners/" + ids,
    method: "delete",
  });
}
