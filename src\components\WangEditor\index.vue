<template>
  <div class="editor-wrapper">
    <!-- 工具栏 -->
    <Toolbar
      id="toolbar-container"
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <!-- 编辑器 -->
    <Editor
      id="editor-container"
      v-model="modelValue"
      :default-config="editorConfig"
      :mode="mode"
      @on-change="handleChange"
      @on-created="handleCreated"
    />
    <!-- 资源弹窗 -->
    <el-dialog
      class="imgDialog"
      v-model="imgDialog.visible"
      :title="imgDialog.title"
      :width="imgDialog.width"
      append-to-body
      @close="closeImgDialog"
    >
      <div class="dialog-body">
        <el-form>
          <el-form-item label="图片名称">
            <el-col :span="12">
              <el-input
                v-model="currentImg.name"
                placeholder="图片名称"
                clearable
                size="large"
              />
            </el-col>
          </el-form-item>
          <el-form-item class="table-item">
            <div class="table-search">
              <div class="filter-row">
                <el-input
                  v-model="queryParams.search"
                  placeholder="请输入关键字"
                  clearable
                  size="large"
                />
              </div>
              <div class="btn primary-btn" @click="handleQuery">
                <i-ep-search /> 搜索
              </div>
            </div>
            <el-table
              v-loading="loading"
              element-loading-text="Loading"
              element-loading-background="#ffffffb4"
              :data="tableData"
              height="20rem"
              border
              fit
              highlight-current-row
              ref="tableRef"
              @select="selectItem"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />

              <el-table-column
                label="名称"
                align="center"
                prop="name"
                min-width="80"
              >
                <template #default="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="大小"
                align="center"
                prop="name"
                min-width="40"
              >
                <template #default="scope">
                  <span>{{ scope.row.size }}Mb</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-footer">
              <pagination
                v-if="total > 0"
                v-model:total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="handleQuery"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeImgDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import UploadImg from "@/components/Upload/UploadImg.vue";
import { getResources } from "@/api/resource";
import { parseTime } from "@/utils";
// API 引用
// import { uploadFileApi } from "@/api/file";

const props = defineProps({
  modelValue: {
    type: [String],
    default: "",
  },
});

const emit = defineEmits(["update:modelValue"]);

const modelValue = useVModel(props, "modelValue", emit);

const editorRef = shallowRef(); // 编辑器实例，必须用 shallowRef
const mode = ref("default"); // 编辑器模式
const toolbarConfig = ref({
  excludeKeys: [
    "fullScreen",
    "group-video",
    "emotion",
    // "group-image",
    "insertImage",
    // "uploadImage",
  ],
}); // 工具条配置
// 编辑器配置
const editorConfig = ref({
  placeholder: "请输入内容...",
  MENU_CONF: {
    uploadImage: {
      async customBrowseAndUpload(insertFn: any) {
        // 打开图片素材库
        imgDialog.visible = true;
      },
    },
    // uploadImage: {
    //   // 自定义图片上传
    //   async customUpload(file: any, insertFn: any) {
    //     uploadFileApi(file).then((response) => {
    //       const url = response.data.url;
    //       insertFn(url);
    //     });
    //   },
    // },
  },
});

//素材-图片弹窗
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  res_type: 20,
  // gid: "",
});
const total = ref(0); // 数据总数
const tableData = ref<any>([]);
const loading = ref(false);
const imgDialog = reactive({
  visible: false,
  title: "素材-图片",
  width: "60%",
});
const tableRef = ref();

const currentImg = reactive<any>({});
watch(
  () => imgDialog.visible,
  (val) => {
    if (val == true) {
      getResourceData();
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

function getResourceData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.res_type,
  };
  getResources(params).then((res: any) => {
    tableData.value = res.data.resources.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      item.size = (item.size / 1024 / 1024)?.toFixed(2);
      if (item.res_type !== 10) {
        item.size = item.res_info.size
          ? (item.res_info.size / 1024 / 1024)?.toFixed(2)
          : (item.size / 1024 / 1024)?.toFixed(2);
      }
      return item;
    });
    total.value = res.total;
    loading.value = false;
  });
}
function handleQuery() {
  getResourceData();
}
function closeImgDialog() {
  tableRef.value.clearSelection();
  Object.assign(currentImg, {
    name: "",
    res_info: "",
    id: "",
  });
  Object.assign(queryParams, {
    search: "",
    pageNum: 1,
    pageSize: 20,
    res_type: 20,
    // gid: "",
  });
  imgDialog.visible = false;
}
function handleSubmit() {
  const res_info: any =
    typeof currentImg.res_info === "string"
      ? JSON.parse(currentImg.res_info)
      : currentImg.res_info;
  closeImgDialog();
  editorRef.value.insertNode({
    type: "image",
    src: res_info["url"],
    textAlign: "center",
    style: { marginRight: "auto", marginLeft: "auto" },
    children: [{ text: "" }],
  });
}
function selectItem(selection: any) {
  let selectRow: any = {};
  if (selection.length > 1) {
    let del_row = selection.shift(); // 删除选中的第一项
    tableRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  }
  // 到这selection数据始终为1条
  if (selection.length) {
    selectRow = selection[0];
  }
  Object.assign(currentImg, {
    name: selectRow.name,
    res_info: selectRow.res_info,
    id: selectRow.id,
  });
}

const handleCreated = (editor: any) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

function handleChange(editor: any) {
  modelValue.value = editor.getHtml();
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style scoped lang="scss">
.editor-wrapper {
  width: 100%;
  min-height: 15rem;
  border: 1px solid #ccc;
}

.imgDialog {
  .table-item {
    width: 100%;

    .table-search {
      display: flex;
      width: 100%;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 360px;
        margin-left: 20px;

        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }
      }

      .primary-btn {
        width: 116px !important;
        margin-left: 20px !important;
        // height: 40px !important;
      }
    }

    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-checkbox {
          display: none;
          visibility: hidden;
        }
      }
    }

    .table-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
  }
}
</style>
