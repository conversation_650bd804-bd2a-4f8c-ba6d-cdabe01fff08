/*
 * @Author: czf <EMAIL>
 * @Date: 2024-08-30 16:19:08
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-12-06 18:04:18
 * @FilePath: \gkweb\mock\dict.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { MockMethod } from "vite-plugin-mock";

export default [
  {
    url: "/api/v1/dict/:code/options",
    method: "get",
    response: ({ url }: { url: any }) => {
      const typeCode = url.match(/\/api\/v1\/dict\/(\w+)\/options/)[1];

      let list: any = null;

      if (typeCode == "gender") {
        list = [
          {
            value: "1",
            label: "男",
          },
          {
            value: "2",
            label: "女",
          },
          {
            value: "0",
            label: "未知",
          },
        ];
      }

      return {
        code: "00000",
        data: list,
        msg: "一切ok",
      };
    },
  },
] as MockMethod[];
