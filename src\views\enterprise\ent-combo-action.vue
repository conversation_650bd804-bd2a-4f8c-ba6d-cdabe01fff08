<template>
  <div class="ent-combo-detail-container">
    <div class="content">
      <div class="base-content">
        <div class="content-header">
          <div class="left">企业套餐-{{ typeMap[pageType].label }}</div>
          <div class="right">
            <div class="btn primary-btn" @click="handleBack">返回</div>
          </div>
        </div>
        <el-form
          :model="formData"
          :rules="formDataRules"
          ref="formRef"
          class="base-form"
          label-width="7.7rem"
          :style="{
            'pointer-events':
              pageType == 'create' || pageType == 'edit' ? 'auto' : 'none',
          }"
        >
          <el-row :gutter="20" class="row">
            <el-col :span="11">
              <el-form-item label="套餐名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入名称"
                  clearable
                  size="large"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="2" />
            <el-col :span="11">
              <el-form-item label="套餐价格（￥）" prop="price">
                <el-input
                  v-model="formData.price"
                  placeholder="请输入"
                  clearable
                  size="large"
                />
              </el-form-item>
            </el-col> -->
          </el-row>

          <!-- <el-form-item label="图片" prop="thumb">
              <UploadImg v-model:imgUrl="formData.thumb" />
            </el-form-item> -->

          <!-- <el-form-item label="账号数量" prop="accounts">
              <el-input
                v-model="formData.accounts"
                placeholder="请输入账号数量"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="更换账号次数" prop="unbind_acc">
              <el-input
                v-model="formData.unbind_acc"
                placeholder="请输入"
                clearable
                size="large"
              />
            </el-form-item> -->
          <!-- <el-form-item label="存储容量" prop="storage">
              <el-input
                v-model="formData.storage"
                placeholder="请输入"
                clearable
                size="large"
              >
                <template #append>G</template>
              </el-input>
            </el-form-item> -->
          <!-- <el-form-item label="套餐有效期至" prop="expired_at">
              <el-date-picker
                v-model="formData.expired_at"
                placeholder="截至日期"
                class="date-picker"
                size="large"
                type="date"
                value-format="YYYY-MM-DD"
              />
            </el-form-item> -->
          <el-row :gutter="20" class="row">
            <el-col :span="11">
              <el-form-item label="有效期天数" prop="period">
                <el-input
                  v-model="formData.period"
                  placeholder="请输入"
                  clearable
                  size="large"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2" />
            <el-col :span="11">
              <el-form-item label="描述" prop="description">
                <el-input
                  v-model="formData.description"
                  placeholder="请输入"
                  clearable
                  size="large"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="row">
            <el-col :span="11">
              <el-form-item label="状态" prop="status" v-show="enableList">
                <el-select
                  :suffix-icon="`CaretBottom`"
                  size="large"
                  v-model="formData.status"
                  placeholder="请选择套餐状态"
                  filterable
                >
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2" />
            <el-col :span="11">
              <el-form-item label="企业配套课程" prop="course">
                <div class="btn primary-btn" @click="handleSelectCourse">
                  <i-ep-circle-plus style="margin-right: 5px" />
                  <span class="text"> 添加配套课程</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="course-content">
        <!-- <el-scrollbar height="100%" warp-style="overflow-x: hidden;"> -->
        <div class="course-header">
          <div class="left">已选课程</div>
          <div class="right">已选{{ tableData.length }}个课程</div>
        </div>
        <div class="course-list">
          <el-table
            :data="tableData"
            height="100%"
            border
            fit
            highlight-current-row
          >
            <el-table-column label="课程" align="center" min-width="100">
              <template #default="scope">
                <div class="cover-name">
                  <img :src="scope.row.thumb" alt="" class="cover-img" />
                  <span>{{ scope.row.title }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="所属栏目" align="center" min-width="100">
              <template #default="scope">
                {{
                  scope.row.sections?.[0]?.name ||
                  scope.row.sections?.toString() ||
                  "--"
                }}
              </template>
            </el-table-column>
            <el-table-column label="课程账号数" align="center" min-width="100">
              <template #default="scope">
                <el-input
                  v-model="scope.row.accounts"
                  placeholder="请输入账号数目"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="可更改账号数"
              align="center"
              min-width="100"
            >
              <template #default="scope">
                <el-input
                  v-model="scope.row.reassigned"
                  placeholder="请输入可更改账号数"
                />
              </template>
            </el-table-column>
            <!-- <el-table-column label="有效期至" align="center" min-width="100">
                <template #default="scope">
                  <span>{{ scope.row.expired_at }}</span>
                </template>
              </el-table-column> -->
            <el-table-column label="操作" align="center" min-width="50">
              <template #default="scope">
                <div class="option-btn">
                  <div
                    class="btn delete-btn"
                    @click="onRowClick('delete', scope.row)"
                  >
                    删除
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="footer">
          <div class="btn orange-btn" @click="handleBack">取消</div>
          <div class="btn primary-btn" @click="handleSubmit">保存</div>
        </div>
        <!-- </el-scrollbar> -->
      </div>
    </div>

    <el-dialog
      class="course-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form label-width="7rem">
          <!-- <el-form-item label="课程有效期至">
            <el-col :span="12">
              <el-date-picker
                v-model="courseForm.expired_at"
                placeholder="截至日期"
                class="date-picker"
                size="large"
                type="date"
                value-format="YYYY-MM-DD"
              />

            </el-col>
          </el-form-item> -->
          <!-- <el-date-picker
                class="date-picker"
                size="large"
                v-model="courseForm.dateTimeRange"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
              /> -->
          <el-form-item class="table-item">
            <div class="table-search">
              <div class="filter-row">
                <el-input
                  v-model="queryParams.search"
                  placeholder="请输入关键字"
                  clearable
                  size="large"
                />
              </div>
              <div class="filter-row">
                <el-select
                  size="large"
                  v-model="queryParams.sec_id"
                  placeholder="请选择栏目"
                  filterable
                  clearable
                  :suffix-icon="`CaretBottom`"
                  @change="getCourseList"
                >
                  <el-option
                    v-for="item in sec_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
              <div class="btn primary-btn" @click="getCourseList">
                <i-ep-search /> 搜索
              </div>
            </div>
            <el-table
              class="content"
              :data="courseList"
              height="35rem"
              border
              fit
              highlight-current-row
              ref="courseRef"
              @select="selectCourse"
              @select-all="selectAllCourses"
              v-loading="courseLoading"
              element-loading-text="Loading"
              element-loading-background="#ffffffb4"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />
              <el-table-column label="课程" align="center" min-width="100">
                <template #default="scope">
                  <div class="cover-name">
                    <img :src="scope.row.thumb" alt="" class="cover-img" />
                    <span>{{ scope.row.title }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="所属栏目"
                align="center"
                prop="mobile"
                min-width="100"
              >
                <template #default="scope">
                  {{ scope.row.sections?.[0]?.name }}
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <div class="table-footer">
          <pagination
            v-if="courseListTotal > 0"
            v-model:total="courseListTotal"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getCourseList"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="courseSubmit">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import UploadImg from "@/components/Upload/UploadImg.vue";
import {
  getEntComboDetail,
  addEntCombo,
  updateEntCombo,
} from "@/api/enterprise";
import { getCourses } from "@/api/course";
import { getSections } from "@/api/web-page";
import { parseTime } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "BannerAction",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const pageType: any = computed(() => route.query.type);
const comboId: any = computed(() => route.query.id);
const typeMap = reactive<any>({
  create: { label: "新增" },
  edit: { label: "编辑" },
  detail: { label: "详情" },
});
const statusOptions = reactive<any>([
  {
    value: 20,
    label: "上架",
  },
  {
    value: 30,
    label: "下架",
  },
]);

// 表单数据
const formData = reactive<any>({
  name: "",
  description: "",
  thumb: null,
  status: 20,
  period: "",
  // expired_at: "",
  storage: 0,
  accounts: 0,
  unbind_acc: 0,
  price: "",
});
const imgUrl = ref("");

const tipsImg = ref(new URL("@/assets/images/tips.png", import.meta.url).href);
const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  thumb: [
    { required: true, message: "请上传封面图", trigger: ["blur", "change"] },
  ],
  accounts: [{ required: true, message: "请输入账号数", trigger: "blur" }],
  storage: [{ required: true, message: "请输入存储容量", trigger: "blur" }],
  unbind_acc: [
    { required: true, message: "请输入可更换账号数", trigger: "blur" },
  ],
  period: [
    { required: true, message: "请选择截至日期", trigger: ["blur", "change"] },
  ],
  price: [
    { required: true, message: "请输入套餐价格", trigger: ["blur", "change"] },
  ],
  // expired_at: [
  //   { required: true, message: "请选择截至日期", trigger: ["blur", "change"] },
  // ],
});

const tableData = ref<any>([]);

const dialog = reactive<any>({
  visible: false,
  type: "course-dialog",
  width: "60%",
  title: "配套课程",
});
const courseForm = reactive<any>({
  dateTimeRange: "",
  // expired_at: "",
  courseData: [
    // {id:'',accounts:""}
  ],
});
const courseLoading = ref(false);
const sec_options = ref<any>([]);
const courseList = ref<any>([]);
const courseListTotal = ref<any>();
const courseRef = ref<any>();
const queryParams = reactive<any>({
  search: "",
  sec_id: "",
  pageNum: 1,
  pageSize: 20,
});
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });

onMounted(() => {
  if (pageType.value == "edit") {
    getData();
  }
});

const enableList = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 18);
});

function getData() {
  getEntComboDetail(comboId.value).then((res: any) => {
    Object.assign(formData, res);
    // formData.expired_at = parseTime(res.expired_at, "{y}-{m}-{d}");
    tableData.value = res.package_courses;
  });
}

function handleBack() {
  router.go(-1);
}

function getSecList() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getSections(params).then((res: any) => {
    sec_options.value = res.data.sections.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
}
function getCourseList() {
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    sec_id: queryParams.sec_id || undefined,
  };
  courseLoading.value = true;
  getCourses(params)
    .then((res: any) => {
      courseList.value = res.data.courses.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.published_at = parseTime(
          item.published_at,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
        return item;
      });
      courseListTotal.value = res.total;
      courseLoading.value = false;
    })
    .catch((e) => {
      courseLoading.value = false;
    });
}

function handleSelectCourse() {
  getSecList();
  getCourseList();
  dialog.visible = true;
}
function closeDialog() {
  dialog.visible = false;
  Object.keys(courseForm).forEach((key) => {
    courseForm[key] = "";
  });
}

function selectCourse(selection: any) {
  // let selectRow: any = {};
  // if (selection.length > 1) {
  //   let del_row = selection.shift(); // 删除选中的第一项
  //   courseRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  // }
  // // 到这selection数据始终为1条
  // if (selection.length) {
  //   selectRow = selection[0];
  // }

  const selectMap = selection.map((item: any) => {
    const res = {
      id: item.id,
      thumb: item.thumb,
      title: item.title,
      sections: item.sections,
    };
    return res;
  });
  courseForm.courseData = selectMap;
  // Object.assign(courseForm, {});
}
function selectAllCourses(selection: any[]) {
  const selectMap = selection.map((item: any) => {
    const res = {
      id: item.id,
      thumb: item.thumb,
      title: item.title,
      sections: item.sections,
    };
    return res;
  });
  courseForm.courseData = selectMap;
}
function courseSubmit() {
  // if (!courseForm.expired_at) {
  //   ElMessage.warning("请输入课程截至日期");
  //   return;
  // }
  if (courseForm.courseData.length == 0) {
    ElMessage.warning("请选择课程");
    return;
  }
  let params: any = courseForm.courseData.map((item: any) => {
    // item.expired_at = parseTime(courseForm.expired_at, "{y}-{m}-{d}");
    return item;
  });
  const ids = tableData.value.map((item: any) => {
    return item.id;
  });
  params.forEach((item: any) => {
    if (ids.indexOf(item.id) == -1) tableData.value.push(item);
  });
  setTimeout(() => {
    closeDialog();
  }, 200);
}

function onRowClick(type: any, row: any) {
  if (type == "delete") {
    tableData.value = tableData.value.filter((item: any) => {
      return item.id !== row.id;
    });
  }
}
function handleSubmit() {
  const data: any = {};
  Object.keys(formData).forEach((key) => {
    data[key] = formData[key];
    // if ((key = "expired_at")) {
    //   data[key] = parseTime(formData[key], "{y}{m}{d}235959");
    // }
  });
  data.courses = tableData.value.map((item: any) => {
    return {
      id: item.id,
      accounts: item.accounts,
      reassigned: item.reassigned,
    };
  });
  if (data.courses.length == 0 || !data.courses) {
    ElMessage.warning("请添加课程");
    return;
  }
  let flag = false;
  flag = data.courses.some((item: any) => {
    return !item.accounts;
  });
  if (flag) {
    ElMessage.warning("课程列表中课程账号数不能为空");
    return;
  }
  formRef.value.validate((valid: any) => {
    if (valid) {
      if (pageType.value == "create") {
        addEntCombo(data).then((res: any) => {
          if (res.status == 200) {
            ElMessage.success({
              message: `新增成功!`,
            });
            handleBack();
          }
        });
      }
      if (pageType.value == "edit") {
        updateEntCombo(comboId.value, data).then((res: any) => {
          if (res.status == 200) {
            ElMessage.success({
              message: `修改成功!`,
            });
            handleBack();
          }
        });
      }
    }
  });
}
</script>

<style scoped lang="scss">
.ent-combo-detail-container {
  height: 95%;
  margin: 20px;

  .content-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      justify-content: space-between;
      font-size: 20px;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      color: #3b4664;
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        //font-family: 苹方-简, 苹方-简;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 70px);
    height: 100%;
    padding: 10px 0;
    border-radius: 8px;
    // box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  }

  .base-content {
    position: relative;
    height: 300px;
    padding: 0 0 20px;
    margin-bottom: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .base-form {
      width: 90%;

      .primary-btn {
        width: 217px;
        height: 50px;
        background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
        border-radius: 8px !important;

        .text {
          margin-left: 5px;
        }
      }
    }

    :deep(.el-date-editor) {
      width: 100% !important;
    }

    :deep(.el-form-item) {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  .course-content {
    position: relative;
    height: calc(100% - 300px);
    padding: 20px 40px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .course-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .left {
        font-size: 20px;
        font-weight: 500;
        color: #3b4664;
      }

      .right {
        font-size: 16px;
        font-weight: 400;
        color: #52b0b0;
      }
    }

    .course-list {
      height: calc(100% - 130px);

      .cover-name {
        display: flex;
        align-items: center;

        .cover-img {
          // width: 150px !important;
          // height: 100px !important;
          width: 162px !important;
          height: 86px !important;
          object-fit: cover;
          border-radius: 8px;
        }

        span {
          display: inline-block;
          width: 40%;
        }
      }
    }
  }

  .footer {
    position: absolute;
    right: 40px;
    bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    margin-top: 15px;

    .btn {
      width: 150px;
      height: 50px;
      margin-left: 20px;
      border-radius: 35px;

      &:nth-child(1) {
        margin-left: 0;
      }
    }
  }
}

.course-dialog {
  .table-item {
    width: 100%;

    .table-search {
      display: flex;
      width: 100%;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        width: 22.5%;
        // justify-content: center;
        // align-items: center;
        margin-left: 20px;
        // width: 360px;
        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }
      }

      .primary-btn {
        width: 116px !important;
        margin-left: 20px !important;
      }
    }
    // :deep(.el-table) {
    //   .el-table__header-wrapper {
    //     .el-checkbox {
    //       display: none;
    //       visibility: hidden;
    //     }
    //   }
    // }
  }

  .cover-name {
    display: flex;
    align-items: center;

    .cover-img {
      // width: 150px !important;
      // height: 100px !important;
      width: 162px !important;
      height: 86px !important;
      object-fit: cover;
      border-radius: 8px;
    }

    span {
      display: inline-block;
      width: 40%;
    }
  }

  .table-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
}
</style>
