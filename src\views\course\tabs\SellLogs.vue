<template>
  <div class="sell-logs-table-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入手机号码"
            clearable
            size="large"
          />
        </div>
        <!-- <div class="filter-row">
          <el-input
            v-model="queryParams.orderNumber"
            placeholder="请输入订单号"
            clearable
            size="large"
          />
        </div> -->
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>

        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.status"
            placeholder="请选择订单状态"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="40">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>

        <el-table-column label="订单号" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.order_no }}
          </template>
        </el-table-column>
        <el-table-column label="用户手机" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.user?.mobile || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="实付绿豆" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.total_gb }}
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.remark || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.created_at || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          label="支付时间"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.paid_at || "--" }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { getCourses, getCoursesDetail } from "@/api/course";

import { parseTime, numberToChinese, containsAllElements } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";

// import * as XLSX from "xlsx";
// import { saveAs } from "file-saver";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "SellLogs",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  courseId: {
    type: String,
    default: "",
  },
  courseDetail: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(false);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  status: null,
});

const courseId = ref<any>(props.courseId);
const courseDetail = reactive<any>(props.courseDetail);
const total = ref(0); // 数据总数
const tableData = ref<any>([]);

const statusOptions = ref<any>([
  { value: 10, label: "未支付" },
  { value: 20, label: "已支付" },
  { value: 30, label: "已取消" },
  { value: 40, label: "支付失败" },
]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "未支付", color: "#409eff" },
  20: { type: "success", label: "已支付", color: "#2ab7b0" },
  30: { type: "info", label: "已取消", color: "#909399" },
  40: { type: "danger", label: "支付失败", color: "#f56c6c" },
  // 50: { type: "warning", label: "已过期", color: "#e6a23c" },
});

const depOptions = ref<any>([]);
const depProps = reactive<any>({
  checkStrictly: true,
});

const rowId = ref<any>(null);

onBeforeMount(() => {});
onMounted(() => {
  getData();
});

function optionsLoop(val: any) {
  const res = {
    label: val.name,
    value: val.id,
    children: val.children?.map(optionsLoop) ?? [],
  };
  return res;
}
function getData() {
  loading.value = true;
  const params = {
    course_id: courseId.value,
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.type || undefined,
    status: queryParams.status,
  };
  loading.value = false;
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
</script>

<style scoped lang="scss">
.sell-logs-table-container {
  width: 100%;
  height: 100%;

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        border-radius: 2px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .export-btn {
        width: 116px;
        height: 40px;
        background: linear-gradient(180deg, #0ebc72 0%, #20c27c 100%);
        border-radius: 2px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 120px);
    padding: 10px 20px;

    :deep(.caret-wrapper) {
      transform: scale(1.5) !important;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
