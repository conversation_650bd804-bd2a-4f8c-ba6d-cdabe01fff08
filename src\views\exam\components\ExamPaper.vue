<template>
  <div class="exam-paper-container">
    <div class="questions">
      <component
        :is="questionMap[item.category].component"
        v-for="(item, index) in filteredQuestions"
        :key="index"
        :isOperable="isOperable"
        :showAnswer="showAnswer"
        :showSelectedAnswer="showSelectedAnswer"
        :showGetScore="showGetScore"
        :showQuestionScore="showQuestionScore"
        :showAnswerTime="showAnswerTime"
        :showAnalysis="showAnalysis"
        :showAnswerType="showAnswerType"
        :questionData="item"
        :questionIndex="index"
        @update-answer="updateAnswer"
        @update-score="updateScore"
        @submit="submit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";

import FillBlank from "./FillBlank.vue";
import Judgement from "./Judgement.vue";
import MultipleChoice from "./MultipleChoice.vue";
import SingleChoice from "./SingleChoice.vue";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "ExamPaper",
  inheritAttrs: false,
});

const props = defineProps({
  isOperable: {
    type: Boolean,
    default: false,
  }, //是否只读或答题
  showQuestionScore: {
    type: Boolean,
    default: false,
  }, //是否显示题目分数
  showAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示答案
  showSelectedAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示选中答案
  showGetScore: {
    type: Boolean,
    default: false,
  }, //是否显示得分
  showAnswerTime: {
    type: Boolean,
    default: false,
  }, //是否显示答题时间

  showAnalysis: {
    type: Boolean,
    default: false,
  }, //是否显示解析
  showAnswerType: {
    type: String,
    default: "all", // all-全部, correct-正确, incorrect-错误
  }, //筛选题目答完后答题类型
  paperData: {
    type: Object,
    default: () => {},
  }, //试卷数据
  questionData: {
    type: Object,
    default: () => {
      return {};
    },
  }, //试题数据
});
const emit = defineEmits(["updateAnswer", "updateScore", "submit"]);
const store = useAppStore();
const route = useRoute();
const router = useRouter();

// const paperData = computed(() => props.paperData);
const questionData = computed(() => props.questionData);
const isOperable = computed(() => props.isOperable);
const showAnswer = computed(() => props.showAnswer);
const showSelectedAnswer = computed(() => props.showSelectedAnswer);
const showGetScore = computed(() => props.showGetScore);
const showQuestionScore = computed(() => props.showQuestionScore);
const showAnswerTime = computed(() => props.showAnswerTime);

const showAnalysis = computed(() => props.showAnalysis);
const showAnswerType = computed(() => props.showAnswerType);

const paperDataMock = ref<any>([
  {
    category: 10,
    title: "单选题",
    score: 10,
    questionNum: 4,
    answers: "A",
    content: {
      id: 1,
      category: 10,
      title: "中央空调开机时应按 (    ) 的先后顺序逐个启动，使机组投入运行。",
      options: [
        { A: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { B: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { C: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { D: "压缩机、冷冻泵、冷却泵、冷却塔" },
      ],
      answers: "A",
      remark: "",
    },
  },
  {
    category: 10,
    title: "单选题",
    score: 10,
    questionNum: 4,
    answers: "A",
    content: {
      id: 1,
      category: 10,
      title: "有图片的   (    ) 的先后顺序逐个启动，使机组投入运行。",
      options: [
        { A: "压缩机", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
        { B: "冷冻泵", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
        { C: "冷却泵", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
        { D: "冷却塔", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
      ],

      answers: "A",
      remark:
        " 选项C错误，不能共用立管。选项D:新风机组焓差大室内机焓差小，其冷凝、蒸发压力不一致，不能共用",
    },
  },
  {
    category: 20,
    title: "多选题",
    score: 10,
    questionNum: 4,
    answers: "A,C,D",
    content: {
      id: 2,
      category: 20,
      title:
        "中央空调开机时应按 (    ) 的先后顺序逐个启动，使机组投入运行题目好长好长好长好长好长好长好长好长好长好长长翻行翻行翻行翻行翻行",
      options: [
        { A: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { B: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { C: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { D: "压缩机、冷冻泵、冷却泵、冷却塔" },
      ],

      answers: "A,B",
      remark:
        " 选项C错误，不能共用立管。选项D:新风机组焓差大室内机焓差小，其冷凝、蒸发压力不一致，不能共用",
    },
  },
  {
    category: 30,
    title: "填空题",
    score: 10,
    questionNum: 0,
    answers: "露点2",
    content: {
      id: 4,
      category: 30,
      title: "中央空调开机时应按 (    ) 的先后顺序逐个启动，使机组投入运行。",
      answers: "露点",
      remark: "",
    },
  },
  {
    category: 40,
    title: "判断题",
    score: 10,
    questionNum: 2,
    answers: "A",
    content: {
      id: 4,
      category: 40,
      title: "干湿球温度越大，相对湿度也越大",
      options: [{ A: "对" }, { B: "错" }],
      answers: "A",
      remark:
        " 选项C错误，不能共用立管。选项D:新风机组焓差大室内机焓差小，其冷凝、蒸发压力不一致，不能共用",
    },
  },
]);
const questionMap = reactive<any>({
  10: { component: SingleChoice, label: "单选题" },
  20: { component: MultipleChoice, label: "多选题" },
  30: { component: FillBlank, label: "填空题" },
  40: { component: Judgement, label: "判断题" },
});

const filteredQuestions = computed(() => {
  if (showAnswerType.value === "all") {
    return paperDataMock.value;
  }
  // 假设每个题目有 userAnswer 字段? 且 answers 为标准答案
  return paperDataMock.value.filter((item) => {
    if (showAnswerType.value === "correct") {
      return item.answers === item.content.answers;
    }
    if (showAnswerType.value === "wrong") {
      return item.answers !== item.content.answers;
    }
    return true;
  });
});
const answersData = ref<any>([]);

function updateAnswer(payload: any) {
  emit("updateAnswer", payload);
}

function updateScore(payload: any) {
  emit("updateScore", payload);
}

function submit() {
  emit("submit");
}

onBeforeMount(() => {});
onMounted(() => {});
</script>

<style scoped lang="scss">
.exam-paper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  .questions {
    width: 100%;
  }
}
</style>
