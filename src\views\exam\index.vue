<template>
  <div class="exam-index-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增考试
        </div>
      </div>
    </div>

    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="关联课程" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.bankName }}
          </template>
        </el-table-column>

        <el-table-column label="标题" align="center" min-width="100">
          <template #default="scope">
            {{ getQuestionTypeText(scope.row.questionType) }}
          </template>
        </el-table-column>

        <el-table-column label="试卷发放规则" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.creator }}
          </template>
        </el-table-column>

        <el-table-column label="试卷数量" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.questionType }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.createdAt }}
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" min-width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 10 ? 'success' : 'danger'">
              {{ scope.row.status === 10 ? "启用" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn light-blue-btn"
                @click="onRowClick('edit', scope.row)"
              >
                编辑
              </div>
              <div
                class="btn"
                :class="[
                  scope.row.status == 10 || scope.row.status == 30
                    ? 'light-green-btn'
                    : 'info-btn',
                ]"
                @click="onRowClick('status', scope.row)"
              >
                {{
                  scope.row.status == 10 || scope.row.status == 30
                    ? "启用"
                    : scope.row.status == 20
                    ? "停用"
                    : "--"
                }}
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { status } from "nprogress";
import { useRoute, useRouter } from "vue-router";

import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;

defineOptions({
  name: "ExamIndex",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const tableData = ref<any>([
  {
    id: 1,
    bankName: "安全  ",
    questionType: 1, // 1-单选题, 2-多选题, 3-判断题, 4-填空题, 5-简答题
    creator: "管理员",
    createdAt: "2024-03-20 10:13:21",
    status: 10, // 1-启用, 0-禁用
  },
  {
    id: 2,
    bankName: "行业 ",
    questionType: 2,
    creator: "张三",
    createdAt: "2024-03-19 15:30:45",
    status: 10,
  },
  {
    id: 3,
    bankName: "职业",
    questionType: 3,
    creator: "李四",
    createdAt: "2024-03-18 09:25:30",
    status: 10,
  },
  {
    id: 4,
    bankName: "节能",
    questionType: 4,
    creator: "王五",
    createdAt: "2024-03-17 14:18:12",
    status: 20,
  },
  {
    id: 5,
    bankName: "应急管理",
    questionType: 1,
    creator: "赵六",
    createdAt: "2024-03-16 11:42:55",
    status: 10,
  },
]);

const rowId = ref<any>(0);
// 10-单选题，20-多选题，30-填空题，40-判断题
const questionTypeOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "判断题", value: 30 },
  { label: "填空题", value: 40 },
]);
// 题目类型映射
const questionTypeMap = reactive<any>({
  10: "单选题",
  20: "多选题",
  30: "判断题",
  40: "填空题",
});

onBeforeMount(() => {});

onMounted(() => {
  getData();
});

function getQuestionTypeText(type: any) {
  return questionTypeMap[type] || "未知";
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  console.log("新增");
  router.push({ path: "exam-action", query: { type: "create" } });
}

function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  switch (type) {
    case "status":
      const status =
        row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
      const message =
        row.status == 10 || row.status == 30
          ? "启用成功"
          : row.status == 20
          ? "停用成功"
          : "";
      const text = row.status == 20 ? "停用" : "启用";

      ElMessageBox.confirm("此操作将" + text + "该考试，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //
      });
      break;
    case "detail":
      console.log("详情 ", row);
      router.push({
        path: "exam-detail",
        query: { id: row.id, type: "detail" },
      });
      break;
    case "edit":
      console.log("编辑 ", row);
      router.push({ path: "exam-action", query: { id: row.id, type: "edit" } });
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除考试"${row.bankName}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      console.log("删除", row);
      ElMessage.success("删除成功");
      getData();
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

function getData() {
  loading.value = true;

  setTimeout(() => {
    total.value = tableData.value.length;
    loading.value = false;
  }, 500);
}
</script>

<style scoped lang="scss">
.exam-index-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
