<template>
  <!-- 获取图片组件，从素材库中获取 -->
  <div class="single-uploader">
    <img v-if="imgUrl" :src="imgUrl" class="single-uploader__image" />
    <!-- + '?x-image-process=style/style-720' -->
    <div v-if="imgUrl" class="img-upload__overlay">
      <el-icon
        class="img-upload__preview-icon upload-icon"
        @click.stop="handlePreview"
      >
        <i-ep-zoom-in />
      </el-icon>
      <el-icon
        class="img-upload__delete-icon upload-icon"
        @click.stop="handleDelete"
      >
        <i-ep-delete />
      </el-icon>
    </div>
    <el-icon v-else class="single-uploader__icon" @click="handleResourceDialog"
      ><i-ep-plus />
    </el-icon>
  </div>
  <span class="tips" v-if="type == 'course'">建议尺寸：786 x 422 px</span>

  <el-dialog
    class="previewImgDialog"
    v-model="dialogPreview.visible"
    append-to-body
    :width="'60%'"
  >
    <!-- <img w-full :src="previewImgUrl" alt="Preview Image" /> -->
    <div class="preview-img">
      <el-scrollbar warp-style="overflow-x: hidden;">
        <img w-full :src="previewImgUrl" alt="Preview Image" />
      </el-scrollbar>
    </div>
  </el-dialog>

  <!-- 资源弹窗 -->
  <el-dialog
    class="imgDialog"
    v-model="imgDialog.visible"
    :title="imgDialog.title"
    :width="imgDialog.width"
    append-to-body
    @close="closeImgDialog"
  >
    <div class="dialog-body">
      <el-form>
        <el-form-item label="图片名称">
          <el-col :span="12">
            <el-input
              v-model="currentImg.name"
              placeholder="图片名称"
              clearable
              size="large"
            />
          </el-col>
        </el-form-item>
        <el-form-item class="table-item">
          <div class="table-search">
            <div class="filter-row">
              <el-input
                v-model="queryParams.search"
                placeholder="请输入关键字"
                clearable
                size="large"
              />
            </div>
            <div class="btn primary-btn" @click="handleQuery">
              <i-ep-search /> 搜索
            </div>
          </div>
          <el-table
            v-loading="loading"
            element-loading-text="Loading"
            element-loading-background="#ffffffb4"
            :data="tableData"
            height="20rem"
            border
            fit
            highlight-current-row
            ref="tableRef"
            @select="selectItem"
          >
            <el-table-column
              type="selection"
              align="center"
              min-width="20"
              class="dialog-checkbox2"
            />

            <el-table-column
              label="名称"
              align="center"
              prop="name"
              min-width="80"
            >
              <template #default="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="大小"
              align="center"
              prop="name"
              min-width="40"
            >
              <template #default="scope">
                <span>{{ scope.row.size }}Mb</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="table-footer">
            <pagination
              v-if="total > 0"
              v-model:total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="handleQuery"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div class="btn cancel-btn" @click="closeImgDialog">取 消</div>
        <div class="btn primary-btn" @click="handleSubmit">确 定</div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// import { UploadRawFile, UploadRequestOptions } from "element-plus";
// import { uploadFileApi } from "@/api/file";

//素材列表接口
import { getResources } from "@/api/resource";
import { parseTime } from "@/utils";
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  imgUrl: {
    type: String,
    default: "",
  },
  gkResource: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:imgUrl", "update:gkResource"]);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  res_type: 20,
  // gid: "",
});
const total = ref(0); // 数据总数
const tableData = ref<any>([]);
const loading = ref(false);
const imgDialog = reactive({
  visible: false,
  title: "素材-图片",
  width: "60%",
});
const tableRef = ref();

const currentImg = reactive<any>({});

const imgUrl = useVModel(props, "imgUrl", emit);
const gkResource = useVModel(props, "gkResource", emit);

const previewImgUrl = computed(() => imgUrl.value);

const dialogPreview = reactive({
  visible: false,
});

// onMounted(() => {
//   getData();
// });
watch(
  () => imgDialog.visible,
  (val) => {
    if (val == true) {
      getData();
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => imgUrl.value,
  (val) => {
    console.log("imgUrl", val);
    console.log("gkResource", gkResource.value);
  }
);
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.res_type,
  };
  getResources(params).then((res: any) => {
    tableData.value = res.data.resources.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      // item.size = (item.size / 1024 / 1024)?.toFixed(1);
      item.size = (item.size / 1024 / 1024)?.toFixed(2);
      if (item.res_type !== 10) {
        item.size = item.res_info.size
          ? (item.res_info.size / 1024 / 1024)?.toFixed(2)
          : (item.size / 1024 / 1024)?.toFixed(2);
      }
      return item;
    });
    total.value = res.total;
    loading.value = false;
  });
}
function handleQuery() {
  getData();
}
function handleResourceDialog() {
  imgDialog.visible = true;
}
function closeImgDialog() {
  tableRef.value.clearSelection();
  Object.assign(currentImg, {
    name: "",
    res_info: "",
    id: "",
  });
  Object.assign(queryParams, {
    search: "",
    pageNum: 1,
    pageSize: 20,
    res_type: 20,
    // gid: "",
  });

  imgDialog.visible = false;
}
function handleSubmit() {
  const res_info: any =
    typeof currentImg.res_info === "string"
      ? JSON.parse(currentImg.res_info)
      : currentImg.res_info;
  imgUrl.value = res_info["url"];
  gkResource.value = currentImg.id;
  closeImgDialog();
}
function selectItem(selection: any) {
  let selectRow: any = {};
  if (selection.length > 1) {
    let del_row = selection.shift(); // 删除选中的第一项
    tableRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  }
  // 到这selection数据始终为1条
  if (selection.length) {
    selectRow = selection[0];
  }
  Object.assign(currentImg, {
    name: selectRow.name,
    res_info: selectRow.res_info,
    id: selectRow.id,
  });
}
function handleDelete() {
  if (imgDialog.visible) {
    tableRef.value.clearSelection();
  }
  Object.assign(currentImg, {
    name: "",
    res_info: "",
    id: "",
  });
  imgUrl.value = "";
  gkResource.value = "";
}

// 预览
function handlePreview() {
  dialogPreview.visible = true;
}
</script>

<style scoped lang="scss">
.single-uploader {
  position: relative;
  width: 146px;
  height: 146px;
  overflow: hidden;
  cursor: pointer;
  border: 1px var(--el-border-color) solid;
  border-radius: 6px;

  .single-uploader__image {
    position: relative;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .img-upload__overlay {
    position: absolute;
    top: 60px;
    left: 38px;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 80px;
    opacity: 0.5;

    .upload-icon {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      cursor: pointer;

      &:hover {
        color: #00ff80;
      }
    }
  }

  .single-uploader__icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  &:hover {
    border-color: var(--el-color-primary);
  }
}

.tips {
  margin-left: 10px;
  font-size: 12px;
  font-weight: 400;
  color: #8f9bb2 !important;
}

.imgDialog {
  .table-item {
    width: 100%;

    .table-search {
      display: flex;
      width: 100%;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 360px;
        margin-left: 20px;

        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }
      }

      .primary-btn {
        width: 116px !important;
        margin-left: 20px !important;
        // height: 40px !important;
      }
    }

    :deep(.el-table) {
      .el-table__header-wrapper {
        .el-checkbox {
          display: none;
          visibility: hidden;
        }
      }
    }

    .table-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
  }
}

.preview-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  :deep(.el-scrollbar__wrap) {
    max-height: 60vh;
  }
}
</style>
