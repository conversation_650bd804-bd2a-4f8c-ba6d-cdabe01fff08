<template>
  <div class="ent-order-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="关键字"
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.ent_id"
            placeholder="企业名称"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in entOptions"
              :key="item.value"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.status"
            placeholder="订单状态"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <!-- <div class="filter-row">
          <el-date-picker
            class="date-picker"
            size="large"
            v-model="queryParams.dateTimeRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            @change="handleQuery"
          />
        </div> -->
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增订单
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" width="50">
          <template #default="scope">
            {{
              scope.$index +
              1 +
              (queryParams.pageNum - 1) * queryParams.pageSize
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="企业名称"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="套餐名称" align="center" min-width="120">
          <template #default="scope">
            <span class="package-name">
              {{
                scope.row.packages?.length > 0
                  ? scope.row.packages.map((item: any) => item.name).toString()
                  : "--"
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="价格（￥）" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.price || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="订单号" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.order_number || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column
          label="审核时间"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="套餐状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag
              :type="comboStatusMap[scope.row.status]?.type"
              effect="plain"
              >{{ comboStatusMap[scope.row.status]?.label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type" effect="plain">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn verify-btn"
                @click="onRowClick('verify', scope.row)"
              >
                审核
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
              >
                撤销
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="order-dialog"
      v-model="orderDialog.visible"
      :title="orderDialog.title"
      :width="orderDialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form
          ref="orderFormRef"
          :model="orderForm"
          class="order-form"
          :rules="formRules"
        >
          <el-form-item
            label="企业名称"
            :prop="orderDialog.type === 'create' ? 'name' : ''"
          >
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="orderForm.ent_id"
              placeholder="请选择企业名称"
              filterable
              clearable
              v-if="orderDialog.type === 'create'"
            >
              <el-option
                v-for="item in entOptions"
                :key="item.value"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
            <div v-else class="ent-name">{{ orderDetail.name }}</div>
          </el-form-item>
          <el-form-item
            label="套餐名称"
            :prop="orderDialog.type === 'create' ? 'package_id' : ''"
          >
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="orderForm.package_id"
              placeholder="请选择套餐名称"
              filterable
              clearable
              multiple
              v-if="orderDialog.type === 'create'"
            >
              <el-option
                v-for="item in comboOptions"
                :key="item.value"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
            <template v-else>
              <el-scrollbar class="package-list" :max-height="200">
                <div class="package-name">我是套餐名字</div>
                <div class="package-name">
                  我是套餐名字，多个套餐会行显示，套餐名
                  称最多两行啊，多了就显示省略号啦！啦…
                </div>
              </el-scrollbar>
            </template>
          </el-form-item>
          <el-form-item
            label="审核结果"
            prop="review_result"
            v-if="orderDialog.type === 'verify'"
          >
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="orderForm.review_result"
              placeholder="请选择审核结果"
              filterable
              clearable
            >
              <el-option
                v-for="item in reviewOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="tips" v-if="orderDialog.type === 'delete'">
          <img :src="warningImg" alt="" />
          <span>套餐取消后无法恢复，请确认是否取消套餐！</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  secondsToHoursAndMinutes,
  resetReactiveObject,
} from "@/utils";
import { getEnterprises, getEntCombo } from "@/api/enterprise";
import { useUserStore } from "@/store/modules/user";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "EntOrder",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const loading = ref(false);
const warningImg = new URL("@/assets/images/warning.png", import.meta.url).href;
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 20,
  type: "",
  status: "",
  search: "",
});
const statusOptions = ref<any>([
  { value: 10, label: "正常" },
  { value: 20, label: "撤销" },
]);
const reviewOptions = ref<any>([
  { value: 10, label: "审核通过" },
  { value: 20, label: "审核不通过" },
]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "待审核" },
  20: { type: "success", label: "审核通过" },
  30: { type: "warning", label: "审核不通过" },
  40: { type: "danger", label: "已取消" },
  41: { type: "danger", label: "部分取消" },
  // add more status mappings as needed
});
const comboStatusMap = reactive<any>({
  10: { type: "primary", label: "正常" },
  20: { type: "success", label: "已过期" },
  30: { type: "info", label: "撤销" },
  40: { type: "danger", label: "--" },
  // add more status mappings as needed
});
const total = ref<any>(0); // 数据总数
const tableData = ref<any>([]);
const rowId = ref<any>(null);
const entOptions = ref<any>([]); // 企业名称下拉列表
const comboOptions = ref<any>([]); // 套餐名称下拉列表
const orderForm = reactive<any>({});
const orderFormRef = ref<any>(null);
const orderDetail = reactive<any>({}); // 订单详情
const orderDialog = reactive<any>({
  visible: false,
  title: "新增订单",
  width: "30%",
  type: "create",
});
const formRules = reactive<any>({
  name: [{ required: true, message: "请选择企业", trigger: "blur" }],
  package_id: [{ required: true, message: "请选择套餐名称", trigger: "blur" }],
  review_result: [
    { required: true, message: "请选择审核结果", trigger: "blur" },
  ],
});

onBeforeMount(() => {});
onMounted(async () => {
  await getOptionsData();
  await getData();
});

async function getOptionsData() {
  const params: any = {
    page: 1,
    per_page: 999,
  };
  await getEntCombo(params).then((res: any) => {
    if (res.status == 200) {
      comboOptions.value = res.data.packages.map((item: any) => {
        item.label = item.name;
        return item;
      });
    }
  });
  await getEnterprises(params).then((res: any) => {
    if (res.status == 200) {
      entOptions.value = res.data.enterprises.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.label = item.name;
        return item;
      });
    }
  });
}
function getData() {}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  orderDialog.visible = true;
  orderDialog.type = "create";
  orderDialog.title = "新增订单";
}
function onRowClick(type: string, row: any) {
  if (type === "detail") {
    router.push({
      path: `ent-order-detail`,
      query: { id: row.id, type: "detail" },
    });
  } else if (type === "verify") {
    rowId.value = row.id;
    Object.assign(orderDetail, row);
    orderDialog.visible = true;
    orderDialog.type = "verify";
    orderDialog.title = "企业套餐审核";
    // 审核订单
  } else if (type === "delete") {
    rowId.value = row.id;
    Object.assign(orderDetail, row);
    orderDialog.visible = true;
    orderDialog.type = "delete";
    orderDialog.title = "取消套餐";
  }
}

function handleSubmit() {
  orderFormRef.value.validate(async (valid: boolean, fields: any) => {
    console.log("valid", valid, "fields", fields);
    if (valid) {
      const params: any = {
        id: rowId.value,
      };
      if (orderDialog.type === "create") {
        // 新增
      } else if (orderDialog.type === "verify") {
        // 审核
      } else if (orderDialog.type === "delete") {
        // 撤销
      }
    } else {
      return false;
    }
  });
}
function closeDialog() {
  orderDialog.visible = false;
  rowId.value = null;
  resetReactiveObject(orderDetail);
  resetReactiveObject(orderForm);
  orderFormRef.value.clearValidate(); // 清除表单验证状态
}
</script>

<style scoped lang="scss">
.ent-order-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    // height: calc(100% - 170px);
    height: calc(100% - 240px);
    padding: 10px 20px;

    .package-name {
      display: -webkit-box; /* 必须结合 line-clamp 使用 */
      max-height: 3em; /* 行高 * 行数 (1.5 * 2 = 3em) */
      overflow: hidden; /* 隐藏超出内容 */
      line-height: 1.5; /* 设置行高 */
      text-overflow: ellipsis; /* 显示省略号 */
      -webkit-box-orient: vertical; /* 设置盒子垂直排列 */
      -webkit-line-clamp: 2; /* 限制显示两行 */
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.order-dialog {
  .dialog-body {
    padding: 5px 20px;
  }

  .ent-name {
    font-size: 17px;
    font-weight: 500;
    color: #3b4664;
    background: hsl(180deg 3% 74% / 43%);
    border-radius: 12px;
  }

  .package-name {
    display: inline-block;
    width: auto;
    padding: 5px 10px;
    margin: 5px 0;
    font-size: 17px;
    font-weight: 500;
    color: #3b4664;
    background: hsl(180deg 3% 74% / 43%);
    border-radius: 12px;
  }

  .tips {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 19px;
    font-weight: 500;
    color: #3b4664 !important;

    img {
      width: 47px;
      height: 45px;
      margin-right: 5px;
    }
  }
}
</style>
