/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path: any) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str: any) {
  // const valid_map = ['18680687453', 'editor']
  return str.length === 11;
  // return valid_map.indexOf(str.trim()) >= 0
}

export function validMobile(str: any) {
  const mobileReg = /^[1][3456789][0-9]{9}$/;
  return mobileReg.test(str);
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url: any) {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return reg.test(url);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str: any) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str: any) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str: any) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email: any) {
  const reg =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return reg.test(email);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str: any) {
  if (typeof str === "string" || str instanceof String) {
    return true;
  }
  return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg: any) {
  if (typeof Array.isArray === "undefined") {
    return Object.prototype.toString.call(arg) === "[object Array]";
  }
  return Array.isArray(arg);
}

/**
 * 校验密码，不能小于8位字符
 */
export function validPsw(psw: any) {
  if (psw) {
    if (psw.length < 8) {
      return false;
    } else {
      return true;
    }
  }
}

/**
 * 判断是否只为数字
 */
export function validNum(val: any) {
  const reg = /^[0-9]+.?[0-9]*$/;
  return reg.test(val);
}

/**
 * 判断是否为浮点数
 */
export function validFloat(val: any) {
  const reg = /^-?([1-9]d*.d*|0.d*[1-9]d*|0?.0+|0)$/;
  return reg.test(val);
}

/**
 * 判断是否为步进值（当前为字母+数字）
 */
export function validStepValue(val: any) {
  const reg = /^[A-Za-z0-9]+$/;
  return reg.test(val);
}

/**
 * 判断是否为单位（当前为字母+数字）
 */
export function validUnit(val: any) {
  const reg = /^[A-Za-z0-9]+$/;
  return reg.test(val);
}

/**
 * 判断字符串中是否存在中文
 */
export function validIsEnglish(val: any) {
  const reg = /^[^\u4e00-\u9fa5]+$/;
  return reg.test(val);
}

export function validateUsername(rule: any, value: any, callback: any) {
  if (!validUsername(value)) {
    callback(new Error("请输入正确的手机号"));
  } else {
    callback();
  }
}
export function validateMobile(rule: any, value: any, callback: any) {
  if (!validMobile(value)) {
    callback(new Error("请输入正确的手机号"));
  } else {
    callback();
  }
}

export function validatePassword(rule: any, value: any, callback: any) {
  const reg =
    /^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?\d)(?=.*?[*?!&￥$%^#,./@";:><}{\-=+_\\|》《。，、？’‘“”~ `])[a-zA-Z\d!#@*&.]*$/;
  if (value.length < 6) {
    callback(new Error("密码不能少于8位"));
  } else if (!reg.test(value)) {
    callback(new Error("密码必须包含大小写英文字母 + 数字 + 特殊字符"));
  } else {
    callback();
  }
}
export function validateCaptcha(rule: any, value: any, callback: any) {
  if (value.length !== 4) {
    callback(new Error("请输入4位图形验证码"));
  } else {
    callback();
  }
}
