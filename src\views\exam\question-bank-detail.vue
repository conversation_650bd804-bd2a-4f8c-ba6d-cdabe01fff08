<!--
 * @Author: czf <EMAIL>
 * @Date: 2025-06-11 10:15:53
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2025-06-11 10:16:45
 * @FilePath: \management-platform\src\views\exam\question-bank-detail.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="exam-action-container">
    <div class="container-header">
      <div class="left">
        <span>考试--{{ typeMap[type].label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">1</div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "ExamAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const examId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

onBeforeMount(() => {});
onMounted(() => {});

function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;

        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
    .content {
      width: 100%;
      height: calc(100% - 70px);
      padding: 10px 0;
    }
  }
}
</style>
