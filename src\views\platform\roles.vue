<template>
  <div class="roles-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn"><i-ep-search /> 搜索</div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增角色
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <!-- <el-table-column label="ID" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column> -->
        <el-table-column label="序号" align="center" min-width="40">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="角色" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row?.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.creator?.name || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
                :style="{
                  pointerEvents: enableAction(scope.row.name) ? 'auto' : 'none',
                  opacity: enableAction(scope.row.name) ? '1' : '0.5',
                }"
              >
                修改
              </div>
              <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn delete-btn"
                @click="handelDelete(scope.row)"
                :style="{
                  pointerEvents: enableAction(scope.row.name) ? 'auto' : 'none',
                  opacity: enableAction(scope.row.name) ? '1' : '0.5',
                }"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-scrollbar max-height="30rem" warp-style="overflow-x: hidden;">
          <el-form
            label-width="6rem"
            :model="formData"
            :rules="formDataRules"
            ref="formRef"
            :style="{
              pointerEvents: dialog.type == 'detail' ? 'none' : 'auto',
            }"
          >
            <el-form-item label="角色名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入角色名称"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="目录权限" prop="menu_ids">
              <el-tree
                class="dialog-tree menu-tree"
                :props="menuProps"
                ref="menusTreeRef"
                :data="menuData"
                :default-expand-all="true"
                highlight-current
                node-key="id"
                show-checkbox
                :default-checked-keys="formData.menu_ids"
                @check-change="handleWebMenusCheckedChange"
                @check="handleCheck"
              />
            </el-form-item>
            <el-form-item label="角色权限" prop="permissions">
              <el-tree
                class="dialog-tree permission-tree"
                :props="permissionsProps"
                ref="permissionsTreeRef"
                :data="rolePermissions"
                :default-expand-all="true"
                highlight-current
                node-key="value"
                show-checkbox
                :default-checked-keys="formData.permissions"
                @check-change="handlePermissionsCheckedChange"
                @check="handlePermissionsCheck"
              />
            </el-form-item>
            <el-form-item label="备注信息">
              <el-input
                v-model="formData.remark"
                placeholder="备注信息"
                clearable
                size="large"
              />
            </el-form-item>
          </el-form>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="dialog.type != 'detail'">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
        <div class="dialog-footer" v-if="dialog.type == 'detail'">
          <div class="btn cancel-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  getSysRoles,
  deleteSysRoles,
  addSysRoles,
  updateSysRoles,
  getSysMenus,
} from "@/api/system";
import { parseTime } from "@/utils";
import { validateMobile, validEmail } from "@/utils/validate";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Roles",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const userInfo = computed(() => {
  return userStore.userInfo;
});

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref<any>([]);
const menu_ids = ref<any>([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "30%",
  title: "新增角色",
});

// 表单数据
const rowId = ref();
const menusTreeRef = ref<any>();
const menuData = ref<any>([]);
const formData = reactive<any>({
  name: "",
  permissions: [], //权限
  remark: "",
  menu_ids: [],
});

const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  // mobile: [
  //   {
  //     required: true,
  //     trigger: "blur",
  //     validator: validateMobile,
  //   },
  // ],
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  menu_ids: [{ required: true, message: "请选择目录权限", trigger: "blur" }],
});
const permissionsTreeRef = ref<any>();
const rolePermissions = ref<any>([
  { label: "课程删除", value: 1 },
  { label: "素材删除", value: 2 },
  { label: "课程金额修改", value: 3 },
  { label: "用户冻结解冻", value: 4 },
  { label: "绿豆赠送", value: 5 },
  { label: "课程上下架", value: 6 },
  { label: "限时免费删除", value: 7 },
  { label: "限时免费启用停用", value: 8 },
  { label: "分类删除", value: 9 },
  { label: "banner删除", value: 10 },
  { label: "banner上下架", value: 11 },
  { label: "栏目删除", value: 12 },
  { label: "企业冻结解冻", value: 13 },
  { label: "企业注册审批", value: 14 },
  { label: "企业删除", value: 15 },
  { label: "企业课程上下架", value: 16 },
  { label: "企业套餐新增", value: 17 },
  { label: "企业套餐审批", value: 18 },
  { label: "企业套餐上下架", value: 19 },
  { label: "企业套餐删除", value: 20 },
  { label: "消息删除", value: 21 },
  { label: "素材分组删除", value: 22 },
  { label: "课程赠送", value: 23 },
]);
const permissions = ref<any>(
  "0000000000000000000000000000000000000000000000000000000000000000"
);
const permissionsProps = reactive<any>({
  //自定义label
  label: (data: { label: any }) => {
    return data.label; // name为你要显示的名称 可以自定义，就是将name替换label
  },
  children: "children",
});
const menuProps = reactive<any>({
  //自定义label
  label: (data: { name: any }) => {
    return data.name; // name为你要显示的名称 可以自定义，就是将name替换label
  },
  children: "children",
});
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getMenus();
  getData();
});
function enableAction(val: any) {
  if (val == "超级管理员") {
    if (userInfo.value.role == "超级管理员") {
      return true;
    } else {
      return false;
    }
  } else {
    return true;
  }
}

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getSysRoles(params).then((res: any) => {
    if (res.status == 200) {
      tableData.value = res.data.sys_roles.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
function getMenus() {
  loading.value = true;
  const params = {
    page: 1,
    per_page: 999,
  };
  getSysMenus(params)
    .then((res: any) => {
      if (res.status == 200) {
        menuData.value = res.data.menus.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          if (item.children && item.children.length > 0) {
            item.children.map((itm: any) => {
              itm.created_at = parseTime(
                itm.created_at,
                "{y}-{m}-{d} {h}:{i}:{s}"
              );
              return itm;
            });
          }
          return item;
        });
      }
    })
    .catch((e) => {});
}

function getMenusId(val: any) {
  let res: any = [];
  val.forEach((item: any) => {
    if (!item.children) {
      res.push(item.id);
    }
    if (item.children && item.children.length > 0) {
      item.children.forEach((itm: any) => {
        res.push(itm.id);
      });
    }
  });
  return res;
}

function getAllMenusId(val: any) {
  let res: any = [];
  val.forEach((item: any) => {
    if (!res.includes(item.id)) {
      res.push(item.id);
    }
    if (item.children && item.children.length > 0) {
      item.children.forEach((itm: any) => {
        if (!res.includes(itm.id)) {
          res.push(itm.id);
        }
      });
    }
  });
  return res;
}

function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增角色";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  if (type === "edit") {
    dialog.visible = true;
    dialog.title = "编辑角色";
    dialog.type = "edit";
    rowId.value = row.id;
    formData.id = row.id;
    formData.name = row.name;
    formData.remark = row.remark;
    formData.menu_ids = getMenusId(row.menus);
    menu_ids.value = getAllMenusId(row.menus);
    setTimeout(() => {
      if (formData.menu_ids && formData.menu_ids.length > 0) {
        handleTreeChange(formData.menu_ids);
      }
      if (row.permissions) {
        permissions.value = row.permissions;
        handleDefaultPermissions(row.permissions);
      }
    }, 500);
  }
  if (type === "delete") {
    handelDelete(row);
  }
  if (type === "detail") {
    dialog.type = "detail";
    dialog.visible = true;
    dialog.title = "角色详情";
    rowId.value = row.id;
    formData.id = row.id;
    formData.name = row.name;
    formData.remark = row.remark;
    formData.menu_ids = getMenusId(row.menus);
    setTimeout(() => {
      if (formData.menu_ids && formData.menu_ids.length > 0) {
        handleTreeChange(formData.menu_ids);
      }

      if (row.permissions) {
        permissions.value = row.permissions;
        handleDefaultPermissions(row.permissions);
      }
    }, 500);
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除角色，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteSysRoles(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "删除成功!",
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  getData();
}

function closeDialog() {
  dialog.visible = false;
  proxy?.$refs.menusTreeRef.setCheckedKeys([]);
  proxy?.$refs.permissionsTreeRef.setCheckedKeys([]);
  formRef.value.clearValidate();
  Object.assign(formData, {
    name: "",
    permissions: [], //权限
    remark: "",
    menu_ids: [],
  });
  permissions.value = "0000000000";
}

function handleTreeChange(val: any) {
  val.forEach((item: any) => {
    var node = proxy?.$refs.menusTreeRef.getNode(item);
    proxy?.$refs.menusTreeRef.setChecked(node, true);
  });
}
function handleCheck(data, checkedNodes) {
  const alreadyCheck: any = menusTreeRef.value.getCheckedNodes();
  formData.menu_ids = alreadyCheck.map((item: any) => {
    return item.id;
  });

  // 另外定义，避免覆盖
  menu_ids.value = checkedNodes.checkedKeys.concat(
    checkedNodes.halfCheckedKeys
  );
}
function handleWebMenusCheckedChange(
  data: any,
  checked: any,
  indeterminate: any
) {
  // console.log("status", checked);
}

function handleDefaultPermissions(val: any) {
  const arr = val.split("");
  arr.forEach((item: any, index: any) => {
    if (item == 1) {
      const key = index + 1;
      formData.permissions.push(key);
      var node = proxy?.$refs.permissionsTreeRef.getNode(key);
      proxy?.$refs.permissionsTreeRef.setChecked(node, true);
    }
  });
}
function handlePermissionsCheck() {
  const alreadyCheck: any = permissionsTreeRef.value.getCheckedNodes();
  const val = alreadyCheck.map((item: any) => {
    return item.value;
  });
  const temp: any = [];
  rolePermissions.value.forEach((v) => {
    if (val.includes(v.value)) {
      temp.push(1);
    } else {
      temp.push(0);
    }
  });
  formData.permissions = temp;
  permissions.value = temp.join("").concat("00000");
  console.log(formData.permissions, permissions.value);
}
function handlePermissionsCheckedChange(
  data: any,
  checked: any,
  indeterminate: any
) {
  // console.log("status", checked);
}

function handleSubmit() {
  const data = {
    name: formData.name,
    remark: formData.remark || undefined,
    menu_ids: menu_ids.value.filter((item: any) => {
      return item !== null;
    }),
    permissions: permissions.value,
  };
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      if (dialog.type === "create") {
        addSysRoles(data).then((res: any) => {
          if (res.status == 200) {
            ElMessage.success({
              message: "创建成功!",
            });
            closeDialog();
            getData();
          }
        });
      }
      if (dialog.type === "edit") {
        updateSysRoles(rowId.value, data).then((res: any) => {
          if (res.status == 200) {
            ElMessage.success({
              message: "修改成功!",
            });
            closeDialog();
            getData();
          }
        });
      }
    }
  });
}
</script>

<style scoped lang="scss">
.roles-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
