<template>
  <div class="mung-bean-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <
          :suffix-icon="`CaretBottom`"
            size="large"
            v-model="query.status"
            placeholder="请选择状态"
            filterable
            clearable
            @change="handleChange"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </>
        </div> -->
      </div>
      <div class="right">
        <div class="btn green-btn2" @click="handleList">
          <i-ep-circle-plus style="margin-right: 5px" />
          一键对账
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="50">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.user.name || "绿知学员" + scope.row.user.id }}
          </template>
        </el-table-column>
        <el-table-column label="头像" align="center" min-width="60">
          <template #default="scope">
            <div class="cover-name">
              <img
                :src="scope.row.user.avatar || userStore.defaultAvatar"
                alt=""
                class="cover-img"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="增加/减少"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.op }}
          </template>
        </el-table-column>

        <el-table-column label="绿豆数" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.gb_amount }}
          </template>
        </el-table-column>
        <el-table-column
          label="事由"
          align="center"
          prop="status"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.reason }}
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <!-- <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-table
          :data="formData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column
            label="用户账号"
            align="center"
            prop="mobile"
            min-width="70"
          >
            <template #default="scope">
              {{ scope.row.mobile }}
            </template>
          </el-table-column>
          <el-table-column
            label="增加减少"
            align="center"
            prop="mobile"
            min-width="120"
          >
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>

          <el-table-column
            label="绿豆数"
            align="center"
            prop="mobile"
            min-width="120"
          >
            <template #default="scope">
              {{ scope.row.mobile }}
            </template>
          </el-table-column>
          <el-table-column
            label="事由"
            align="center"
            prop="status"
            min-width="120"
          >
            <template #default="scope">
              <el-tag :type="scope.row.status == 1 ? 'success' : 'info'">{{
                scope.row.status == 1 ? "上架" : "禁用"
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="时间"
            align="center"
            prop="createTime"
            min-width="120"
          >
            <template #default="scope">
              {{ scope.row.createTime }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import { getMungBean, settleMungBean } from "@/api/mung-bean";
import { parseTime } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "MungBean",
  inheritAttrs: false,
});
/** 仓库*/
const userStore = useUserStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  from: "",
  to: "",
  op: "",
  uid: "",
  mobile: "",
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref([]);

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "55%",
  title: "详情",
});

// 用户表单数据
const rowId = ref();
const formData = ref<any>([]);

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function getData() {
  loading.value = true;
  const params = {
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    search: queryParams.search || undefined,
  };
  getMungBean(params)
    .then((res: any) => {
      if (res.status == 200) {
        tableData.value = res.data.bg_cons.map((item: any) => {
          item.created_at = parseTime(
            item.created_at,
            "{y}-{m}-{d} {h}:{i}:{s}"
          );
          return item;
        });
        total.value = res.total;
        loading.value = false;
      }
    })
    .catch((e) => {
      loading.value = false;
    });
}
function handleList() {
  router.push({
    path: "bean-reconciliation",
    query: { type: "reconciliation" },
  });
}
function onRowClick(type: any, row: any) {
  if (type == "detail") {
    dialog.visible = true;
    dialog.title = "详情";
    dialog.type = "detail";
    formData.value = row.bg_cons;
    rowId.value = row.id;
  }
}

function closeDialog() {
  dialog.visible = false;
}
</script>

<style scoped lang="scss">
.mung-bean-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  .after-header {
    width: 100%;
    padding: 20px 20px 20px 20px;
  }
  .container-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 20px 20px;
    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 60%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      .btn {
        width: 120px;
        height: 38px;
      }
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
  }
  .content {
    // height: calc(100% - 240px);
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
    .cover-name {
      display: flex;
      align-items: center;
      .cover-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }
      span {
        width: 40%;
        display: inline-block;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
.dialog {
  .dialog-body {
    height: 60vh;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-top: 15px;
  }
}
</style>
