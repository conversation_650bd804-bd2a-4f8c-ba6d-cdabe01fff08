<template>
  <div class="exam-action-container">
    <div class="container-header">
      <div class="left">
        <span>考试--{{ typeMap[type].label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>

    <div class="content">
      <div class="exam-layout">
        <!-- 左侧考试配置区域 -->
        <div class="exam-config">
          <div class="config-header">
            <h3>考试配置</h3>
            <el-button type="text" @click="toggleConfig">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>

          <div class="config-content">
            <!-- 考试基本信息 -->
            <div class="config-section">
              <div class="section-title">
                <el-icon class="section-icon"><Document /></el-icon>
                <span>考试基本信息</span>
              </div>
              <div class="form-content">
                <el-form :model="examForm" label-width="100px" size="default">
                  <el-form-item label="考试名称">
                    <el-input
                      v-model="examForm.name"
                      placeholder="请输入考试名称"
                    />
                  </el-form-item>
                  <el-form-item label="考试时长">
                    <el-input-number
                      v-model="examForm.duration"
                      :min="1"
                      :max="300"
                      placeholder="分钟"
                    />
                    <span class="unit">分钟</span>
                  </el-form-item>
                  <el-form-item label="及格分数">
                    <el-input-number
                      v-model="examForm.passScore"
                      :min="0"
                      :max="100"
                      placeholder="分数"
                    />
                    <span class="unit">分</span>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 题目配置 -->
            <div class="config-section">
              <div class="section-title">
                <el-icon class="section-icon"><Edit /></el-icon>
                <span>题目配置</span>
              </div>
              <div class="form-content">
                <el-form :model="examForm" label-width="100px" size="default">
                  <el-form-item label="单选题">
                    <el-input-number
                      v-model="examForm.singleChoice"
                      :min="0"
                      placeholder="题数"
                    />
                    <span class="unit">题</span>
                  </el-form-item>
                  <el-form-item label="多选题">
                    <el-input-number
                      v-model="examForm.multipleChoice"
                      :min="0"
                      placeholder="题数"
                    />
                    <span class="unit">题</span>
                  </el-form-item>
                  <el-form-item label="判断题">
                    <el-input-number
                      v-model="examForm.trueOrFalse"
                      :min="0"
                      placeholder="题数"
                    />
                    <span class="unit">题</span>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 考试设置 -->
            <div class="config-section">
              <div class="section-title">
                <el-icon class="section-icon"><Setting /></el-icon>
                <span>考试设置</span>
              </div>
              <div class="form-content">
                <el-form :model="examForm" label-width="100px" size="default">
                  <el-form-item label="随机出题">
                    <el-switch v-model="examForm.randomQuestions" />
                  </el-form-item>
                  <el-form-item label="打乱选项">
                    <el-switch v-model="examForm.shuffleOptions" />
                  </el-form-item>
                  <el-form-item label="允许重考">
                    <el-switch v-model="examForm.allowRetake" />
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="config-actions">
              <el-button type="primary" @click="saveExam">保存考试</el-button>
              <el-button @click="previewExam">预览</el-button>
            </div>
          </div>
        </div>

        <!-- 右侧考试列表 -->
        <div class="exam-list">
          <div class="list-header">
            <div class="header-info">
              <span class="total-count">共 {{ examList.length }} 场考试</span>
            </div>
            <div class="header-actions">
              <el-button type="primary" size="small" @click="createNewExam">
                <el-icon><Plus /></el-icon>
                新建考试
              </el-button>
            </div>
          </div>

          <div class="list-content">
            <div class="exam-table">
              <el-table :data="examList" style="width: 100%" stripe>
                <el-table-column prop="id" label="序号" width="60" />
                <el-table-column prop="name" label="考试名称" min-width="150" />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="时长" width="80">
                  <template #default="scope">
                    {{ scope.row.duration }}分钟
                  </template>
                </el-table-column>
                <el-table-column prop="passScore" label="及格分" width="80" />
                <el-table-column
                  prop="totalQuestions"
                  label="题目数"
                  width="80"
                />
                <el-table-column
                  prop="participants"
                  label="参与人数"
                  width="90"
                />
                <el-table-column
                  prop="createdAt"
                  label="创建时间"
                  width="150"
                />
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="scope">
                    <el-button
                      type="text"
                      size="small"
                      @click="editExam(scope.row)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click="viewExam(scope.row)"
                    >
                      查看
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click="copyExam(scope.row)"
                    >
                      复制
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      style="color: #f56c6c"
                      @click="deleteExam(scope.row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "ExamAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const examId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

onBeforeMount(() => {});
onMounted(() => {});

function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;

        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
    .content {
      width: 100%;
      height: calc(100% - 70px);
      padding: 10px 0;
    }
  }
}
</style>
