<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="94" height="94" viewBox="0 0 94 94"><defs><style>.a{fill:url(#a);}.b{fill:#fff;}.c{filter:url(#d);}.d{filter:url(#b);}</style><radialGradient id="a" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#33cec9"/><stop offset="1" stop-color="#1c8d84"/></radialGradient><filter id="b" x="0" y="0" width="94" height="94" filterUnits="userSpaceOnUse"><feOffset input="SourceAlpha"/><feGaussianBlur stdDeviation="5" result="c"/><feFlood flood-color="#26804c" flood-opacity="0.443"/><feComposite operator="in" in2="c"/><feComposite in="SourceGraphic"/></filter><filter id="d" x="0" y="0" width="94" height="94" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="e"/><feFlood flood-color="#8ff8c8" flood-opacity="0.353" result="f"/><feComposite operator="out" in="SourceGraphic" in2="e"/><feComposite operator="in" in="f"/><feComposite operator="in" in2="SourceGraphic"/></filter></defs><g data-type="innerShadowGroup"><g class="d" transform="matrix(1, 0, 0, 1, 0, 0)"><circle class="a" cx="32" cy="32" r="32" transform="translate(15 15)"/></g><g class="c" transform="matrix(1, 0, 0, 1, 0, 0)"><circle class="b" cx="32" cy="32" r="32" transform="translate(15 15)"/></g></g></svg>