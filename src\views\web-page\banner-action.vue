<template>
  <div class="banner-detail-container">
    <div class="container-header">
      <div class="left">banner管理</div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar height="100%" warp-style="overflow-x: hidden;">
        <div class="block top">
          <el-row>
            <el-col :span="12" align="center">
              <div class="label">banner名称</div>
              <div class="input">
                <el-input
                  v-model="bannerForm.name"
                  placeholder="请输入banner名称"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">身份类型</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="bannerForm.ut"
                  placeholder="请选择身份类型"
                  filterable
                  clearable
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12" align="center">
              <div class="label">链接地址</div>
              <div class="input">
                <el-input
                  v-model="bannerForm.link_url"
                  placeholder="请输入链接地址，为空时不跳转（选填）"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">上架时间</div>
              <div class="input">
                <el-date-picker
                  class="date-picker"
                  size="large"
                  v-model="bannerForm.dateTimeRange"
                  type="daterange"
                  range-separator="~"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="YYYY-MM-DD"
                />
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12" align="center">
              <div class="label">展示图片</div>
              <div class="input">
                <el-upload
                  v-model="bannerForm.imgUrl"
                  class="single-uploader"
                  :show-file-list="false"
                  list-type="picture-card"
                >
                  <!-- :before-upload="handleBeforeUpload" -->
                  <!-- :http-request="uploadFile" -->
                  <img
                    v-if="imgUrl"
                    :src="imgUrl"
                    class="single-uploader__image"
                  />
                  <div v-if="imgUrl" class="img-upload__overlay">
                    <el-icon
                      class="img-upload__preview-icon"
                      @click.stop="handlePreview"
                    >
                      <i-ep-zoom-in />
                    </el-icon>

                    <el-icon
                      class="img-upload__delete-icon"
                      @click.stop="handleDelete"
                    >
                      <i-ep-delete />
                    </el-icon>
                  </div>

                  <el-icon v-else class="single-uploader__icon"
                    ><i-ep-plus />
                  </el-icon>
                </el-upload>
                <span class="tips">建议尺寸：1920x380 px</span>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label">备注</div>
              <div class="input">
                <el-input
                  v-model="bannerForm.remark"
                  placeholder="请输入备注（选填）"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
          </el-row>

          <div class="footer">
            <div class="btn cancel-btn" @click="close">取 消</div>
            <div class="btn primary-btn" @click="handleSubmit">保 存</div>
          </div>
        </div>
      </el-scrollbar>
      <el-dialog v-model="dialog.visible">
        <img w-full :src="previewImgUrl" alt="Preview Image" />
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import {
  ElImageViewer,
  UploadRawFile,
  UploadRequestOptions,
} from "element-plus";
import { getBannersDetail, updateBanners } from "@/api/web-page";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "BannerAction",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const imgUrl = ref(new URL("@/assets/images/cover.jpg", import.meta.url).href);
const previewImgUrl = imgUrl.value;

// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "dialog",
  width: "40%",
  title: "",
});

//表单数据
const bannerId = ref(router.currentRoute.value.params.id);
const bannerForm = reactive<any>({
  name: "",
  resource_id: "",
  seq: "",
  link_url: "",
  beginned_at: "",
  end_at: "",
  remark: "",
  status: "",
});
const options = reactive<any>([]);

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });

function getData() {}

function handleQuery() {}

function close() {
  router.go(-1);
}
function handleSubmit() {}
function handleBack() {
  router.go(-1);
}

// 图片上传相关
function handlePreview() {
  dialog.visible = true;
}
function handleDelete() {
  imgUrl.value = "";
}
function handleBeforeUpload(file: any) {}
function uploadFile() {}
</script>

<style scoped lang="scss">
.banner-detail-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      justify-content: space-between;
      font-size: 20px;
      //font-family: 苹方-简, 苹方-简;
      font-weight: 500;
      color: #3b4664;
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        //font-family: 苹方-简, 苹方-简;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
  }

  .block {
    width: 100%;
    padding: 10px;
    border-radius: 8px 8px 0 0;

    .el-row {
      margin: 20px 0;
    }

    .el-col {
      display: flex;
      align-items: center;
      padding: 0 20px;
    }

    .label {
      width: 200px;
      font-size: 15px;
      //font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #3b4664;
    }

    .input {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      width: 100%;

      img {
        width: 50%;
        height: 160px;
        object-fit: cover;
      }

      span {
        margin-top: 10px;
        font-size: 13px;
        //font-family: 苹方-简, 苹方-简;
        font-weight: 400;
        color: #8d9295;
        text-align: left;
      }

      :deep(.el-range-editor--large.el-input__wrapper) {
        width: 100% !important;
      }
    }

    &:nth-child(3) {
      border-bottom: none;
    }

    .single-uploader {
      position: relative;
      display: flex;
      justify-content: flex-start;

      .single-uploader__image {
        position: relative;
        width: 100%;
        height: 100%;
      }

      .img-upload__overlay {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 10%;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    margin-top: 15px;

    .btn {
      width: 150px;
      height: 50px;
      margin-left: 20px;
      border-radius: 35px;

      &:nth-child(1) {
        margin-left: 0;
      }
    }
  }
}
</style>
