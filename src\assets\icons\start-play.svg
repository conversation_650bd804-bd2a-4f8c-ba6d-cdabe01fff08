<svg xmlns="http://www.w3.org/2000/svg" width="209" height="209" viewBox="0 0 209 209">
  <defs>
    <style>
      .a { fill: url(#a); }
      .b { fill: #fff; }
      .c { fill: url(#g); }
      .d { filter: url(#j); }
      .e { filter: url(#h); }
      .f { filter: url(#d); }
      .g { filter: url(#b); }
    </style>
    <radialGradient id="a" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0.482"/>
    </radialGradient>
    <filter id="b" x="0" y="0" width="209" height="209" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="10" result="c"/>
      <feFlood flood-color="#12ac88" flood-opacity="0.655"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="d" x="0" y="0" width="209" height="209" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="6" result="e"/>
      <feFlood flood-color="#075345" flood-opacity="0.412" result="f"/>
      <feComposite operator="out" in="SourceGraphic" in2="e"/>
      <feComposite operator="in" in="f"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <linearGradient id="g" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#33cec9"/>
      <stop offset="1" stop-color="#1c8d84"/>
    </linearGradient>
    <filter id="h" x="73.927" y="63.678" width="73.083" height="79.199" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="i"/>
      <feFlood flood-color="#238306" flood-opacity="0.192"/>
      <feComposite operator="in" in2="i"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="j" x="73.927" y="63.678" width="73.083" height="79.199" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="k"/>
      <feFlood flood-color="#9dfc86" flood-opacity="0.482" result="l"/>
      <feComposite operator="out" in="SourceGraphic" in2="k"/>
      <feComposite operator="in" in="l"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="translate(29.647 26.311)">
    <g data-type="innerShadowGroup">
      <g class="g" transform="translate(-29.65 -26.31)">
        <circle class="a" cx="74.5" cy="74.5" r="74.5" transform="translate(30 27)"/>
      </g>
      <g class="f" transform="translate(-29.65 -26.31)">
        <circle class="b" cx="74.5" cy="74.5" r="74.5" transform="translate(30 27)"/>
      </g>
    </g>
    <g data-type="innerShadowGroup">
      <g class="e" transform="translate(-29.65 -26.31)">
        <path class="c" d="M128,89.2c0-3.388,2.39-4.781,5.32-3.124l47.562,26.892c2.938,1.662,2.93,4.361,0,6.016L133.32,145.873c-2.938,1.662-5.32.263-5.32-3.124Z" transform="translate(-45.07 -15.7)"/>
      </g>
      <g class="d" transform="translate(-29.65 -26.31)">
        <path class="b" d="M128,89.2c0-3.388,2.39-4.781,5.32-3.124l47.562,26.892c2.938,1.662,2.93,4.361,0,6.016L133.32,145.873c-2.938,1.662-5.32.263-5.32-3.124Z" transform="translate(-45.07 -15.7)"/>
      </g>
    </g>
  </g>
</svg>