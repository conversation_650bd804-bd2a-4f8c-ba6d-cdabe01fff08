<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="94" height="94" viewBox="0 0 94 94"><defs><style>.a{fill:url(#a);}.b{fill:#fff;}.c{fill:url(#g);}.d{filter:url(#d);}.e{filter:url(#b);}</style><radialGradient id="a" cx="0.5" cy="0.5" r="0.5" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#e7f5ef"/><stop offset="1" stop-color="#cae5d5"/></radialGradient><filter id="b" x="0" y="0" width="94" height="94" filterUnits="userSpaceOnUse"><feOffset input="SourceAlpha"/><feGaussianBlur stdDeviation="5" result="c"/><feFlood flood-color="#0f9848" flood-opacity="0.443"/><feComposite operator="in" in2="c"/><feComposite in="SourceGraphic"/></filter><filter id="d" x="0" y="0" width="94" height="94" filterUnits="userSpaceOnUse"><feOffset input="SourceAlpha"/><feGaussianBlur stdDeviation="10" result="e"/><feFlood flood-color="#45ceb9" flood-opacity="0.373" result="f"/><feComposite operator="out" in="SourceGraphic" in2="e"/><feComposite operator="in" in="f"/><feComposite operator="in" in2="SourceGraphic"/></filter><linearGradient id="g" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#00918c"/><stop offset="1" stop-color="#1c8d84"/></linearGradient></defs><g transform="translate(-345 -246)"><g data-type="innerShadowGroup"><g class="e" transform="matrix(1, 0, 0, 1, 345, 246)"><circle class="a" cx="32" cy="32" r="32" transform="translate(15 15)"/></g><g class="d" transform="matrix(1, 0, 0, 1, 345, 246)"><circle class="b" cx="32" cy="32" r="32" transform="translate(15 15)"/></g></g><path class="c" d="M44.276,130.4l-2.25-2.319a.941.941,0,0,0-1.374.017L23.377,146.78l-8.164-8.8a.941.941,0,0,0-1.377-.01l-2.258,2.363a.994.994,0,0,0-.006,1.362L22.684,153.52a.941.941,0,0,0,1.382,0l20.224-21.758a.994.994,0,0,0-.014-1.363Z" transform="translate(363.693 152.213)"/></g></svg>