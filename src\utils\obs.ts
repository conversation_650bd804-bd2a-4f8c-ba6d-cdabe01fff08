import { useStorage } from "@vueuse/core";
import { useUserStore } from "@/store/modules/user";
// import "./esdk-obs-browserjs.3.24.3.min.js";
import ObsClient from "esdk-obs-browserjs/src/obs";

export async function obsUpload(file: any, ObjKey: any) {
  const userStore = useUserStore();
  const ak = userStore.obsKey.ak;
  const sk = userStore.obsKey.sk;
  const token = userStore.obsKey.token;
  const objKey = ObjKey;

  const res: any = {
    url: "",
  };
  //@ts-ignore
  const obsClient = new ObsClient({
    access_key_id: ak,
    secret_access_key: sk,
    security_token: token,
    server: "https://obs.cn-north-4.myhuaweicloud.com",
  });
  // const resUrl = "https://gkr.obs.cn-north-4.myhuaweicloud.com/" + obj<PERSON><PERSON>;
  const resUrl = "http://obs.gknowledge.cn/" + obj<PERSON><PERSON>;
  try {
    const response = await obsClient.putObject({
      Bucket: "gkr",
      Key: objKey,
      SourceFile: file,
      // ContentType: "application/zip",
    });
    // function (err: any, result: any) {
    //   if (err) {
    //     ElMessage.error("上传失败，请重试或刷新后上传");
    //   }
    //   if (result) {
    //     console.log("result", result);
    //     res.url = resUrl;
    //   }
    // }
    // console.log("response", response, response.CommonMsg.Status);
    if (response && response.CommonMsg.Status == 200) {
      res.url = resUrl;
      return res;
    }
  } catch (e) {
    ElMessage.error("上传失败，请重试或刷新后上传");
    console.log(e);
  }

  // // 获取文件，避免变为附件下载---暂时用不上
  // axios.get(`${resUrl}`, { responseType: "arraybuffer" }).then((res) => {
  //   // 转ArrayBuffer为Blob类型
  //   const blob = new Blob([res.data], { type: res.headers["content-type"] });
  //   const reader = new FileReader();
  //   // 使用FileReader将文件内容读取出来
  //   reader.onload = (e) => {
  //     imageData.value = e.target?.result?.toString() ?? "";
  //   };
  //   reader.readAsDataURL(blob);
  // });

  // return res;
}
