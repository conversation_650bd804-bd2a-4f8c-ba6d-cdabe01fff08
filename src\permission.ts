import router from "@/router";
import { useUserStoreHook } from "@/store/modules/user";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useRoute, useRouter, RouteLocationMatched } from "vue-router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";

NProgress.configure({ showSpinner: false }); // 进度条

const permissionStore = usePermissionStoreHook();
const userStore = useUserStoreHook();
// userStore.resetToken(); //问题暂未知-- 本地运行时卡住页面清一下token再进入？
// const route = useRoute();
// const router = useRouter();
// 白名单路由
const whiteList = ["/login"];
let routerFlag: any = 0;
router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const localMenus: any = localStorage.getItem("menusByAdmin");
  const menus: any = localMenus ? JSON.parse(localMenus) : [];
  const allRoutes: any = permissionStore.routes;
  const hasToken: any = localStorage.getItem("accessTokenByAdmin");
  console.log("from", from);
  console.log("to", to);
  // 判断目标路由是否存在
  // const hasRoute = router.getRoutes().some((route: any) => {
  //   return route.path === to.path || route.name === to.name;
  // });
  if (hasToken) {
    if (to.path === "/login") {
      // 如果已登录，跳转首页
      next({ path: "/" });
      NProgress.done();
    } else {
      if (to.matched.length === 0) {
        try {
          routerFlag++; // 通过flag判断循环
          console.log("first---路由导航的匹配路由");
          const accessRoutes = await permissionStore.generateRoutes(menus);
          accessRoutes.forEach((route: any) => {
            router.addRoute(route);
          });
          // 判断该路由是否存在，存在则进行跳转，不存在则跳转到第一个目录
          // match都为空，默认跳转第一个路由了TODO:可以继续优化，刷新会跳回第一个目录
          if (routerFlag >= 2) {
            console.log(
              "循环次数超过2次，重定向到路由accessRoutes[0]",
              routerFlag
            );
            //避免无限循环，超过2次则跳转到第一个路由
            next({ path: accessRoutes[0].path, replace: true });
          } else {
            console.log("有缓存或刷新后进入路由", routerFlag);
            next({ ...to, replace: true });
          }
        } catch (error) {
          // 移除 token 并跳转登录页或首页，初始化index
          await userStore.resetToken();
          next({ path: "/" });
          NProgress.done();
        }
      } else {
        next();
      }
    }
  } else {
    console.log("noToken", to);
    // 未登录可以访问白名单页
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      userStore.resetToken();
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
