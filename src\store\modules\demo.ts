/*
 * @Author: czf <EMAIL>
 * @Date: 2024-10-28 17:01:07
 * @LastEditors: czf <EMAIL>
 * @LastEditTime: 2024-10-30 14:14:29
 * @FilePath: \management-platform\src\store\modules\demo.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from "pinia";
import { store } from "@/store";
//选项方式写法
// export const useDemoStore = defineStore("demo", {
//   state: () => {
//     return {
//       num: 0,
//     };
//   },
//   getters: {},
//   actions: {
//     changeCar(val: any) {
//       this.num += val;
//     },
//   },
//   persist: {
//     enabled: true, // 这个配置代表存储生效，而且是整个store都存储
//     // strategies: [
//     //   {
//     //     storage: sessionStorage,
//     //     key: "", //储存到浏览器中的key值，默认会以store的id作为key
//     //     paths: [""], //指定state的字段缓存
//     //   },
//     // ],
//   },
// });

// 组合式写法
export const useStore = defineStore(
  "demo",
  () => {
    // const my: MyInfo = {};

    /**
     * @param {}
     * @returns
     */

    // 获取信息
    // function getDemo() {
    //   return new Promise<MyInfo>((resolve, reject) => {
    //     getMyInfoApi()
    //       .then(({ data }) => {
    //         if (!data) {
    //           reject("Verification failed, please Login again.");
    //           return;
    //         }
    //         if (!data.roles || data.roles.length <= 0) {
    //           reject("getUserInfo: roles must be a non-null array!");
    //           return;
    //         }
    //         Object.assign(my, { ...data });
    //         resolve(data);
    //       })
    //       .catch((error) => {
    //         reject(error);
    //       });
    //   });
    // }

    return {};
  }
  // {
  //   persist: {
  //     enabled: true,
  //   },
  // }
);

// 非setup
export function useStoreHook() {
  return useStore(store);
}
