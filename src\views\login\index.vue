<template>
  <div class="login-container">
    <!-- 登录表单 -->
    <el-card
      class="z-1 !border-none w-120 !bg-transparent !rounded-4% <sm:w-83"
    >
      <div class="text-center relative">
        <h2>{{ title }}</h2>
        <!-- <el-tag class="ml-2 absolute top-0 right-0">{{ version }}</el-tag> -->
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginData"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="mobile">
          <div class="p-2">
            <svg-icon icon-class="user" />
          </div>
          <el-input
            ref="username"
            v-model="loginData.mobile"
            class="flex-1"
            size="large"
            placeholder="请输入手机号码"
            name="username"
          />
        </el-form-item>

        <!-- <el-tooltip
          :disabled="isCapslock === false"
          content="Caps lock is On"
          placement="right"
        >
          <el-form-item prop="password">
            <span class="p-2">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              v-model="loginData.password"
              class="flex-1"
              :placeholder="$t('login.password')"
              :type="passwordVisible === false ? 'password' : 'input'"
              size="large"
              name="password"
              @keyup="checkCapslock"
              @keyup.enter="handleLogin"
            />
            <span class="mr-2" @click="passwordVisible = !passwordVisible">
              <svg-icon
                :icon-class="passwordVisible === false ? 'eye' : 'eye-open'"
                class="cursor-pointer"
              />
            </span>
          </el-form-item>
        </el-tooltip> -->

        <!-- 验证码 -->
        <el-form-item prop="code">
          <span class="p-2">
            <svg-icon icon-class="captcha" />
          </span>

          <el-input
            v-model="loginData.code"
            auto-complete="off"
            placeholder="请输入验证码"
            class="w-[60%] flex-1"
            @keyup.enter="handleLogin"
          />

          <div class="captcha">
            <div
              class="btn primary-btn"
              :style="{
                'pointer-events': verifyDisabled ? 'none' : 'auto',
                opacity: verifyDisabled ? 0.7 : 1,
              }"
              @click="getCaptcha"
            >
              {{ verifyDisabled ? `${countDown}秒后重新发送` : "获取验证码" }}
            </div>
          </div>
        </el-form-item>

        <!-- <el-button
          :loading="loading"
          type="primary"
          class="w-full"
          size="large"
          @click.prevent="handleLogin"
          >{{ $t("login.login") }}
        </el-button> -->
        <div
          class="btn login-btn w-full"
          :loading="loading"
          @click.prevent="handleLogin"
        >
          {{ $t("login.login") }}
        </div>
        <!-- 账号密码提示 -->
        <!-- <div class="mt-10 text-sm">
          <span>{{ $t("login.username") }}: admin</span>
          <span class="ml-4"> {{ $t("login.password") }}: 123456</span>
        </div> -->
      </el-form>
    </el-card>

    <!-- ICP备案 -->
    <div class="absolute bottom-1 text-[14px] text-center">
      <div>
        Copyright ©2025 gknowledge.cn All Rights Reserved.
        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">
          京ICP备2024083906号
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from "vue-i18n";
import router from "@/router";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { useSettingsStore } from "@/store/modules/settings";

// 状态管理依赖
import { useUserStore } from "@/store/modules/user";
import { useAppStore } from "@/store/modules/app";

// API依赖
import { LocationQuery, LocationQueryValue, useRoute } from "vue-router";
import { getVerifyCode } from "@/api/user";

import { validateMobile } from "@/utils/validate";

const settingsStore = useSettingsStore();

const { title, version } = settingsStore;

/**
 * 根据屏幕宽度切换设备模式
 */
const appStore = useAppStore();

const loading = ref(false); // 按钮loading
const countDown = ref(60);
const verifyDisabled = ref(false);
const isCapslock = ref(false); // 是否大写锁定
// const passwordVisible = ref(false); // 密码是否可见
const loginFormRef = ref(ElForm); // 登录表单ref
const loginData = reactive<any>({
  mobile: "",
  code: "",
});

const { t } = useI18n();
const loginRules = computed(() => {
  const prefix = appStore.language === "en" ? "Please enter " : "请输入";
  return {
    mobile: [
      {
        required: true,
        trigger: "blur",
        validator: validateMobile,
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        validator: (rule: any, value: any, callback: any) => {
          if (value.length < 6) {
            callback(new Error("The password can not be less than 6 digits"));
          } else {
            callback();
          }
        },
        message: `${prefix}${t("login.password")}`,
      },
    ],
    code: [
      {
        required: true,
        trigger: "blur",
        message: `${prefix}验证码`,
      },
    ],
  };
});

// onBeforeMount(() => {
//   localStorage.clear();
//   sessionStorage.clear(); //TODO:是否需要清除缓存，登录前清一遍缓存，避免卡死
// });

/**
 * 获取验证码
 */
function getCaptcha() {
  const mobileReg = /^[1][3456789][0-9]{9}$/;
  const valid = mobileReg.test(loginData.mobile);
  if (loginData.mobile && valid) {
    getVerifyCode({ mobile: loginData.mobile }).then((res: any) => {
      if (res.status == 200) {
        verifyDisabled.value = true;
        const timer = setInterval(() => {
          if (countDown.value > 0) {
            countDown.value--;
          } else {
            clearInterval(timer);
            verifyDisabled.value = false;
            countDown.value = 60; // 重置倒计时时间
          }
        }, 1000);
        ElMessage.success({
          message: "发送成功!",
        });
      }
    });
  } else {
    ElMessage.warning({
      message: "请输入正确的手机号",
    });
    return;
  }
  //点击获取验证码
}

/**
 * 登录
 */
const route = useRoute();
const userStore = useUserStore();
function handleLogin() {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as LocationQueryValue) ?? "/";
  const otherQueryParams = Object.keys(query).reduce(
    (acc: any, cur: string) => {
      if (cur !== "redirect") {
        acc[cur] = query[cur];
      }
      return acc;
    },
    {}
  );
  // router.push({ path: redirect, query: otherQueryParams });
  // ElMessage.success("登录成功!");
  loginFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      userStore
        .login(loginData)
        .then(async (res: any) => {
          if (res.status == 200) {
            userStore.getUserInfo().then((info: any) => {
              console.log("登录成功!");
              if (info.status == 200) {
                ElMessage.success("登录成功!");
                router.push({ path: redirect, query: otherQueryParams });
              }
            });
          }
        })
        .catch(() => {})
        .finally(() => {
          loading.value = false;
        });
    }
  });
}
onBeforeMount(() => {
  // localStorage.clear(); //TODO:是否需要清除缓存，可在用户跳转前清空一遍缓存
});
onMounted(() => {});
</script>

<style lang="scss" scoped>
.dark .login-container {
  background: url("@/assets/images/login-bg-dark.jpg") no-repeat center right;
}

.login-container {
  @apply w-full h-full flex-center;

  overflow-y: auto;
  background: url("@/assets/images/login-bg.jpg") no-repeat center right;

  .login-form {
    padding: 30px 10px;

    .login-btn {
      height: 50px;
      font-size: 18px;
      font-weight: 600;
    }

    .captcha {
      position: absolute;
      top: 0;
      right: 0;
      min-width: 120px;
      height: 100%;

      .btn {
        height: 100%;
      }

      .image-slot {
        display: flex;
        align-items: center;
        justify-content: right;
        width: 100%;
        height: 100%;
        font-size: 18px;
        color: var(--el-text-color-secondary);
        background: var(--el-fill-color-light);

        svg {
          margin-right: 10px;
        }
      }
    }
  }
}

.el-form-item {
  background: var(--el-input-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 0;
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;

    &.is-focus,
    &:hover {
      box-shadow: none !important;
    }

    input:-webkit-autofill {
      transition: background-color 1000s ease-in-out 0s; /* 通过延时渲染背景色变相去除背景颜色 */
    }
  }
}
</style>
