import request from "@/utils/request";
import { AxiosPromise } from "axios";

//企业信息
export function getEnterprises(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/enterprises",
    method: "get",
    params: queryParams,
  });
}
export function getEnterprisesDetail(id: number) {
  return request({
    url: "/gkadmin/v1/enterprises/" + id,
    method: "get",
  });
}

/**
 * 新增
 *
 * @param data
 */
export function addEnterprises(data: any) {
  return request({
    url: "/gkadmin/v1/enterprises",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateEnterprises(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/enterprises/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteEnterprises(ids: string) {
  return request({
    url: "/gkadmin/v1/enterprises/" + ids,
    method: "delete",
  });
}

//企业套餐
export function getEntCombo(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/ent_packages",
    method: "get",
    params: queryParams,
  });
}
export function getEntComboDetail(id: number) {
  return request({
    url: "/gkadmin/v1/ent_packages/" + id,
    method: "get",
  });
}

/**
 * 新增
 *
 * @param data
 */
export function addEntCombo(data: any) {
  return request({
    url: "/gkadmin/v1/ent_packages",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateEntCombo(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/ent_packages/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteEntCombo(ids: string) {
  return request({
    url: "/gkadmin/v1/ent_packages/" + ids,
    method: "delete",
  });
}

//企业课程
export function getEntCourses(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/ent_courses",
    method: "get",
    params: queryParams,
  });
}
export function updateEntCourses(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/ent_courses/" + id,
    method: "put",
    data: data,
  });
}
// 企业素材

export function getEntResources(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/ent_resources",
    method: "get",
    params: queryParams,
  });
}
export function getEntResourcesDetail(id: number) {
  return request({
    url: "/gkadmin/v1/ent_resources/" + id,
    method: "get",
  });
}
export function updateEntResources(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/ent_resources/" + id,
    method: "put",
    data: data,
  });
}

// 获取企业目录列表
export function getEntSysMenus(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/ent_admin_menus",
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增企业目录
 *
 * @param data
 */
export function addEntSysMenus(data: any) {
  return request({
    url: "/gkadmin/v1/ent_admin_menus",
    method: "post",
    data: data,
  });
}

/**
 *  修改企业目录
 *
 * @param id
 * @param data
 */
export function updateEntSysMenus(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/ent_admin_menus/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除企业目录
 *
 * @param ids
 */
export function deleteEntSysMenus(ids: string) {
  return request({
    url: "/gkadmin/v1/ent_admin_menus/" + ids,
    method: "delete",
  });
}

//企业内训系统

/**
 *
 *详情
 * @param ent_id
 */
export function getEntTrainSys(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/train_syses",
    method: "get",
    params: queryParams,
  });
}
/**
 *
 *新增
 * @param ent_id
 */
export function addEntTrainSys(data: any) {
  return request({
    url: "/gkadmin/v1/train_syses",
    method: "post",
    data,
  });
}
/**
 *
 *更新
 * @param ent_id
 */
export function updateEntTrainSys(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/train_syses/" + id,
    method: "put",
    data,
  });
}

//获取企业员工
export function getEntUsers(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/ent_users",
    method: "get",
    params: queryParams,
  });
}

//获取企业培训列表
export function getEntTrainingList(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/ent_trainings",
    method: "get",
    params: queryParams,
  });
}
