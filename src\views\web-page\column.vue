<template>
  <div class="column-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <el-select
          :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.status"
            placeholder="请选择状态"
            filterable
            clearable
            @change="handleChange"
          >
            <el-option
              v-for="item in userTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
      </div>
      <div class="middle">
        <div class="tips">
          注意：为避免用户端显示出错，部分信息将不可修改或删除
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增栏目
        </div>
      </div>
    </div>
    <!-- <div class="after-header">
      <el-radio-group
        v-model="activeUserType"
        class="header-radio-group"
        @change="handleUserChange"
      >
        <el-radio-button
          size="large"
          v-for="(item, index) in userTypeOptions"
          :key="index"
          :value="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>
    </div> -->
    <div class="content">
      <el-table
        class="draggable"
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
        :key="refreshTableKey"
      >
        <el-table-column label="" fixed min-width="40" align="center">
          <!-- <template #default="scope"> -->
          <el-icon class="sort-icon" style="cursor: pointer">
            <i-ep-rank />
          </el-icon>
          <!-- </template> -->
        </el-table-column>
        <el-table-column
          label="排序"
          align="center"
          prop="mobile"
          min-width="70"
        >
          <template #default="scope">
            {{ scope.row.seq }}
          </template>
        </el-table-column>
        <el-table-column
          label="栏目名称"
          align="center"
          prop="mobile"
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="状态"
          align="center"
          prop="status"
          min-width="120"
        >
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column> -->

        <el-table-column label="操作" align="center" min-width="120">
          <template #default="scope">
            <div class="option-btn">
              <!-- :style="{
                opacity: scope.row.id <= 3 ? 0.5 : 1,
                pointerEvents: scope.row.id <= 3 ? 'none' : 'auto',
              }" -->
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                修改
              </div>
              <!-- <div
                class="btn info-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div> -->
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
                :style="{
                  opacity: scope.row.id <= 3 ? 0.5 : 1,
                  pointerEvents: scope.row.id <= 3 ? 'none' : 'auto',
                }"
                v-show="enableDelete"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="user-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div class="dialog-body">
        <el-form label-width="5rem">
          <el-form-item
            label="栏目名称"
            :style="{
              pointerEvents:
                formData.id <= 3 && dialog.type == 'edit' ? 'none' : 'auto',
            }"
          >
            <el-input
              v-model="formData.name"
              placeholder="请输入栏目名称"
              clearable
              size="large"
            />
          </el-form-item>
          <el-form-item label="栏目展示">
            <el-select
              size="large"
              v-model="formData.display"
              placeholder="请选择展示方式"
              filterable
              clearable
              :suffix-icon="`CaretBottom`"
            >
              <el-option
                v-for="item in displayOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="栏目顺序">
            <el-input
              v-model="formData.seq"
              placeholder="请输入栏目顺序"
              clearable
              size="large"
            />
            <span class="tips"
              >*栏目顺序若有冲突，栏目创建成功后将原当前顺序以及后面的栏目顺延一位。</span
            >
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { default as Sortable, SortableEvent } from "sortablejs";
import { deepClone } from "@/utils";
import {
  getSections,
  getSectionsDetail,
  deleteSections,
  addSections,
  updateSections,
} from "@/api/web-page";
import { parseTime } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "Column",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const activeUserType = ref<any>(10);
const userTypeOptions = ref<any>([
  {
    value: 10,
    label: "游客",
  },
  {
    value: 20,
    label: "学员",
  },
  {
    value: 30,
    label: "企业员工",
  },
  {
    value: 40,
    label: "企业管理员",
  },
]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const refreshTableKey = ref(0);
const tableData = ref<any[]>([]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "正常" },
  20: { type: "success", label: "上架" },
  30: { type: "info", label: "下架" },
  40: { type: "danger", label: "删除" },
  // add more status mappings as needed
});
// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "25%",
  title: "",
});

const enableDelete = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 11);
});

// 表单数据
const rowId = ref();
const formData = reactive<any>({
  name: "",
  seq: "",
  display: "",
});
const displayOptions = ref<any>([
  {
    value: "list",
    label: "列表",
  },
  {
    value: "grid",
    label: "九宫格",
  },
]);

// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
  rowDrag();
});

watch(
  refreshTableKey,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        rowDrag();
      });
    }
  },
  {
    immediate: true, //初始化立即执行
  }
);

// 行拖拽
function rowDrag() {
  const tbody = document.querySelector(
    ".draggable .el-table__body-wrapper tbody"
  );
  if (!tbody) return;

  Sortable.create(tbody as HTMLElement, {
    draggable: ".draggable .el-table__row",
    animation: 150,
    ghostClass: "drag-class",
    handle: ".sort-icon", // 指定了拖拽手柄的类名
    onEnd: function (evt) {
      let newIndex = evt.newIndex;
      let oldIndex = evt.oldIndex;
      if (newIndex !== oldIndex) {
        const arr = deepClone(tableData.value);
        const currRow = arr.splice(oldIndex, 1)[0];
        const arr2 = deepClone(arr);
        arr2.splice(newIndex, 0, currRow);
        tableData.value = arr2.map((item: any, index: any) => {
          item.seq = index;

          return item;
        });
        refreshTableKey.value++;
        tableData.value.forEach((item: any, index: any) => {
          updateSections(item.id, { seq: index }).then((res: any) => {});
        });
      }
    },
  });
}

function handleChange(val?: string | number | undefined) {}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
function handleUserChange() {
  console.log(activeUserType.value);
}
function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getSections(params).then((res: any) => {
    tableData.value = res.data.sections.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return item;
    });
    total.value = res.total;
    loading.value = false;
  });
}
function handleCreate() {
  dialog.visible = true;
  dialog.title = "新增栏目";
  dialog.type = "create";
}
function onRowClick(type: any, row: any) {
  if (type == "edit") {
    dialog.visible = true;
    dialog.type = "edit";
    dialog.title = "修改栏目";
    Object.assign(formData, row);
    rowId.value = row.id;
  }
  if (type == "detail") {
    dialog.visible = true;
    dialog.type = "detail";
    dialog.title = "栏目详情";
  }
  if (type == "delete") {
    handelDelete(row);
  }
}
function handelDelete(row: any) {
  ElMessageBox.confirm("此操作将永久删除该栏目，是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteSections(row.id).then(() => {
      ElMessage.success({
        message: "删除成功!",
      });
    });
  });
}

function closeDialog() {
  dialog.visible = false;
  Object.assign(formData, {
    name: "",
    seq: "",
    display: "",
  });
}
function handleSubmit() {
  const data: any = {
    name: formData.name,
    seq: formData.seq,
    display: formData.display,
  };
  if (dialog.type === "create") {
    addSections(data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "新增成功!",
        });
        closeDialog();
        getData();
      }
    });
  }
  if (dialog.type === "edit") {
    updateSections(rowId.value, data).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: "修改成功!",
        });
        closeDialog();
        getData();
      }
    });
  }
}
</script>

<style scoped lang="scss">
.column-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #ffffff;
  box-shadow: 0px 3px 20px 1px rgba(109, 125, 126, 0.07);
  border-radius: 8px 8px 8px 8px;
  // .after-header {
  //   width: 100%;
  //   padding: 20px 20px 20px 20px;
  // }
  .container-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    // padding: 20px 20px 0px 20px;
    padding: 10px 20px 10px 20px;
    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 60%;
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
    .middle {
      display: flex;
      justify-content: center;
      align-items: center;
      .tips {
        font-weight: 400;
        font-size: 14px;
        color: #f23c33;
      }
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .filter-row {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 20px;
      width: 260px;
      &:nth-child(1) {
        margin-left: 0;
        width: 260px;
      }
      .btn {
        margin-left: 20px;
        width: 116px;
        height: 40px;
      }
    }
  }
  .content {
    // height: calc(100% - 240px);
    height: calc(100% - 170px);
    width: 100%;
    padding: 10px 20px 10px 20px;
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }
}
</style>
