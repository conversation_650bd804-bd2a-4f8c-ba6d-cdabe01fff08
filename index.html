<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="绿知管理后台" />
    <!-- <meta name="keywords" content="vue-element-admin,vue3-element-admin" /> -->
    <title>绿知管理后台</title>

    <!-- 思源黑体 -->
    <link
      href=" https://cdn.jsdelivr.net/npm/cn-fontsource-source-han-sans-sc-vf@1.0.10/font.min.css"
      rel="stylesheet"
    />
    <link
      href="https://at.alicdn.com/wf/webfont/7QwQkQw2v2pA/1v7w8QwQkQw2v2pA.css"
      rel="stylesheet"
    />
    <!-- Fugaz One 字体（Google Fonts） -->
    <link
      href="https://fonts.googleapis.com/css2?family=Fugaz+One&display=swap"
      rel="stylesheet"
    />
    <!-- PingFang SC 不是 Web 字体，只有苹果设备自带 PingFang SC，其他默认显示思源黑体 -->
  </head>

  <body>
    <div id="app" class="app">
      <!--加载动画-->
      <div class="mesh-loader">
        <div class="set-one">
          <div class="circle"></div>
          <div class="circle"></div>
        </div>
        <div class="set-two">
          <div class="circle"></div>
          <div class="circle"></div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      global = globalThis;
    </script>

    <style>
      html,
      body,
      #app {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-family: "Source Han Sans SC VF", "Source Han Serif CN",
          "Noto Sans SC", "PingFang SC", "Microsoft YaHei", sans-serif !important;
        font-style: normal;
        font-weight: 400;
      }

      .mesh-loader {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .mesh-loader .circle {
        position: absolute;
        width: 25px;
        height: 25px;
        margin: -12.5px;
        background: #00918c;
        border-radius: 50%;
        animation: mesh 3s ease-in-out infinite;
        animation: mesh 3s ease-in-out infinite -1.5s;
      }

      .mesh-loader > div .circle:last-child {
        animation-delay: 0s;
      }

      .mesh-loader > div {
        position: absolute;
        top: 50%;
        left: 50%;
      }

      .mesh-loader > div:last-child {
        transform: rotate(90deg);
      }

      @keyframes mesh {
        0% {
          transform: rotate(0);
          transform-origin: 50% -100%;
        }

        50% {
          transform: rotate(360deg);
          transform-origin: 50% -100%;
        }

        50.00001% {
          transform: rotate(0deg);
          transform-origin: 50% 200%;
        }

        100% {
          transform: rotate(360deg);
          transform-origin: 50% 200%;
        }
      }

      @keyframes mesh {
        0% {
          transform: rotate(0);
          transform-origin: 50% -100%;
        }

        50% {
          transform: rotate(360deg);
          transform-origin: 50% -100%;
        }

        50.00001% {
          transform: rotate(0deg);
          transform-origin: 50% 200%;
        }

        100% {
          transform: rotate(360deg);
          transform-origin: 50% 200%;
        }
      }
    </style>
  </body>
</html>
