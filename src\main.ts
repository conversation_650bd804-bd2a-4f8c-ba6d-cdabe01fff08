import { createApp } from "vue";
import App from "./App.vue";
import router from "@/router";
import { setupStore } from "@/store";
import { setupDirective } from "@/directive";
import { usePermissionStoreHook } from "@/store/modules/permission";

import "@/permission";

// 本地SVG图标
import "virtual:svg-icons-register";

// 国际化
import i18n from "@/lang/index";

// 样式
import "element-plus/theme-chalk/dark/css-vars.css";
import "@/styles/index.scss";
import "uno.css";

// import "amfe-flexible/index.js";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import ElementPlus from "element-plus";
import "@/utils/pxrem.js";

const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(ElementPlus, {});
//全局禁用点击弹窗外部关闭
//@ts-ignore
app._context.components.ElDialog.props.closeOnClickModal.default = false;
// 全局注册 自定义指令(directive)
setupDirective(app);
// 全局注册 状态管理(store)
setupStore(app);

// const boot = async () => {
//   const { initRouter } = usePermissionStoreHook();
//   await initRouter();
//   app.use(router).use(i18n).mount("#app");
// };

// boot();
app.use(router).use(i18n).mount("#app");
