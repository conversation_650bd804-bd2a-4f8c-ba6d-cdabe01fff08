html.dark {
  // --menuBg: var(--el-bg-color-overlay);
  // --menuText: #fff;
  // --menuActiveText: var(--el-menu-active-color);
  // --menuHover: rgb(0 0 0 / 20%);
  // --subMenuBg: var(--el-menu-bg-color);
  // --subMenuActiveText: var(--el-menu-active-color);
  // --subMenuHover: rgb(0 0 0 / 20%);
  --menuBg: #ffffff;
  --menuText: #ffffff;
  --menuActiveText: #3B4664
  --menuHover: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
  --subMenuBg: #ffffff;
  --subMenuActiveText: #3B4664
  --subMenuHover: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);

  // wang-editor toolbar
  --w-e-toolbar-bg-color: var(--el-bg-color-overlay);
  --w-e-toolbar-color: var(--el-text-color-regulary);
  --w-e-toolbar-active-bg-color: var(--el-bg-color-page);
  --w-e-toolbar-active-color: var(--el-color-info-dark-2);
  --w-e-toolbar-disabled-color: var(--el-disabled-text-color);
  --w-e-toolbar-border-color: var(--el-color-info-light-8);

  // wang-editor textarea
  --w-e-textarea-bg-color: var(--el-bg-color-overlay);
  --w-e-textarea-color: var(--el-text-color-regulary);
  --w-e-textarea-slight-border-color: var(--el-color-primary);
  --w-e-textarea-slight-bg-color: rgb(var(--el-color-primary-rgb) 0.1);
  --w-e-textarea-selected-border-color: var(--el-color-primary);
  --w-e-textarea-border-color: var(--el-color-info-light-5);

  // // wang-editor modal
  --w-e-modal-button-bg-color: var(--el-button-bg-color);
  --w-e-modal-button-border-color: var(--el-color-info-light-3);

  .navbar {
    background-color: var(--el-bg-color);

    .setting-container .setting-item:hover {
      background: var(--el-fill-color-light);
    }
  }

  .right-panel-btn {
    background-color: var(--el-color-primary-dark);
  }

  .sidebar-container {
    .el-menu-item.is-active .svg-icon {
      fill: var(--el-color-primary);
    }
  }
}
